{"name": "wakari_app", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "db:generate": "drizzle-kit generate", "db:migrate:local": "NODE_ENV=local drizzle-kit migrate", "db:migrate:dev": "NODE_ENV=development drizzle-kit migrate", "db:migrate:prod": "NODE_ENV=production drizzle-kit migrate", "db:studio:local": "NODE_ENV=local drizzle-kit studio", "db:studio:dev": "NODE_ENV=development drizzle-kit studio", "db:studio:prod": "NODE_ENV=production drizzle-kit studio", "db:push:local": "NODE_ENV=local drizzle-kit push", "db:push:dev": "NODE_ENV=development drizzle-kit push", "db:push:prod": "NODE_ENV=production drizzle-kit push", "db:seed": "tsx src/server/seed/questions.ts", "test:env": "node -e \"console.log('NODE_ENV:', process.env.NODE_ENV)\"", "test:env:local": "NODE_ENV=local npm run test:env", "test:env:dev": "NODE_ENV=development npm run test:env", "test:env:prod": "NODE_ENV=production npm run test:env", "test:db": "tsx src/server/db/test-connection.ts", "test:db:local": "NODE_ENV=local tsx -r dotenv/config src/server/db/test-connection.ts dotenv_config_path=.env.local", "test:db:dev": "NODE_ENV=development tsx -r dotenv/config src/server/db/test-connection.ts dotenv_config_path=.env.development", "test:db:prod": "NODE_ENV=production tsx -r dotenv/config src/server/db/test-connection.ts dotenv_config_path=.env.production"}, "dependencies": {"@clerk/nextjs": "^5.7.5", "@hookform/resolvers": "^3.9.1", "@neondatabase/serverless": "^0.10.3", "@pdf-lib/fontkit": "^1.1.1", "@radix-ui/react-accordion": "^1.2.0", "@radix-ui/react-alert-dialog": "^1.1.6", "@radix-ui/react-avatar": "^1.1.0", "@radix-ui/react-checkbox": "^1.1.1", "@radix-ui/react-collapsible": "^1.1.0", "@radix-ui/react-context-menu": "^2.2.2", "@radix-ui/react-dialog": "^1.1.1", "@radix-ui/react-dropdown-menu": "^2.1.1", "@radix-ui/react-icons": "^1.3.0", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-progress": "^1.1.0", "@radix-ui/react-scroll-area": "^1.2.0", "@radix-ui/react-select": "^2.1.1", "@radix-ui/react-separator": "^1.1.0", "@radix-ui/react-slot": "^1.1.2", "@radix-ui/react-switch": "^1.1.2", "@radix-ui/react-tabs": "^1.1.1", "@radix-ui/react-toast": "^1.2.2", "@radix-ui/react-tooltip": "^1.1.2", "@sentry/nextjs": "^8.30.0", "@t3-oss/env-nextjs": "^0.11.1", "@tanstack/react-table": "^8.10.7", "@types/fontkit": "^2.0.7", "@types/pdfkit": "^0.13.5", "@uploadthing/react": "^7.1.3", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "cmdk": "^1.0.0", "date-fns": "^2.30.0", "dotenv": "^16.4.5", "drizzle-orm": "^0.34.1", "embla-carousel-react": "^8.1.6", "fontkit": "^2.0.4", "framer-motion": "^12.4.10", "iconv-lite": "^0.6.3", "lucide-react": "^0.435.0", "motion": "^12.6.2", "next": "^14.2.15", "pdfkit": "^0.14.0", "react": "^18", "react-dom": "^18", "react-hook-form": "^7.53.1", "recharts": "^2.12.7", "sharp": "^0.33.5", "svix": "^1.37.0", "tailwind-merge": "^2.6.0", "tailwindcss-animate": "^1.0.7", "uploadthing": "^7.4.1", "zod": "^3.23.8"}, "devDependencies": {"@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "drizzle-kit": "^0.25.0", "eslint": "^8", "eslint-config-next": "14.2.6", "postcss": "^8", "prettier": "^3.3.3", "prettier-plugin-tailwindcss": "^0.6.6", "tailwindcss": "^3.4.1", "tsx": "^4.19.2", "typescript": "^5"}}