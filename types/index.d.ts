declare interface HeaderBoxProps {
  type?: "title" | "greeting";
  title: string;
  user?: string;
  subtext: string;
}

declare interface SelectOption {
  // Define an interface for the SelectOption type
  value: string;
  label: string;
}

declare interface Step {
  // Define an interface for the Step type
  id: number;
  img?: string;
  tagline: string;
  title: string;
  content: string;
  descr: string;
  helpicon?: LucideIcon;
  helptitle: string;
  helpcontent: string;
  inputlabel: string;
  selectOptions: SelectOption[];
  additionalinputlabel: string;
  additionalselectOptions?: SelectOption[];
  video: string;
}

declare interface OnboardingProgressBarProps {
  // Define an interface for the OnboardingProgressBarProps type
  step: number;
  totalSteps: number;
}

declare interface SideBarProps {
  user: User;
}

declare interface NavBarProps {
  className?: string;
}

declare interface FooterProps {
  className?: string;
}

declare interface AssessmentCardProps {
  title: string;
  description: string;
  complexity: string;
  suitability: string;
  link: string;
  handleCompleteReview: () => Promise<void>;
}

declare interface FAQItem {
  question: string;
  answer: string;
}

declare interface FAQCardProps {
  title: string;
  description: string;
  faqs: FAQItem[];
}

declare interface AssessmentProgressCardProps {
  title: string;
  assignedTo: string;
  progress: number;
  total: number;
  icon?: LucideIcon;
}

declare interface AnswerButton {
  text: string;
  value: string;
  icon?: LucideIcon;
}

declare interface AnswerButtonGroupProps {
  answers?: AnswerButton[];
  onAnswer: (answer: string) => void;
  selectedAnswer?: string | null;
}

declare interface AnswerCommentBoxProps {
  id: string;
  value: string;
  onChange: (id: string, value: string) => void;
}

declare interface Question {
  category: string;
  id: string;
  text: string;
  answer: string;
  comment: string;
  recommendations: Recommendation[];
  questionGuide: QuestionGuide;
}

declare interface QuestionGuide {
  image: string;
  description: string;
  otherOrganizations: string[];
}
declare interface QuestionGuideCardProps {
  questionGuide: QuestionGuide;
}

declare interface AssessmentReviewCardProps {
  question: Question;
  index: number;
  isSelected: boolean;
  onSelect: () => void;
  onQuestionUpdate: (updatedQuestion: Question) => void;
  currentQuestionIndex: number;
  setCurrentQuestionIndex: React.Dispatch<React.SetStateAction<number>>;
  questions: Question[];
  setQuestions: React.Dispatch<React.SetStateAction<Question[]>>;
  currentAnswer: string;
  setCurrentAnswer: React.Dispatch<React.SetStateAction<string>>;
  onAnswerChange: (answer: string) => void;
  onGetHelpClick: () => void;
  onCommentsClick: (questionId?: string) => void;
  openComments: string | null;
  comments: Comment[];
  onCommentChange: (comment: Comment) => void;
}

declare interface CommentsBoxCardProps {
  comments: IComment[];
  onCommentChange: (comment: IComment) => void;
}

declare interface IComment {
  id: string;
  text: string;
  timestamp: string;
  username: string;
  content: string;
  category: string;
  questionNumber: number;
}

interface Assessment {
  id: number;
  title: string;
  type: string;
  date: string;
  tasksCompleted: number;
  totalTasks: number;
  status: "complete" | "incomplete";
}

declare interface AssessmentProps {
  assessment: Assessment;
}

declare interface TaskData {
  id: string;
  title: string;
  startDate: string;
  status: "To Do" | "In Progress" | "Completed" | "Archived";
  category: string;
  dueDate: string;
  assignedTo: string;
}

declare interface ConfirmAssessmentDialogProps {
  link: string;
}

declare type User = {
  $id: string;
  email: string;
  UserId: string;
  firstName: string;
  lastName: string;
} | null;

export interface Question {
  id: string;
  text: string;
  description: string;
  answer?: string;
  comment?: string;
  guideImage?: string;
  organizationId?: string;
  clerkUserId?: string;
  questionGuide?: {
    image: string;
    description: string;
    otherOrganizations?: string[];
  };
  recommendations: {
    text: string;
    priority: string;
    effort: string;
  }[];
}

export interface Recommendation {
  id: string;
  questionId: string;
  text: string;
  priority: string;
  effort?: string;
}

export interface QuestionWithAnswers extends Question {
  answeredByUserId?: string;
  answeredAt?: Date;
}

export interface OnboardingData {
  role?: string;
  teamSize?: string;
  industry?: string;
  locationCount?: string;
  headquarterCity?: string;
  headquarterCountry?: string;
  invites?: string[];
  email?: string;
}

export type ApiResponse<T> = {
  success: boolean;
  data?: T;
  error?: string;
};

export interface Comment {
  id: string;
  username: string;
  timestamp: Date;
  content: string;
  category: string | null;
  questionNumber: string | null;
}
