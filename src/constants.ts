import { ThumbsUp, ThumbsDown, HelpCircle, XCircle } from "lucide-react";
import { type AnswerButton } from "@/types";

export const defaultAnswers: AnswerButton[] = [
  {
    text: "Yes",
    value: "yes",
    icon: ThumbsUp,
  },
  {
    text: "No",
    value: "no",
    icon: ThumbsDown,
  },
  {
    text: "Partially",
    value: "partially",
    icon: HelpCircle,
  },
  {
    text: "Not Applicable",
    value: "na",
    icon: XCircle,
  },
];
