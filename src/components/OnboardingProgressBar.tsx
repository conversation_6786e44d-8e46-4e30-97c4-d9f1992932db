"use client";

import React from "react";
import { Progress } from "./ui/progress";

interface OnboardingProgressBarProps {
  step: number;
  totalSteps: number;
}

const OnboardingProgressBar = ({
  step,
  totalSteps,
}: OnboardingProgressBarProps) => {
  const progressValue = Math.round((step / totalSteps) * 100);

  return (
    <div
      className={`m-auto mb-6 w-[90%] max-w-[450px] ${step === 9 ? "max-w-[600px]" : ""}`}
    >
      <div className="flex flex-row items-center justify-between text-sm leading-9">
        <span className="font-semibold">
          Step {step} of {totalSteps}
        </span>
        <span className="font-semibold">{progressValue}%</span>
      </div>
      <Progress
        value={progressValue}
        className="h-2 rounded-sm bg-[#f7e0d8]"
        aria-label="Onboarding progress"
      />
    </div>
  );
};

export default OnboardingProgressBar;
