import { But<PERSON> } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { UserPlusIcon } from "lucide-react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import { Avatar, AvatarImage, AvatarFallback } from "@/components/ui/avatar";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

const InviteTeamDialog = () => {
  return (
    <Dialog>
      <DialogTrigger asChild>
        <Button
          variant="outline"
          size="sm"
          className="custom-primary-outline border-color-[#d6673e] transition-all duration-300 hover:bg-[#d6673e] hover:text-white"
        >
          <UserPlusIcon className="mr-2 h-4 w-4" /> Invite Team
        </Button>
      </DialogTrigger>
      <DialogContent>
        <DialogHeader className="px-6">
          <DialogTitle className="text-2xl">Invite team members</DialogTitle>
          <DialogDescription>
            You can invite team members to your workspace.
            <br />
            You will still have access.
          </DialogDescription>
        </DialogHeader>
        {/* <Separator /> */}
        <Card className="border-none py-6">
          <CardContent>
            <div className="flex">
              <Label htmlFor="link" className="sr-only">
                Link
              </Label>
              <Input
                id="link"
                value=""
                placeholder="Enter email address"
                readOnly
                className="mr-2"
              />
              <Button
                variant="outline"
                className="shrink-0 border-primary text-primary hover:bg-primary hover:text-white"
              >
                Send Invite
              </Button>
            </div>
            <Separator className="my-4" />
            <div className="space-y-4">
              <h4 className="text-sm font-medium">People with access</h4>
              <div className="grid gap-6">
                <div className="flex items-center justify-between space-x-4">
                  <div className="flex items-center space-x-4">
                    <Avatar>
                      <AvatarImage src="/avatars/03.png" alt="Image" />
                      <AvatarFallback>U1</AvatarFallback>
                    </Avatar>
                    <div>
                      <p className="text-sm font-medium leading-none">
                        Username 1
                      </p>
                      <p className="text-sm text-muted-foreground">
                        <EMAIL>
                      </p>
                    </div>
                  </div>
                  <Select defaultValue="edit">
                    <SelectTrigger
                      className="ml-auto w-[110px]"
                      aria-label="Edit"
                    >
                      <SelectValue placeholder="Select" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="edit">Can edit</SelectItem>
                      <SelectItem value="view">Can view</SelectItem>
                      <Separator />
                      <SelectItem value="remove">Remove access</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="flex items-center justify-between space-x-4">
                  <div className="flex items-center space-x-4">
                    <Avatar>
                      <AvatarImage src="/avatars/05.png" alt="Image" />
                      <AvatarFallback>U2</AvatarFallback>
                    </Avatar>
                    <div>
                      <p className="text-sm font-medium leading-none">
                        Username 2
                      </p>
                      <p className="text-sm text-muted-foreground">
                        <EMAIL>
                      </p>
                    </div>
                  </div>
                  <Select defaultValue="view">
                    <SelectTrigger
                      className="ml-auto w-[110px]"
                      aria-label="Edit"
                    >
                      <SelectValue placeholder="Select" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="edit">Can edit</SelectItem>
                      <SelectItem value="view">Can view</SelectItem>
                      <Separator />
                      <SelectItem value="remove">Remove access</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="flex items-center justify-between space-x-4">
                  <div className="flex items-center space-x-4">
                    <Avatar>
                      <AvatarImage src="/avatars/01.png" alt="Image" />
                      <AvatarFallback>U3</AvatarFallback>
                    </Avatar>
                    <div>
                      <p className="text-sm font-medium leading-none">
                        Username 3
                      </p>
                      <p className="text-sm text-muted-foreground">
                        <EMAIL>
                      </p>
                    </div>
                  </div>
                  <Select defaultValue="view">
                    <SelectTrigger
                      className="ml-auto w-[110px]"
                      aria-label="Edit"
                    >
                      <SelectValue placeholder="Select" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="edit">Can edit</SelectItem>
                      <SelectItem value="view">Can view</SelectItem>
                      <Separator />
                      <SelectItem value="remove">Remove access</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
        <DialogFooter className="px-6">
          <Button type="submit">Save changes</Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default InviteTeamDialog;
