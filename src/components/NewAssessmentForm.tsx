"use client";

import React, { useState, useEffect } from "react";
import Head<PERSON><PERSON><PERSON> from "@/components/HeaderBox";
import AssessmentProgressCardBox from "@/components/AssessmentProgressCardBox";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Share2,
  ChevronLeft,
  ChevronRight,
  CheckCircle,
  FileText,
  ArrowLeft,
  Pencil,
  HelpCircle,
  X,
  Menu,
} from "lucide-react";
import AnswerButtonGroup from "@/components/AnswerButtonGroup";
import AnswerCommentBox from "@/components/AnswerCommentBox";
import QuestionGuideCard from "@/components/QuestionGuideCard";
import { useRouter } from "next/navigation";
import AddCollaboratorsDialog from "@/components/AddCollaboratorsDialog";
import ReAssignQuestionDialog from "@/components/ReAssignQuestionDialog";
import {
  getReviewType,
  saveAnswer,
  updateReviewStatus,
  updateReviewTitle,
  getReviewTitle,
  completeReview,
} from "@/server/actions/reviews";
import { useToast } from "@/components/hooks/use-toast";
import { type QuestionWithCategory, type Comment } from "@/types";
import { LoadingSpinner } from "@/components/LoadingSpinner";
import {
  addComment,
  getComments,
  updateComment as updateCommentTable,
} from "@/server/actions/comments";
import { z } from "zod";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { FeedbackDialog } from "@/components/FeedbackDialog";

interface NewAssessmentFormProps {
  userId: string;
  userName: string;
  initialQuestions: QuestionWithCategory[];
  reviewId: string;
  reviewType: string;
  initialTitle?: string;
}

const formSchema = z.object({
  title: z.string().min(1, "Title is required"),
  type: z.enum(["basic", "comprehensive"]),
  // ... other existing fields
});

const answerValidationSchema = z.object({
  answer: z.enum(["yes", "no", "partially", "na"]),
  comment: z.string().superRefine((comment, ctx) => {
    const answer = (ctx as any).data?.answer;

    if (answer === "partially" || answer === "na") {
      if (!comment.trim()) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message:
            "Comment is required for Partially or Not Applicable answers",
        });
      }
    }
  }),
});

type AnswerFormData = z.infer<typeof answerValidationSchema>;

export default function NewAssessmentForm({
  userId,
  userName,
  initialQuestions,
  reviewId,
  reviewType: initialReviewType,
  initialTitle,
}: NewAssessmentFormProps) {
  const router = useRouter();
  const [questions, setQuestions] =
    useState<QuestionWithCategory[]>(initialQuestions);
  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);
  const { toast } = useToast();
  const currentQuestion = questions[currentQuestionIndex];
  const isLastQuestion = currentQuestionIndex === questions.length - 1;
  const [isCompleting, setIsCompleting] = useState(false);
  const [reviewType, setReviewType] = useState<string>(initialReviewType);
  const [isSubmittingComment, setIsSubmittingComment] = useState(false);
  const [questionComments, setQuestionComments] = useState<
    Record<string, Comment[]>
  >({});
  const [editingCommentId, setEditingCommentId] = useState<string | null>(null);
  const [isEditingComment, setIsEditingComment] = useState(false);
  const [reviewTitle, setReviewTitle] = useState<string>("");
  const [isEditingTitle, setIsEditingTitle] = useState(false);
  const [isSavingTitle, setIsSavingTitle] = useState(false);
  const [currentAnswer, setCurrentAnswer] = useState<string | undefined>();
  const [answerError, setAnswerError] = useState<string | undefined>();
  const [showFeedback, setShowFeedback] = useState(false);
  const [isRedirecting, setIsRedirecting] = useState(false);
  const [isGuideOpen, setIsGuideOpen] = useState(false);

  const form = useForm({
    resolver: zodResolver(formSchema),
    defaultValues: {
      title: initialTitle || "",
      type: reviewType as "basic" | "comprehensive",
    },
    mode: "onChange",
  });

  useEffect(() => {
    const fetchReviewType = async () => {
      try {
        const result = await getReviewType(reviewId);
        if (result.success && result.data) {
          setReviewType(result.data);
        }
      } catch (error) {
        console.error("Error fetching review type:", error);
      }
    };

    if (!initialReviewType) {
      fetchReviewType();
    }
  }, [reviewId, initialReviewType]);

  useEffect(() => {
    const fetchReviewTitle = async () => {
      try {
        const result = await getReviewTitle(reviewId);
        if (result.success && result.data) {
          setReviewTitle(result.data);
          form.setValue("title", result.data);
        }
      } catch (error) {
        console.error("Error fetching review title:", error);
      }
    };

    void fetchReviewTitle();
  }, [reviewId, form]);

  const categoryProgress = React.useMemo(() => {
    const progress: Record<string, { total: number; answered: number }> = {};
    questions.forEach((question) => {
      if (!progress[question.category]) {
        progress[question.category] = { total: 0, answered: 0 };
      }
      progress[question.category].total++;
      if (question.answer) {
        progress[question.category].answered++;
      }
    });
    return progress;
  }, [questions]);

  const handleAnswer = async (answer: "yes" | "no" | "partially" | "na") => {
    if (!userId) return;

    setCurrentAnswer(answer);
    setAnswerError(undefined);

    try {
      const validation = answerValidationSchema.safeParse({
        answer,
        comment: currentQuestion.comment || "",
      });

      if (!validation.success) {
        setAnswerError(validation.error.errors[0]?.message);
        return;
      }

      setQuestions((prevQuestions) =>
        prevQuestions.map((q, index) =>
          index === currentQuestionIndex ? { ...q, answer } : q,
        ),
      );

      try {
        const result = await saveAnswer({
          reviewId,
          questionId: currentQuestion.id,
          answer,
          userId,
          comment: currentQuestion.comment,
        });

        if (!result.success) {
          // Revert on error
          setQuestions((prevQuestions) =>
            prevQuestions.map((q, index) =>
              index === currentQuestionIndex ? { ...q, answer: undefined } : q,
            ),
          );
          toast({
            title: "Error",
            description: result.error || "Failed to save answer",
            variant: "destructive",
          });
        }
      } catch (error) {
        toast({
          title: "Error",
          description: "Failed to save answer",
          variant: "destructive",
        });
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to validate answer",
        variant: "destructive",
      });
    }
  };

  const handleCommentChange = (comment: string) => {
    setQuestions((prevQuestions) =>
      prevQuestions.map((q, index) =>
        index === currentQuestionIndex ? { ...q, comment } : q,
      ),
    );
  };

  const goToPreviousQuestion = () => {
    setCurrentQuestionIndex((prevIndex) => Math.max(0, prevIndex - 1));
  };

  const skipToNextQuestion = async () => {
    if (
      currentQuestion?.answer === "partially" ||
      currentQuestion?.answer === "na"
    ) {
      const validation = answerValidationSchema.safeParse({
        answer: currentQuestion.answer,
        comment: currentQuestion.comment || "",
      });

      if (!validation.success) {
        setAnswerError(validation.error.errors[0]?.message);
        return;
      }
    }

    setCurrentQuestionIndex((prevIndex) =>
      Math.min(questions.length - 1, prevIndex + 1),
    );
    setAnswerError(undefined);
  };

  const handleCompleteReview = async () => {
    console.log("Starting review completion from form...");
    setIsCompleting(true);
    try {
      const result = await completeReview(reviewId);
      if (result.success) {
        router.push(`/reviews/${reviewId}/complete`);
      }
    } catch (error) {
      console.error("Error completing review:", error);
      toast({
        title: "Error",
        description: "Failed to complete review",
        variant: "destructive",
      });
    } finally {
      setIsCompleting(false);
    }
  };

  const areAllQuestionsAnswered = React.useMemo(() => {
    return questions.every((question) => question.answer !== undefined);
  }, [questions]);

  const handleCategoryClick = (category: string) => {
    const firstQuestionIndex = questions.findIndex(
      (q) => q.category === category,
    );
    if (firstQuestionIndex !== -1) {
      setCurrentQuestionIndex(firstQuestionIndex);
    }
  };

  const fetchQuestionComments = async (questionId: string) => {
    try {
      const result = await getComments(reviewId, questionId);
      if (result.success && result.data) {
        const comments = result.data as Comment[];
        setQuestionComments((prev) => {
          const newComments = { ...prev };
          newComments[questionId] = comments;
          return newComments;
        });
      }
    } catch (error) {
      console.error("Failed to fetch comments:", error);
    }
  };

  useEffect(() => {
    if (currentQuestion?.id) {
      void fetchQuestionComments(currentQuestion.id);
    }
  }, [currentQuestion?.id]);

  const handleCommentAdded = (newComment: Comment) => {
    setQuestionComments((prev) => ({
      ...prev,
      [currentQuestion.id]: [...(prev[currentQuestion.id] || []), newComment],
    }));
  };

  const handleEditComment = async (commentId: string, newText: string) => {
    try {
      const commentToEdit = questionComments[currentQuestion?.id]?.find(
        (c) => c.id === commentId,
      );
      if (!commentToEdit) return;

      setIsEditingComment(true);

      // Fix optimistic update type issues
      setQuestionComments((prev) => ({
        ...prev,
        [currentQuestion.id]: prev[currentQuestion.id].map((comment) =>
          comment.id === commentId
            ? { ...comment, text: newText, edited: true }
            : comment,
        ),
      }));

      const result = await updateCommentTable({ commentId, text: newText });

      if (!result.success) {
        // Revert on error
        void fetchQuestionComments(currentQuestion.id);
        toast({
          title: "Error",
          description: "Failed to update comment",
          variant: "destructive",
        });
      }
    } catch (error) {
      void fetchQuestionComments(currentQuestion.id);
      toast({
        title: "Error",
        description: "Failed to update comment",
        variant: "destructive",
      });
    } finally {
      setIsEditingComment(false);
      setEditingCommentId(null);
    }
  };

  const onTitleChange = async (value: string) => {
    try {
      setIsSavingTitle(true);
      const result = await updateReviewTitle(reviewId, value);
      if (result.success) {
        setReviewTitle(value);
        setIsEditingTitle(false);
      } else {
        toast({
          title: "Error",
          description: "Failed to update review title",
          variant: "destructive",
        });
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to update review title",
        variant: "destructive",
      });
    } finally {
      setIsSavingTitle(false);
    }
  };

  useEffect(() => {
    if (!showFeedback && isRedirecting) {
      router.push(`/reviews/${reviewId}/complete`);
    }
  }, [showFeedback, isRedirecting, reviewId, router]);

  const goToNextCategory = () => {
    const categories = Object.keys(categoryProgress);
    const currentCategoryIndex = categories.findIndex(
      (category) => category === currentQuestion?.category,
    );
    if (currentCategoryIndex < categories.length - 1) {
      handleCategoryClick(categories[currentCategoryIndex + 1]);
    }
  };

  const goToPreviousCategory = () => {
    const categories = Object.keys(categoryProgress);
    const currentCategoryIndex = categories.findIndex(
      (category) => category === currentQuestion?.category,
    );
    if (currentCategoryIndex > 0) {
      handleCategoryClick(categories[currentCategoryIndex - 1]);
    }
  };

  const categories = Object.keys(categoryProgress);
  const currentCategoryIndex = categories.findIndex(
    (category) => category === currentQuestion?.category,
  );

  return (
    <div className="w-full space-y-4 sm:space-y-8">
      <div className="mb-8 flex w-full flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
        <div className="flex items-center gap-4">
          <Button
            variant="ghost"
            size="icon"
            onClick={() => router.push("/reviews/manage")}
            className="h-9 w-9"
          >
            <ChevronLeft className="h-6 w-6" />
            <span className="sr-only">Back to reviews</span>
          </Button>
          <div className="flex items-center gap-2">
            <HeaderBox
              type="title"
              title={reviewTitle || "New Review"}
              subtext=""
            />
            <Button
              variant="ghost"
              size="icon"
              onClick={() => setIsEditingTitle(true)}
              className="h-8 w-8"
            >
              <Pencil className="h-4 w-4" />
            </Button>
          </div>
        </div>
        <div className="flex items-center gap-2 pl-[52px] sm:pl-0">
          <span className="custom-navyblue-text text-md flex items-center gap-2 capitalize">
            <b>Review Type</b> : {reviewType || "basic"}
          </span>
        </div>
      </div>

      {isEditingTitle && (
        <div className="mb-4">
          <Form {...form}>
            <form className="space-y-4">
              <FormField
                control={form.control}
                name="title"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Review Title</FormLabel>
                    <FormControl>
                      <div className="flex gap-2">
                        <Input
                          placeholder="Enter a title for your review"
                          {...field}
                          className="max-w-md"
                          onChange={(e) => {
                            field.onChange(e);
                          }}
                        />
                        <Button
                          onClick={() => void onTitleChange(field.value)}
                          type="button"
                          disabled={isSavingTitle}
                        >
                          {isSavingTitle ? (
                            <>
                              <LoadingSpinner className="mr-2 h-4 w-4" />
                              Saving...
                            </>
                          ) : (
                            "Save"
                          )}
                        </Button>
                        <Button
                          variant="outline"
                          onClick={() => setIsEditingTitle(false)}
                          type="button"
                        >
                          Cancel
                        </Button>
                      </div>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </form>
          </Form>
        </div>
      )}

      {/* Mobile Category Navigation */}
      <div className="block lg:hidden">
        <div className="relative flex items-start justify-between gap-2">
          <div className="absolute left-0 top-4 z-10">
            <Button
              onClick={goToPreviousCategory}
              disabled={currentCategoryIndex === 0}
              variant="outline"
              size="icon"
              className="h-10 w-10 shrink-0 rounded-full bg-gray-50"
            >
              <ChevronLeft className="h-5 w-5" />
            </Button>
          </div>

          {currentQuestion?.category && (
            <div className="mx-12 flex w-full items-center">
              <AssessmentProgressCardBox
                title={currentQuestion.category}
                assignedTo={userName}
                progress={categoryProgress[currentQuestion.category].answered}
                total={categoryProgress[currentQuestion.category].total}
                isActive={true}
              />
            </div>
          )}

          <div className="absolute right-0 top-4 z-10">
            <Button
              onClick={goToNextCategory}
              disabled={currentCategoryIndex === categories.length - 1}
              variant="outline"
              size="icon"
              className="h-10 w-10 shrink-0 rounded-full bg-gray-50"
            >
              <ChevronRight className="h-5 w-5" />
            </Button>
          </div>
        </div>
      </div>

      {/* Desktop Category Cards */}
      <div className="hidden grid-cols-2 gap-4 lg:grid lg:grid-cols-4">
        {Object.entries(categoryProgress).map(([category, progress]) => (
          <AssessmentProgressCardBox
            key={category}
            title={category}
            assignedTo={userName}
            progress={progress.answered}
            total={progress.total}
            isActive={currentQuestion?.category === category}
            onClick={() => handleCategoryClick(category)}
          />
        ))}
      </div>

      <div className="mx-auto grid w-full flex-1 auto-rows-max gap-4">
        <div className="flex items-center gap-4">
          <div className="flex justify-between gap-2">
            <Button
              onClick={goToPreviousQuestion}
              disabled={currentQuestionIndex === 0}
              variant="outline"
              size="icon"
              className="h-7 w-7"
            >
              <ChevronLeft className="h-4 w-4" />
              <span className="sr-only">Back</span>
            </Button>
            <h3 className="text-md flex-1 shrink-0 whitespace-nowrap tracking-tight sm:grow-0"></h3>

            <Button
              onClick={skipToNextQuestion}
              disabled={isLastQuestion}
              variant="outline"
              size="icon"
              className="h-7 w-7"
            >
              <ChevronRight className="h-4 w-4" />
              <span className="sr-only">Skip</span>
            </Button>
          </div>

          {/* <div className="hidden items-center gap-2 md:ml-auto md:flex">
            <ReAssignQuestionDialog />
          </div> */}
        </div>

        {/* Main Question and Guide Cards */}
        <div className="grid gap-4 lg:grid-cols-3">
          <div className="grid auto-rows-max items-start gap-4 lg:col-span-2 lg:gap-8">
            <Card className="shadow-xl">
              <CardHeader>
                <CardTitle>
                  <div className="flex items-center justify-between gap-2 text-wrap text-xl font-bold sm:text-2xl">
                    <span className="flex-1">
                      {currentQuestionIndex + 1}. {currentQuestion?.text}
                    </span>
                    <Button
                      variant="outline"
                      size="icon"
                      className="ml-2 flex h-8 w-8 shrink-0 items-center justify-center rounded-full bg-gray-50 lg:hidden"
                      onClick={() => setIsGuideOpen(true)}
                    >
                      <HelpCircle className="h-4 w-4" />
                    </Button>
                  </div>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div>
                  <AnswerButtonGroup
                    onAnswer={handleAnswer}
                    selectedAnswer={currentQuestion?.answer}
                  />
                </div>
                <div className="grid gap-3 pt-12">
                  <AnswerCommentBox
                    value={currentQuestion?.comment || ""}
                    onChange={handleCommentChange}
                    required={
                      currentQuestion?.answer === "partially" ||
                      currentQuestion?.answer === "na"
                    }
                    reviewId={reviewId}
                    questionId={currentQuestion?.id}
                    userId={userId}
                    comments={questionComments[currentQuestion?.id] || []}
                    onCommentAdded={handleCommentAdded}
                    isSubmitting={isSubmittingComment}
                    editingCommentId={editingCommentId}
                    onEditComment={handleEditComment}
                    onStartEdit={setEditingCommentId}
                    isEditingComment={isEditingComment}
                  />
                  {answerError && (
                    <p className="text-sm font-medium text-destructive">
                      {answerError}
                    </p>
                  )}
                </div>
              </CardContent>
              <CardFooter className="flex justify-between">
                <Button
                  onClick={goToPreviousQuestion}
                  disabled={currentQuestionIndex === 0}
                  variant="outline"
                >
                  <ChevronLeft className="mr-2 h-4 w-4" />
                  Previous
                </Button>

                {isLastQuestion ? (
                  <Button
                    onClick={handleCompleteReview}
                    disabled={isCompleting || !areAllQuestionsAnswered}
                  >
                    {isCompleting ? (
                      <>
                        <LoadingSpinner className="mr-2 h-4 w-4" />
                        Completing Review...
                      </>
                    ) : areAllQuestionsAnswered ? (
                      "Complete Review"
                    ) : (
                      "Answer All Questions to Complete"
                    )}
                  </Button>
                ) : (
                  <Button
                    onClick={skipToNextQuestion}
                    disabled={!currentQuestion?.answer}
                  >
                    Next <ChevronRight className="ml-2 h-4 w-4" />
                  </Button>
                )}
              </CardFooter>
            </Card>
          </div>

          {/* Desktop Guide Card */}
          <div className="hidden lg:grid">
            <div className="grid auto-rows-max items-start gap-4 lg:gap-8">
              <QuestionGuideCard
                questionGuide={currentQuestion?.questionGuide}
              />
            </div>
          </div>
        </div>
      </div>

      {/* Mobile Slide-in Guide Panel */}
      <div
        className={`fixed inset-y-0 right-0 z-50 flex h-full w-full max-w-md transform flex-col bg-white shadow-xl transition-transform duration-300 ease-in-out lg:hidden ${
          isGuideOpen ? "translate-x-0" : "translate-x-full"
        }`}
      >
        <div className="border-b p-4">
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-semibold">Question Guide</h3>
            <Button
              variant="ghost"
              size="icon"
              onClick={() => setIsGuideOpen(false)}
            >
              <X className="h-5 w-5" />
            </Button>
          </div>
        </div>
        <div className="flex-1 overflow-y-auto p-4">
          <QuestionGuideCard questionGuide={currentQuestion?.questionGuide} />
        </div>
      </div>

      {/* Backdrop for mobile guide */}
      {isGuideOpen && (
        <div
          className="fixed inset-0 z-40 bg-black/50 lg:hidden"
          onClick={() => setIsGuideOpen(false)}
        />
      )}

      <FeedbackDialog
        open={showFeedback}
        onOpenChange={(open) => {
          setShowFeedback(open);
          if (!open) {
            setIsRedirecting(true);
          }
        }}
        type="post_review"
      />
    </div>
  );
}
