"use client";

import { But<PERSON> } from "@/components/ui/button";
import { FileText, Loader2 } from "lucide-react";
import Link from "next/link";
import { useState } from "react";

interface ViewReportButtonProps {
  reviewId: string;
  className?: string;
}

export function ViewReportButton({
  reviewId,
  className,
}: ViewReportButtonProps) {
  const [isLoading, setIsLoading] = useState(false);

  return (
    <Link
      href={`/reviews/${reviewId}/report`}
      onClick={() => setIsLoading(true)}
    >
      <Button
        size="sm"
        variant="outline"
        className={`hover:text-white" rounded-full border border-[#138213] text-[#138213] transition-all duration-300 hover:bg-[#138213] hover:text-white ${className}`}
        disabled={isLoading}
      >
        {isLoading ? (
          <Loader2 className="mr-2 h-4 w-4 animate-spin" />
        ) : (
          <FileText className="mr-2 h-4 w-4" />
        )}
        View Report
      </Button>
    </Link>
  );
}
