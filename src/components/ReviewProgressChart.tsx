"use client";

import { ChartContainer } from "@/components/ChartContainer";
import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>Axi<PERSON>, YAxi<PERSON> } from "recharts";

interface ReviewProgressChartProps {
  value: number;
  total: number;
  color: string;
}

export function ReviewProgressChart({
  value,
  total,
  color,
}: ReviewProgressChartProps) {
  return (
    <ChartContainer
      config={{
        steps: {
          label: "Steps",
          color,
        },
      }}
      className="aspect-auto h-[10px] w-full"
    >
      <BarChart
        layout="vertical"
        width={100}
        height={12}
        data={[{ value, remaining: total - value }]}
      >
        <Bar
          dataKey="value"
          fill="var(--color-steps)"
          radius={4}
          stackId="a"
          barSize={12}
        />
        <Bar
          dataKey="remaining"
          fill="hsl(var(--muted-foreground) / 0.2)"
          radius={4}
          stackId="a"
          barSize={12}
        />
        <XAxis type="number" domain={[0, total]} hide />
        <YAxis type="category" hide />
      </BarChart>
    </ChartContainer>
  );
}
