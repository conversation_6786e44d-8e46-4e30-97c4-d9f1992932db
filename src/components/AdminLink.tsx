"use client";

import { useEffect, useState } from "react";
import Link from "next/link";
import { Shield } from "lucide-react";
import { cn } from "@/lib/utils";
import { checkUserIsAdmin } from "@/app/(auth)/actions/auth";
import { usePathname } from "next/navigation";
import { Separator } from "@/components/ui/separator";

export function AdminLink({ userId }: { userId: string }) {
  const [isAdmin, setIsAdmin] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const pathname = usePathname();

  // Check if current path is an admin route
  const isActive = pathname.startsWith("/admin");

  useEffect(() => {
    async function checkAdmin() {
      try {
        const isAdmin = await checkUserIsAdmin(userId);
        setIsAdmin(isAdmin);
      } catch (error) {
        console.error("Error checking admin status:", error);
      } finally {
        setIsLoading(false);
      }
    }
    checkAdmin();
  }, [userId]);

  if (isLoading || !isAdmin) return null;

  return (
    <>
      <Separator />
      <Link
        href="/admin"
        className={cn(
          "custom-navyblue-text flex items-center gap-3 rounded-lg px-3 py-2 text-base transition-all hover:bg-primary/10 hover:text-primary",
          {
            "bg-primary text-white hover:bg-primary hover:text-white": isActive,
          },
        )}
      >
        <Shield size={24} />
        Admin Dashboard
      </Link>
    </>
  );
}
