"use client";

import { useState, useEffect } from "react";
import AssessmentReviewCard from "./AssessmentReviewCard";
import QuestionGuideCard from "./QuestionGuideCard";
import { type QuestionWithCategory } from "@/types";
import { updateReviewAnswerAndTasks } from "@/server/actions/reviews";
import { sortQuestionsByCategory } from "@/lib/utils/sort";
import { Button } from "@/components/ui/button";
import {
  ChevronLeft,
  ChevronRight,
  X,
  CheckCircle,
  XCircle,
  MinusCircle,
  HelpCircle,
} from "lucide-react";
import AssessmentProgressCardBox from "@/components/AssessmentProgressCardBox";
import { Card, CardHeader, CardContent } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { useToast } from "@/components/hooks/use-toast";

interface ReviewFeedbackSectionProps {
  answeredQuestions: QuestionWithCategory[];
  reviewId: string;
  userId: string;
  firstName: string;
  lastName: string;
}

type UpdateResult = {
  success: boolean;
  error?: string;
};

export function ReviewFeedbackSection({
  answeredQuestions: initialQuestions,
  reviewId,
  userId,
  firstName,
  lastName,
}: ReviewFeedbackSectionProps) {
  const { toast } = useToast();

  // Local state for optimistic updates
  const [questions, setQuestions] = useState(initialQuestions);
  const sortedQuestions = sortQuestionsByCategory(questions);

  const [currentGuide, setCurrentGuide] = useState(
    sortedQuestions[0]?.questionGuide,
  );
  const [selectedQuestionId, setSelectedQuestionId] = useState<string | null>(
    sortedQuestions[0]?.id ?? null,
  );
  const [isGuideOpen, setIsGuideOpen] = useState(false);
  const [currentCategoryIndex, setCurrentCategoryIndex] = useState(0);
  const [isDesktop, setIsDesktop] = useState(false);
  const [pendingUpdates, setPendingUpdates] = useState<Set<string>>(new Set());

  // Sync local state with prop changes (e.g., from server revalidation)
  useEffect(() => {
    setQuestions(initialQuestions);
  }, [initialQuestions]);

  useEffect(() => {
    const handleResize = () => {
      setIsDesktop(window.innerWidth >= 1024);
    };

    handleResize();
    window.addEventListener("resize", handleResize);

    return () => {
      window.removeEventListener("resize", handleResize);
    };
  }, []);

  const categories = Array.from(
    new Set(sortedQuestions.map((q) => q.category)),
  );

  const currentCategory = categories[currentCategoryIndex];
  const categoryQuestions = sortedQuestions.filter(
    (q) => q.category === currentCategory,
  );

  const goToNextCategory = () => {
    if (currentCategoryIndex < categories.length - 1) {
      setCurrentCategoryIndex(currentCategoryIndex + 1);
      // Update selected question to first question of new category
      const nextCategory = categories[currentCategoryIndex + 1];
      const firstQuestionOfCategory = sortedQuestions.find(
        (q) => q.category === nextCategory,
      );
      if (firstQuestionOfCategory) {
        setSelectedQuestionId(firstQuestionOfCategory.id);
        setCurrentGuide(firstQuestionOfCategory.questionGuide);
      }
    }
  };

  const goToPreviousCategory = () => {
    if (currentCategoryIndex > 0) {
      setCurrentCategoryIndex(currentCategoryIndex - 1);
      // Update selected question to first question of new category
      const prevCategory = categories[currentCategoryIndex - 1];
      const firstQuestionOfCategory = sortedQuestions.find(
        (q) => q.category === prevCategory,
      );
      if (firstQuestionOfCategory) {
        setSelectedQuestionId(firstQuestionOfCategory.id);
        setCurrentGuide(firstQuestionOfCategory.questionGuide);
      }
    }
  };

  const handleCardSelect = (
    questionId: string,
    guide: { description: string; image: string },
  ) => {
    if (selectedQuestionId === questionId) {
      setSelectedQuestionId(null);
      setCurrentGuide(sortedQuestions[0]?.questionGuide);
    } else {
      setSelectedQuestionId(questionId);
      setCurrentGuide(guide);
    }
  };

  // Optimistically update question answer in local state
  const updateQuestionOptimistically = (
    questionId: string,
    newAnswer: "yes" | "no" | "partially" | "na",
    comment?: string,
  ) => {
    setQuestions((prevQuestions) =>
      prevQuestions.map((question) =>
        question.id === questionId
          ? {
              ...question,
              answer: newAnswer,
              comment: comment || question.comment,
            }
          : question,
      ),
    );
  };

  // Revert optimistic update on failure
  const revertOptimisticUpdate = (
    questionId: string,
    originalAnswer: "yes" | "no" | "partially" | "na" | undefined,
    originalComment?: string,
  ) => {
    setQuestions((prevQuestions) =>
      prevQuestions.map((question) =>
        question.id === questionId
          ? { ...question, answer: originalAnswer, comment: originalComment }
          : question,
      ),
    );
  };

  const handleAnswerUpdate = async (
    questionId: string,
    newAnswer: "yes" | "no" | "partially" | "na",
    comment?: string,
  ) => {
    // Find the original question for potential rollback
    const originalQuestion = questions.find((q) => q.id === questionId);
    const originalAnswer = originalQuestion?.answer;
    const originalComment = originalQuestion?.comment;

    // Track pending update
    setPendingUpdates((prev) => new Set(prev).add(questionId));

    // Optimistically update the UI immediately
    updateQuestionOptimistically(questionId, newAnswer, comment);

    try {
      const result = (await updateReviewAnswerAndTasks({
        reviewId,
        questionId,
        answer: newAnswer,
        comment,
        userId,
      })) as UpdateResult;

      if (!result.success && result.error) {
        // Revert the optimistic update on failure
        revertOptimisticUpdate(questionId, originalAnswer, originalComment);
        toast({
          title: "Update Failed",
          description:
            result.error || "Failed to save your answer. Please try again.",
          variant: "destructive",
        });
        throw new Error(result.error);
      } else {
        // Success: Ensure the optimistic update is confirmed with server data
        // The optimistic update should already match, but this ensures consistency
        updateQuestionOptimistically(questionId, newAnswer, comment);
      }
    } catch (error) {
      console.error("Failed to update answer and tasks:", error);
      // Revert the optimistic update on error
      revertOptimisticUpdate(questionId, originalAnswer, originalComment);
      // Show error toast
      toast({
        title: "Update Failed",
        description: "Failed to save your answer. Please try again.",
        variant: "destructive",
      });
    } finally {
      // Remove from pending updates
      setPendingUpdates((prev) => {
        const newSet = new Set(prev);
        newSet.delete(questionId);
        return newSet;
      });
    }
  };

  return (
    <>
      {/* Category Overview Section */}
      <div>
        {/* Mobile Category Navigation */}
        <div className="block lg:hidden">
          <div className="relative mb-4 flex items-start justify-between gap-2">
            <div className="absolute left-0 top-4 z-10">
              <Button
                onClick={goToPreviousCategory}
                disabled={currentCategoryIndex === 0}
                variant="outline"
                size="icon"
                className="h-10 w-10 shrink-0 rounded-full bg-gray-50"
              >
                <ChevronLeft className="h-5 w-5" />
              </Button>
            </div>

            {currentCategory && (
              <div className="mx-12 flex w-full items-center">
                <Card className="w-full border-none shadow-none">
                  <CardHeader className="flex flex-row items-center space-x-2 rounded-lg border p-2 shadow-none">
                    <div className="rounded-md bg-[#f7e0d8] p-2">
                      {categoryQuestions.filter((q) => q.answer === "yes")
                        .length >
                      categoryQuestions.filter((q) => q.answer === "no")
                        .length ? (
                        <CheckCircle className="h-6 w-6 text-primary" />
                      ) : (
                        <XCircle className="h-6 w-6 text-primary" />
                      )}
                    </div>
                    <div>
                      <h2 className="text-sm font-bold">{currentCategory}</h2>
                      <p className="text-sm text-gray-500">
                        {categoryQuestions.length} questions
                      </p>
                    </div>
                  </CardHeader>
                  <CardContent className="px-0 py-1">
                    <div className="space-y-4">
                      <div className="flex items-center justify-between">
                        <div className="flex w-3/4 items-center gap-2">
                          <CheckCircle className="h-4 w-4 text-green-600" />
                          <Progress
                            value={
                              (categoryQuestions.filter(
                                (q) => q.answer === "yes",
                              ).length /
                                categoryQuestions.length) *
                              100
                            }
                            className="h-2"
                            indicatorClassName="bg-green-600"
                          />
                        </div>
                        <span className="text-sm font-bold text-green-600">
                          {
                            categoryQuestions.filter((q) => q.answer === "yes")
                              .length
                          }
                          /{categoryQuestions.length}
                        </span>
                      </div>

                      <div className="flex items-center justify-between">
                        <div className="flex w-3/4 items-center gap-2">
                          <XCircle className="h-4 w-4 text-red-600" />
                          <Progress
                            value={
                              (categoryQuestions.filter(
                                (q) => q.answer === "no",
                              ).length /
                                categoryQuestions.length) *
                              100
                            }
                            className="h-2"
                            indicatorClassName="bg-red-600"
                          />
                        </div>
                        <span className="text-sm font-bold text-red-600">
                          {
                            categoryQuestions.filter((q) => q.answer === "no")
                              .length
                          }
                          /{categoryQuestions.length}
                        </span>
                      </div>

                      <div className="flex items-center justify-between">
                        <div className="flex w-3/4 items-center gap-2">
                          <MinusCircle className="h-4 w-4 text-yellow-600" />
                          <Progress
                            value={
                              (categoryQuestions.filter(
                                (q) => q.answer === "partially",
                              ).length /
                                categoryQuestions.length) *
                              100
                            }
                            className="h-2"
                            indicatorClassName="bg-yellow-600"
                          />
                        </div>
                        <span className="text-sm font-bold text-yellow-600">
                          {
                            categoryQuestions.filter(
                              (q) => q.answer === "partially",
                            ).length
                          }
                          /{categoryQuestions.length}
                        </span>
                      </div>

                      <div className="flex items-center justify-between">
                        <div className="flex w-3/4 items-center gap-2">
                          <HelpCircle className="h-4 w-4 text-gray-600" />
                          <Progress
                            value={
                              (categoryQuestions.filter(
                                (q) => q.answer === "na",
                              ).length /
                                categoryQuestions.length) *
                              100
                            }
                            className="h-2"
                            indicatorClassName="bg-gray-600"
                          />
                        </div>
                        <span className="text-sm font-bold text-gray-600">
                          {
                            categoryQuestions.filter((q) => q.answer === "na")
                              .length
                          }
                          /{categoryQuestions.length}
                        </span>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>
            )}

            <div className="absolute right-0 top-4 z-10">
              <Button
                onClick={goToNextCategory}
                disabled={currentCategoryIndex === categories.length - 1}
                variant="outline"
                size="icon"
                className="h-10 w-10 shrink-0 rounded-full bg-gray-50"
              >
                <ChevronRight className="h-5 w-5" />
              </Button>
            </div>
          </div>
        </div>

        {/* Desktop Category Cards Grid */}
        <div className="mb-8 hidden gap-2 md:grid md:grid-cols-2 lg:grid-cols-4">
          {Object.entries(
            sortedQuestions.reduce(
              (acc, question) => {
                if (!acc[question.category]) {
                  acc[question.category] = {
                    total: 0,
                    yes: 0,
                    no: 0,
                    partially: 0,
                    na: 0,
                  };
                }
                acc[question.category].total++;
                if (question.answer === "yes") acc[question.category].yes++;
                if (question.answer === "no") acc[question.category].no++;
                if (question.answer === "partially")
                  acc[question.category].partially++;
                if (question.answer === "na") acc[question.category].na++;
                return acc;
              },
              {} as Record<
                string,
                {
                  total: number;
                  yes: number;
                  no: number;
                  partially: number;
                  na: number;
                }
              >,
            ),
          ).map(([category, scores]) => (
            <Card key={category} className="w-full border-none shadow-none">
              <CardHeader className="flex flex-row items-center space-x-2 rounded-lg border p-2 shadow-none">
                <div className="rounded-md bg-[#f7e0d8] p-2">
                  {scores.yes > scores.no ? (
                    <CheckCircle className="h-6 w-6 text-primary" />
                  ) : (
                    <XCircle className="h-6 w-6 text-primary" />
                  )}
                </div>
                <div>
                  <h2 className="text-sm font-bold">{category}</h2>
                  <p className="text-sm text-gray-500">
                    {scores.total} questions
                  </p>
                </div>
              </CardHeader>
              <CardContent className="px-0 py-1">
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div className="flex w-3/4 items-center gap-2">
                      <CheckCircle className="h-4 w-4 text-green-600" />
                      <Progress
                        value={(scores.yes / scores.total) * 100}
                        className="h-2"
                        indicatorClassName="bg-green-600"
                      />
                    </div>
                    <span className="text-sm font-bold text-green-600">
                      {scores.yes}/{scores.total}
                    </span>
                  </div>

                  <div className="flex items-center justify-between">
                    <div className="flex w-3/4 items-center gap-2">
                      <XCircle className="h-4 w-4 text-red-600" />
                      <Progress
                        value={(scores.no / scores.total) * 100}
                        className="h-2"
                        indicatorClassName="bg-red-600"
                      />
                    </div>
                    <span className="text-sm font-bold text-red-600">
                      {scores.no}/{scores.total}
                    </span>
                  </div>

                  <div className="flex items-center justify-between">
                    <div className="flex w-3/4 items-center gap-2">
                      <MinusCircle className="h-4 w-4 text-yellow-600" />
                      <Progress
                        value={(scores.partially / scores.total) * 100}
                        className="h-2"
                        indicatorClassName="bg-yellow-600"
                      />
                    </div>
                    <span className="text-sm font-bold text-yellow-600">
                      {scores.partially}/{scores.total}
                    </span>
                  </div>

                  <div className="flex items-center justify-between">
                    <div className="flex w-3/4 items-center gap-2">
                      <HelpCircle className="h-4 w-4 text-gray-600" />
                      <Progress
                        value={(scores.na / scores.total) * 100}
                        className="h-2"
                        indicatorClassName="bg-gray-600"
                      />
                    </div>
                    <span className="text-sm font-bold text-gray-600">
                      {scores.na}/{scores.total}
                    </span>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>

      {/* Questions and Guide Section */}
      <div className="grid gap-6 lg:grid-cols-3">
        <div className="col-span-full grid max-h-[calc(100vh-12rem)] auto-rows-max items-start gap-4 overflow-y-auto pr-4 lg:col-span-2 lg:gap-8">
          {(isDesktop ? sortedQuestions : categoryQuestions).map(
            (question, index) => (
              <AssessmentReviewCard
                key={question.id}
                question={question}
                reviewId={reviewId}
                userId={userId}
                firstName={firstName}
                lastName={lastName}
                isSelected={selectedQuestionId === question.id}
                onGuideChange={(guide) => {
                  handleCardSelect(question.id, guide);
                  if (!isDesktop) {
                    setIsGuideOpen(true);
                  }
                }}
                questionNumber={index + 1}
                onAnswerUpdate={handleAnswerUpdate}
                isUpdating={pendingUpdates.has(question.id)}
              />
            ),
          )}
        </div>
        <div
          id="question-guide"
          className="sticky top-4 hidden h-fit auto-rows-max items-start gap-4 lg:grid lg:gap-8"
        >
          <QuestionGuideCard questionGuide={currentGuide} />
        </div>
      </div>

      {/* Mobile Slide-in Guide Panel */}
      <div
        className={`fixed inset-y-0 right-0 z-50 flex h-full w-full max-w-md transform flex-col bg-white shadow-xl transition-transform duration-300 ease-in-out lg:hidden ${
          isGuideOpen ? "translate-x-0" : "translate-x-full"
        }`}
      >
        <div className="border-b p-4">
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-semibold">Question Guide</h3>
            <Button
              variant="ghost"
              size="icon"
              onClick={() => setIsGuideOpen(false)}
            >
              <X className="h-5 w-5" />
            </Button>
          </div>
        </div>
        <div className="flex-1 overflow-y-auto p-4">
          <QuestionGuideCard questionGuide={currentGuide} />
        </div>
      </div>

      {/* Backdrop for mobile guide */}
      {isGuideOpen && (
        <div
          className="fixed inset-0 z-40 bg-black/50 lg:hidden"
          onClick={() => setIsGuideOpen(false)}
        />
      )}
    </>
  );
}
