"use client";

import React, { useState } from "react";
import {
  <PERSON>,
  <PERSON><PERSON><PERSON>er,
  CardT<PERSON>le,
  CardDescription,
  CardContent,
  CardFooter,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Separator } from "@/components/ui/separator";
import { <PERSON><PERSON><PERSON><PERSON>, ListCheck, Loader2 } from "lucide-react";
import { useRouter } from "next/navigation";

interface AssessmentCardProps {
  title: string;
  description: string;
  complexity: string;
  suitability: string;
  link: string;
  disabled?: boolean;
}

const AssessmentCard: React.FC<AssessmentCardProps> = ({
  title,
  description,
  complexity,
  suitability,
  link,
  disabled,
}) => {
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(false);

  const type = title.toLowerCase().includes("basic")
    ? "basic"
    : "comprehensive";

  const handleClick = async () => {
    if (disabled) return;

    setIsLoading(true);
    try {
      await router.push(`/reviews/new?type=${type}`);
    } catch (error) {
      console.error("Navigation failed:", error);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Card className="custom-primary-outline flex h-full flex-col">
      <CardHeader className="flex flex-row items-center">
        <div className="grid gap-2">
          <CardTitle className="custom-navyblue-text text-lg font-bold">
            {title}
          </CardTitle>
          <CardDescription className="custom-navyblue-text text-sm">
            {description}
          </CardDescription>
        </div>
      </CardHeader>
      <CardContent className="flex-grow">
        <div className="my-4">
          <span className="flex items-center text-sm">
            <CircleCheck className="custom-navyblue-text mr-2 h-4 w-4" />
            <span className="font-semibold">Complexity</span>
          </span>
          <span className="ml-6 text-sm text-gray-600">{complexity}</span>
          <Separator className="my-2" />
        </div>

        <div className="my-4">
          <span className="flex items-center text-sm">
            <CircleCheck className="custom-navyblue-text mr-2 h-4 w-4" />
            <span className="font-semibold">Best suited for</span>
          </span>
          <span className="ml-6 text-sm text-gray-600">{suitability}</span>
          <Separator className="my-2" />
        </div>
      </CardContent>
      <CardFooter className="mt-auto">
        <Button
          className={`w-full text-white ${
            disabled
              ? "cursor-not-allowed bg-gray-300 hover:bg-gray-300"
              : "bg-primary hover:bg-primary/90"
          }`}
          onClick={handleClick}
          disabled={disabled || isLoading}
        >
          {isLoading ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              Loading...
            </>
          ) : (
            <>
              <ListCheck className="mr-2 h-4 w-4" />
              Select Review
            </>
          )}
        </Button>
      </CardFooter>
    </Card>
  );
};

export default AssessmentCard;
