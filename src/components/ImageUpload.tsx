"use client";

import Image from "next/image";
import { generateUploadDropzone } from "@uploadthing/react";
import type { OurFileRouter } from "@/app/api/uploadthing/core";
import { useToast } from "@/components/hooks/use-toast";
import { useState } from "react";
import { Loader2 } from "lucide-react";

const UploadDropzone = generateUploadDropzone<OurFileRouter>();

interface ImageUploadProps {
  currentImage?: string | null;
  onUpload: (url: string) => void;
  disabled?: boolean;
}

export function ImageUpload({
  currentImage,
  onUpload,
  disabled,
}: ImageUploadProps) {
  const { toast } = useToast();
  const [isUploading, setIsUploading] = useState(false);
  const [previewUrl, setPreviewUrl] = useState<string | null>(
    currentImage || null,
  );

  const showLocalPreview = (file: File) => {
    const reader = new FileReader();
    reader.onload = (e) => {
      setPreviewUrl(e.target?.result as string);
    };
    reader.readAsDataURL(file);
  };

  return (
    <div className="space-y-4">
      {(previewUrl || isUploading) && (
        <div className="relative aspect-video w-full overflow-hidden rounded-lg">
          {isUploading && (
            <div className="absolute inset-0 flex items-center justify-center bg-background/80">
              <Loader2 className="h-8 w-8 animate-spin text-primary" />
            </div>
          )}
          {previewUrl && (
            <Image
              src={previewUrl}
              alt="Guide image"
              fill
              className="object-contain"
            />
          )}
        </div>
      )}
      <UploadDropzone
        endpoint="imageUploader"
        onBeforeUploadBegin={(files) => {
          setIsUploading(true);
          if (files[0]) {
            showLocalPreview(files[0]);
          }
          onUpload(previewUrl || "");
          return files;
        }}
        onClientUploadComplete={(res) => {
          console.log("Upload completed", res);
          if (res?.[0]) {
            const imageUrl = res[0].url;
            console.log("Image URL:", imageUrl);
            setPreviewUrl(imageUrl);
            onUpload(imageUrl);
            toast({
              title: "Success",
              description: "Image uploaded successfully",
            });
          }
          setIsUploading(false);
        }}
        onUploadError={(error: Error) => {
          console.error("Upload error:", error);
          toast({
            title: "Error",
            description: error.message,
            variant: "destructive",
          });
          setIsUploading(false);
          onUpload(currentImage || "");
        }}
        disabled={disabled || isUploading}
        className="ut-button:bg-primary ut-button:hover:bg-primary/90 ut-button:text-primary-foreground ut-allowed-content:text-muted-foreground ut-label:text-foreground border-2 border-dashed border-primary/20"
        content={{
          label: isUploading
            ? "Uploading..."
            : "Drop your image here or click to browse",
          allowedContent: "Images up to 4MB",
        }}
      />
    </div>
  );
}
