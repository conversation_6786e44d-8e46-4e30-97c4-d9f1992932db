"use client";

import { MessageSquarePlus } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { FeedbackDialog } from "@/components/FeedbackDialog";
import { useState } from "react";

export function FeedbackButton() {
  const [showFeedback, setShowFeedback] = useState(false);

  return (
    <>
      <Button
        variant="ghost"
        size="sm"
        className="w-full gap-2 rounded-full bg-[#0a2245] font-semibold text-white hover:bg-[#0a2245]/80 hover:text-white"
        onClick={() => setShowFeedback(true)}
      >
        <MessageSquarePlus className="h-4 w-4" />
        <span className="hidden md:inline">Submit Feedback</span>
      </Button>

      <FeedbackDialog
        open={showFeedback}
        onOpenChange={setShowFeedback}
        type="voluntary"
      />
    </>
  );
}
