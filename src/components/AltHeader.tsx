"use client";

import React, { useState, useRef, useEffect } from "react";
import Link from "next/link";
import { But<PERSON> } from "@/components/ui/button";
import Image from "next/image";
import { useAuth, SignOutButton } from "@clerk/nextjs";
import { ArrowR<PERSON>, Menu, X, ChevronDown } from "lucide-react";
import { usePathname } from "next/navigation";
import { useActiveSection } from "@/hooks/useActiveSection";
import { cn } from "@/lib/utils";

export const AltHeader = () => {
  const { userId } = useAuth();
  const pathname = usePathname();
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const [whatWeDoOpen, setWhatWeDoOpen] = useState(false);
  const [teamOpen, setTeamOpen] = useState(false);

  const whatWeDoRef = useRef<HTMLDivElement>(null);
  const teamRef = useRef<HTMLDivElement>(null);

  // Define section IDs for tracking active section
  const sectionIds = [
    "home",
    "our-story",
    "features",
    "team",
    "founder",
    "contact",
  ];
  const activeSection = useActiveSection(sectionIds);

  // Close dropdowns when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        whatWeDoRef.current &&
        !whatWeDoRef.current.contains(event.target as Node)
      ) {
        setWhatWeDoOpen(false);
      }
      if (teamRef.current && !teamRef.current.contains(event.target as Node)) {
        setTeamOpen(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  // Function to determine if a link is active
  const isActive = (href: string) => {
    // For the home link
    if (href === "/" || href === "#home") {
      return pathname === "/" && (activeSection === "home" || !activeSection);
    }

    // For section links, they should only be active on the home page
    if (href.startsWith("#")) {
      // Only check for active sections when on the home page
      if (pathname === "/") {
        const sectionId = href.substring(1);
        return activeSection === sectionId;
      }
      return false;
    }

    // For other page links (like /training, /consulting)
    return pathname === href;
  };

  // Function to get the correct href for section links
  const getSectionHref = (sectionId: string) => {
    // If we're already on the home page, just use the fragment identifier
    if (pathname === "/") {
      return `#${sectionId}`;
    }
    // Otherwise, navigate to the home page with the fragment identifier
    return `/#${sectionId}`;
  };

  return (
    <header className="sticky top-0 z-50 bg-white shadow-sm backdrop-blur-sm">
      <div className="container mx-auto flex items-center justify-between px-4 py-5">
        <div className="flex items-center">
          <Link href="/" className="mr-10 flex items-center" prefetch={false}>
            <Image
              src="/assets/wakari-logo.png"
              alt="Wakari Logo"
              width={150}
              height={150}
            />
          </Link>
          <nav className="hidden space-x-8 md:flex">
            <Link
              href={getSectionHref("home")}
              className={cn(
                "text-sm font-medium text-gray-600 hover:text-[#FF5C28]",
                isActive("#home") && "font-semibold text-[#FF5C28]",
              )}
            >
              Home
            </Link>
            <Link
              href={getSectionHref("our-story")}
              className={cn(
                "text-sm font-medium text-gray-600 hover:text-[#FF5C28]",
                isActive("#our-story") && "font-semibold text-[#FF5C28]",
              )}
            >
              Our Story
            </Link>

            {/* What We Do Dropdown */}
            <div ref={whatWeDoRef} className="relative">
              <button
                onClick={() => setWhatWeDoOpen(!whatWeDoOpen)}
                className={cn(
                  "flex items-center text-sm font-medium text-gray-600 hover:text-[#FF5C28]",
                  (isActive("#features") ||
                    pathname === "/training" ||
                    pathname === "/consulting") &&
                    "font-semibold text-[#FF5C28]",
                )}
              >
                What We Do
                <ChevronDown className="ml-1 h-4 w-4" />
              </button>
              {whatWeDoOpen && (
                <div className="absolute left-0 top-full z-50 mt-1 w-48 rounded-md border border-gray-200 bg-white py-2 shadow-lg">
                  <Link
                    href={getSectionHref("features")}
                    className={cn(
                      "block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 hover:text-[#FF5C28]",
                      isActive("#features") &&
                        "bg-gray-100 font-semibold text-[#FF5C28]",
                    )}
                    onClick={() => setWhatWeDoOpen(false)}
                  >
                    Features
                  </Link>
                  <Link
                    href="/training"
                    className={cn(
                      "block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 hover:text-[#FF5C28]",
                      pathname === "/training" &&
                        "bg-gray-100 font-semibold text-[#FF5C28]",
                    )}
                    onClick={() => setWhatWeDoOpen(false)}
                  >
                    Training
                  </Link>
                  <Link
                    href="/consulting"
                    className={cn(
                      "block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 hover:text-[#FF5C28]",
                      pathname === "/consulting" &&
                        "bg-gray-100 font-semibold text-[#FF5C28]",
                    )}
                    onClick={() => setWhatWeDoOpen(false)}
                  >
                    Consulting
                  </Link>
                </div>
              )}
            </div>

            {/* Team Dropdown */}
            <div ref={teamRef} className="relative">
              <button
                onClick={() => setTeamOpen(!teamOpen)}
                className={cn(
                  "flex items-center text-sm font-medium text-gray-600 hover:text-[#FF5C28]",
                  (isActive("#team") || isActive("#founder")) &&
                    "font-semibold text-[#FF5C28]",
                )}
              >
                Team
                <ChevronDown className="ml-1 h-4 w-4" />
              </button>
              {teamOpen && (
                <div className="absolute left-0 top-full z-50 mt-1 w-48 rounded-md border border-gray-200 bg-white py-2 shadow-lg">
                  <Link
                    href={getSectionHref("founder")}
                    className={cn(
                      "block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 hover:text-[#FF5C28]",
                      isActive("#founder") &&
                        "bg-gray-100 font-semibold text-[#FF5C28]",
                    )}
                    onClick={() => setTeamOpen(false)}
                  >
                    Founder's Story
                  </Link>
                  <Link
                    href={getSectionHref("team")}
                    className={cn(
                      "block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 hover:text-[#FF5C28]",
                      isActive("#team") &&
                        "bg-gray-100 font-semibold text-[#FF5C28]",
                    )}
                    onClick={() => setTeamOpen(false)}
                  >
                    Our People
                  </Link>
                </div>
              )}
            </div>

            <Link
              href={getSectionHref("contact")}
              className={cn(
                "text-sm font-medium text-gray-600 hover:text-[#FF5C28]",
                isActive("#contact") && "font-semibold text-[#FF5C28]",
              )}
            >
              Contact
            </Link>
          </nav>
        </div>
        <div className="flex items-center gap-2">
          {userId ? (
            <>
              <SignOutButton>
                <Button
                  size="sm"
                  variant="outline"
                  className="hidden justify-self-end px-2 py-1 text-sm md:block"
                >
                  Sign Out
                </Button>
              </SignOutButton>
              <Link href="/dashboard/home">
                <Button
                  size="sm"
                  className="hidden items-center whitespace-nowrap px-4 py-1 text-sm md:inline-flex"
                >
                  Dashboard{" "}
                  <ArrowRight className="ml-2 h-4 w-4 flex-shrink-0" />
                </Button>
              </Link>
            </>
          ) : (
            <>
              <Link href="/sign-in">
                <Button
                  size="sm"
                  variant="outline"
                  className="hidden justify-self-end px-2 py-1 text-sm md:block"
                >
                  Sign in
                </Button>
              </Link>
              <Link href="/sign-up">
                <Button
                  size="sm"
                  className="hidden justify-self-end px-2 py-1 text-sm md:block"
                >
                  Sign Up
                </Button>
              </Link>
            </>
          )}
          <button
            onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
            className="ml-4 md:hidden"
          >
            {isMobileMenuOpen ? (
              <X className="h-6 w-6" />
            ) : (
              <Menu className="h-6 w-6" />
            )}
          </button>
        </div>
      </div>

      {/* Mobile Menu */}
      {isMobileMenuOpen && (
        <div className="fixed inset-0 top-[72px] z-50 bg-white px-4 py-6 md:hidden">
          <nav className="flex flex-col space-y-4 bg-white p-6">
            <Link
              href={getSectionHref("home")}
              className="text-lg font-medium text-gray-600 hover:text-[#FF5C28]"
              onClick={() => setIsMobileMenuOpen(false)}
            >
              Home
            </Link>
            <Link
              href={getSectionHref("our-story")}
              className="text-lg font-medium text-gray-600 hover:text-[#FF5C28]"
              onClick={() => setIsMobileMenuOpen(false)}
            >
              Our Story
            </Link>

            {/* Mobile What We Do Section */}
            <div className="space-y-2">
              <h3 className="text-lg font-medium text-gray-600">What We Do</h3>
              <div className="ml-4 flex flex-col space-y-2">
                <Link
                  href={getSectionHref("features")}
                  className="text-base font-medium text-gray-600 hover:text-[#FF5C28]"
                  onClick={() => setIsMobileMenuOpen(false)}
                >
                  Features
                </Link>
                <Link
                  href="/training"
                  className="text-base font-medium text-gray-600 hover:text-[#FF5C28]"
                  onClick={() => setIsMobileMenuOpen(false)}
                >
                  Training
                </Link>
                <Link
                  href="/consulting"
                  className="text-base font-medium text-gray-600 hover:text-[#FF5C28]"
                  onClick={() => setIsMobileMenuOpen(false)}
                >
                  Consulting
                </Link>
              </div>
            </div>

            {/* Mobile Team Section */}
            <div className="space-y-2">
              <h3 className="text-lg font-medium text-gray-600">Team</h3>
              <div className="ml-4 flex flex-col space-y-2">
                <Link
                  href={getSectionHref("founder")}
                  className="text-base font-medium text-gray-600 hover:text-[#FF5C28]"
                  onClick={() => setIsMobileMenuOpen(false)}
                >
                  Founder's Story
                </Link>
                <Link
                  href={getSectionHref("team")}
                  className="text-base font-medium text-gray-600 hover:text-[#FF5C28]"
                  onClick={() => setIsMobileMenuOpen(false)}
                >
                  Our People
                </Link>
              </div>
            </div>

            <Link
              href={getSectionHref("contact")}
              className="text-lg font-medium text-gray-600 hover:text-[#FF5C28]"
              onClick={() => setIsMobileMenuOpen(false)}
            >
              Contact
            </Link>
            {userId ? (
              <>
                <SignOutButton>
                  <Button
                    size="sm"
                    variant="outline"
                    className="w-full justify-self-end px-2 py-1 text-sm"
                  >
                    Sign Out
                  </Button>
                </SignOutButton>
                <Link href="/dashboard/home" className="w-full">
                  <Button
                    size="sm"
                    className="w-full justify-self-end px-2 py-1 text-sm"
                  >
                    Dashboard <ArrowRight className="ml-2 size-4" />
                  </Button>
                </Link>
              </>
            ) : (
              <>
                <Link href="/sign-in" className="w-full">
                  <Button
                    size="sm"
                    variant="outline"
                    className="w-full justify-self-end px-2 py-1 text-sm"
                  >
                    Sign in
                  </Button>
                </Link>
                <Link href="/sign-up" className="w-full">
                  <Button
                    size="sm"
                    className="w-full justify-self-end px-2 py-1 text-sm"
                  >
                    Sign Up
                  </Button>
                </Link>
              </>
            )}
          </nav>
        </div>
      )}
    </header>
  );
};
