"use client";

import { useRouter, useSearchParams } from "next/navigation";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import type { Review } from "@/types";

interface ReviewSelectorProps {
  reviews: Review[];
  currentReviewId?: string;
}

export default function ReviewSelector({
  reviews,
  currentReviewId,
}: ReviewSelectorProps) {
  const router = useRouter();
  const searchParams = useSearchParams();

  const handleReviewChange = (reviewId: string) => {
    const params = new URLSearchParams(searchParams.toString());
    params.set("reviewId", reviewId);
    router.push(`/tasks?${params.toString()}`);
  };

  return (
    <div className="w-full">
      <Select onValueChange={handleReviewChange} defaultValue={currentReviewId}>
        <SelectTrigger className="w-[300px]">
          <SelectValue placeholder="Select a review" />
        </SelectTrigger>
        <SelectContent>
          {reviews.map((review) => (
            <SelectItem key={review.id} value={review.id}>
              {review.title}
            </SelectItem>
          ))}
        </SelectContent>
      </Select>
    </div>
  );
}
