"use client";

import { cn } from "@/lib/utils";

interface ChartConfig {
  [key: string]: {
    label: string;
    color: string;
  };
}

interface ChartContainerProps {
  config: ChartConfig;
  className?: string;
  children: React.ReactNode;
}

export function ChartContainer({
  config,
  className,
  children,
}: ChartContainerProps) {
  return (
    <div
      className={cn("relative", className)}
      style={
        {
          "--color-steps": config.steps.color,
        } as React.CSSProperties
      }
    >
      {children}
    </div>
  );
}
