"use client";

import React, { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, CardHeader, CardTitle } from "@/components/ui/card";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  CircleAlert,
  ListCheck,
  CalendarDays,
  ClipboardCheck,
  ChevronDown,
  CircleCheck,
} from "lucide-react";
import Link from "next/link";
import { useRouter } from "next/navigation";
import {
  completeReview,
  archiveReview,
  deleteReview,
  restoreReview,
} from "@/server/actions/reviews";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";

interface AssessmentProps {
  assessment: {
    id: string;
    title: string;
    type: "basic" | "comprehensive";
    status: "draft" | "in_progress" | "completed";
    date: Date;
    tasksCompleted: number;
    totalTasks: number;
    isArchived?: boolean | null;
  };
}

const AssessmentManageCard: React.FC<AssessmentProps> = ({ assessment }) => {
  const [isLoading, setIsLoading] = useState(false);
  const router = useRouter();
  const isComplete = assessment.status === "completed";

  const handleReviewClick = () => {
    if (isComplete) {
      router.push(`/reviews/${assessment.id}/feedback`);
    } else {
      router.push(`/reviews/${assessment.id}/review`);
    }
  };

  const handleReportClick = () => {
    if (isComplete) {
      router.push(`/reviews/${assessment.id}/report`);
    }
  };

  const handleComplete = async () => {
    setIsLoading(true);
    console.log("Starting review completion...");
    try {
      const result = await completeReview(assessment.id);
      console.log("Complete review result:", result);
      if (result.success) {
        console.log("Incrementing review count before navigation");
        router.push(`/reviews/${assessment.id}/complete`);
      }
    } catch (error) {
      console.error("Error completing review:", error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleArchive = async () => {
    setIsLoading(true);
    try {
      const result = await archiveReview(assessment.id);
      if (result.success) {
        router.refresh();
      }
    } catch (error) {
      console.error("Error archiving review:", error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleRestore = async () => {
    setIsLoading(true);
    try {
      const result = await restoreReview(assessment.id);
      if (result.success) {
        router.refresh();
      }
    } catch (error) {
      console.error("Error restoring review:", error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleDelete = async () => {
    setIsLoading(true);
    try {
      const result = await deleteReview(assessment.id);
      if (result.success) {
        router.refresh();
      }
    } catch (error) {
      console.error("Error deleting review:", error);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <>
      <Card className="cursor-pointer transition-all duration-300 hover:border-primary hover:bg-primary/5 hover:shadow-lg">
        <CardHeader className="space-y-4 p-4 sm:p-6">
          <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
            <div className="flex gap-4">
              <div className="flex shrink-0 items-center">
                {isComplete ? (
                  <CircleCheck className="h-8 w-8 stroke-1 text-green-700 sm:h-12 sm:w-12" />
                ) : (
                  <CircleAlert className="h-8 w-8 stroke-1 text-primary sm:h-12 sm:w-12" />
                )}
              </div>
              <div className="flex flex-1 flex-col gap-2">
                <div className="text-base font-bold sm:text-lg">
                  <button onClick={handleReviewClick}>
                    <span>{assessment.title}</span>
                  </button>
                </div>
                <div className="flex flex-col gap-2 text-xs sm:flex-row sm:gap-4 sm:text-sm">
                  <span className="flex items-center text-gray-500">
                    <ListCheck className="mr-1.5 h-3.5 w-3.5 sm:mr-2 sm:h-4 sm:w-4" />
                    <span className="capitalize">{assessment.type}</span>
                  </span>
                  <span className="flex items-center text-gray-500">
                    <CalendarDays className="mr-1.5 h-3.5 w-3.5 sm:mr-2 sm:h-4 sm:w-4" />
                    {new Date(assessment.date).toLocaleDateString("en-GB", {
                      day: "2-digit",
                      month: "2-digit",
                      year: "numeric",
                    })}
                  </span>
                </div>
              </div>
            </div>
            <div className="flex items-center justify-end gap-2 sm:flex-shrink-0 sm:gap-3">
              {isComplete ? (
                <Button
                  size="sm"
                  className="h-8 bg-green-700 text-xs sm:h-9 sm:w-32 sm:text-sm"
                  onClick={handleReportClick}
                >
                  View Report
                </Button>
              ) : (
                <Button
                  disabled
                  size="sm"
                  className="h-8 bg-primary text-xs sm:h-9 sm:w-32 sm:text-sm"
                >
                  Generate Report
                </Button>
              )}
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button
                    size="sm"
                    variant="outline"
                    className="h-8 text-xs sm:h-9 sm:text-sm"
                  >
                    View{" "}
                    <ChevronDown className="ml-1.5 h-3.5 w-3.5 sm:ml-2 sm:h-4 sm:w-4" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  {assessment.isArchived ? (
                    <AlertDialog>
                      <AlertDialogTrigger asChild>
                        <DropdownMenuItem onSelect={(e) => e.preventDefault()}>
                          <span className="text-xs sm:text-sm">Restore</span>
                        </DropdownMenuItem>
                      </AlertDialogTrigger>
                      <AlertDialogContent className="sm:max-w-[425px]">
                        <AlertDialogHeader>
                          <AlertDialogTitle>Restore Review</AlertDialogTitle>
                          <AlertDialogDescription>
                            Are you sure you want to restore this review? It
                            will become active again.
                          </AlertDialogDescription>
                        </AlertDialogHeader>
                        <AlertDialogFooter>
                          <AlertDialogCancel>Cancel</AlertDialogCancel>
                          <AlertDialogAction
                            onClick={handleRestore}
                            className="bg-primary hover:bg-primary/90"
                          >
                            {isLoading ? "Restoring..." : "Restore"}
                          </AlertDialogAction>
                        </AlertDialogFooter>
                      </AlertDialogContent>
                    </AlertDialog>
                  ) : (
                    <>
                      <DropdownMenuItem onClick={handleReviewClick}>
                        <span className="text-xs sm:text-sm">
                          {isComplete ? "View Review" : "Continue Review"}
                        </span>
                      </DropdownMenuItem>
                      <DropdownMenuItem>
                        <Link href={`/tasks?reviewId=${assessment.id}`}>
                          <span className="text-xs sm:text-sm">Tasks</span>
                        </Link>
                      </DropdownMenuItem>
                      <AlertDialog>
                        <AlertDialogTrigger asChild>
                          <DropdownMenuItem
                            onSelect={(e) => e.preventDefault()}
                          >
                            <span className="text-xs sm:text-sm">Archive</span>
                          </DropdownMenuItem>
                        </AlertDialogTrigger>
                        <AlertDialogContent className="sm:max-w-[425px]">
                          <AlertDialogHeader>
                            <AlertDialogTitle>Archive Review</AlertDialogTitle>
                            <AlertDialogDescription>
                              Are you sure you want to archive this review?
                              Archived reviews will be hidden from the main view
                              but can be accessed in the archived section.
                            </AlertDialogDescription>
                          </AlertDialogHeader>
                          <AlertDialogFooter>
                            <AlertDialogCancel>Cancel</AlertDialogCancel>
                            <AlertDialogAction
                              onClick={handleArchive}
                              className="bg-primary hover:bg-primary/90"
                            >
                              {isLoading ? "Archiving..." : "Archive"}
                            </AlertDialogAction>
                          </AlertDialogFooter>
                        </AlertDialogContent>
                      </AlertDialog>
                    </>
                  )}
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          </div>
        </CardHeader>
      </Card>
    </>
  );
};

export default AssessmentManageCard;
