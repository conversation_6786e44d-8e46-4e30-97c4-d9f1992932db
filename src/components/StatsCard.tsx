import React from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";

type StatsCardProps = {
  stats: {
    total: number;
    drafts: number;
    completed: number;
    lastReview: string;
  };
};

const StatsCard = ({ stats }: StatsCardProps) => {
  const statsData = [
    {
      title: "Total Reviews",
      value: stats.total,
    },
    {
      title: "Drafts",
      value: stats.drafts,
    },
    {
      title: "Completed",
      value: stats.completed,
    },
    {
      title: "Last Review",
      value: stats.lastReview,
    },
  ];

  return (
    <>
      {statsData.map((data, index) => (
        <Card key={index} className="p-1 sm:p-2">
          <CardHeader className="flex flex-col items-start space-y-0 pb-1 sm:pb-2">
            <CardTitle className="text-[11px] font-medium sm:text-sm">
              {data.title}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-base font-bold sm:text-xl">{data.value}</div>
          </CardContent>
        </Card>
      ))}
    </>
  );
};

export default StatsCard;
