"use client";

import { usePathname } from "next/navigation";
import { useEffect, useState } from "react";

export default function Navigation() {
  const pathname = usePathname();
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    setIsLoading(false);
  }, [pathname]);

  return (
    <nav>
      {isLoading && (
        <div className="fixed left-0 top-0 h-1 w-full">
          <div className="animate-loading-bar h-full bg-blue-500" />
        </div>
      )}
      {/* Your navigation items */}
    </nav>
  );
}
