"use client";

import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { type TaskWithDetails } from "@/server/actions/tasks";
import { Badge } from "@/components/ui/badge";
import TaskAssigneeSelect from "./TaskAssigneeSelect";
import TaskStatusSelect from "./TaskStatusSelect";
import { Skeleton } from "@/components/ui/skeleton";

interface TasksTableProps {
  tasks: TaskWithDetails[];
  currentUserId: string;
  companyId: string;
  isLoading?: boolean;
}

function TableSkeleton() {
  return (
    <>
      {[...Array(5)].map((_, index) => (
        <TableRow key={index}>
          <TableCell>
            <Skeleton className="h-4 w-8" />
          </TableCell>
          <TableCell>
            <Skeleton className="h-4 w-[250px]" />
          </TableCell>
          <TableCell>
            <Skeleton className="h-6 w-[100px]" />
          </TableCell>
          <TableCell>
            <Skeleton className="h-6 w-[80px]" />
          </TableCell>
          <TableCell>
            <Skeleton className="h-8 w-[120px]" />
          </TableCell>
          <TableCell>
            <Skeleton className="h-8 w-[150px]" />
          </TableCell>
        </TableRow>
      ))}
    </>
  );
}

export default function TasksTable({
  tasks,
  currentUserId,
  companyId,
  isLoading = false,
}: TasksTableProps) {
  return (
    <div className="relative">
      {/* Desktop view */}
      <div className="hidden sm:block">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead className="w-[50px]">#</TableHead>
              <TableHead>Title</TableHead>
              <TableHead>Question Category</TableHead>
              <TableHead>Priority</TableHead>
              <TableHead>Status</TableHead>
              <TableHead>Assignee</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {isLoading ? (
              <TableSkeleton />
            ) : (
              tasks.map((task, index) => (
                <TableRow key={task.id}>
                  <TableCell className="font-medium">{index + 1}</TableCell>
                  <TableCell>{task.title}</TableCell>
                  <TableCell>
                    <Badge variant="outline">{task.category}</Badge>
                  </TableCell>
                  <TableCell>
                    <Badge
                      variant="outline"
                      className={`${
                        task.priority === "high"
                          ? "border-red-500 text-red-500"
                          : task.priority === "medium"
                            ? "border-yellow-500 text-yellow-500"
                            : "border-blue-500 text-blue-500"
                      }`}
                    >
                      {task.priority}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    <TaskStatusSelect task={task} />
                  </TableCell>
                  <TableCell>
                    <TaskAssigneeSelect
                      task={task}
                      currentUserId={currentUserId}
                      companyId={companyId}
                    />
                  </TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </div>

      {/* Mobile view */}
      <div className="space-y-4 sm:hidden">
        {isLoading ? (
          // Mobile loading skeleton
          <div className="space-y-4">
            {[...Array(3)].map((_, index) => (
              <div key={index} className="rounded-lg border p-4">
                <Skeleton className="h-4 w-3/4" />
                <div className="mt-2 flex gap-2">
                  <Skeleton className="h-6 w-20" />
                  <Skeleton className="h-6 w-16" />
                </div>
                <div className="mt-4 flex justify-between gap-2">
                  <Skeleton className="h-8 w-[45%]" />
                  <Skeleton className="h-8 w-[45%]" />
                </div>
              </div>
            ))}
          </div>
        ) : (
          tasks.map((task, index) => (
            <div key={task.id} className="rounded-lg border p-4">
              <div className="flex items-baseline justify-between">
                <span className="text-sm font-medium text-muted-foreground">
                  #{index + 1}
                </span>
                <div className="flex gap-2">
                  <Badge variant="outline">{task.category}</Badge>
                  <Badge
                    variant="outline"
                    className={`${
                      task.priority === "high"
                        ? "border-red-500 text-red-500"
                        : task.priority === "medium"
                          ? "border-yellow-500 text-yellow-500"
                          : "border-blue-500 text-blue-500"
                    }`}
                  >
                    {task.priority}
                  </Badge>
                </div>
              </div>
              <h3 className="mt-1 font-medium">{task.title}</h3>
              <div className="mt-4 flex justify-between gap-2">
                <TaskStatusSelect task={task} />
                <TaskAssigneeSelect
                  task={task}
                  currentUserId={currentUserId}
                  companyId={companyId}
                />
              </div>
            </div>
          ))
        )}
      </div>
    </div>
  );
}
