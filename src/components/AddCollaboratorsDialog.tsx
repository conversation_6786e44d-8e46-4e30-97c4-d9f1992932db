import * as React from "react";
import { Check, Plus, Send, CheckCircle } from "lucide-react";

import { cn } from "@/lib/utils";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { But<PERSON> } from "@/components/ui/button";
import {
  <PERSON>,
  CardContent,
  CardFooter,
  CardHeader,
} from "@/components/ui/card";
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "@/components/ui/command";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import Link from "next/link";

const users = [
  {
    name: "User 1",
    email: "<EMAIL>",
    avatar: "/avatars/01.png",
  },
  {
    name: "User 2",
    email: "<EMAIL>",
    avatar: "/avatars/03.png",
  },
  {
    name: "User 3",
    email: "<EMAIL>",
    avatar: "/avatars/05.png",
  },
  {
    name: "User 4",
    email: "<EMAIL>",
    avatar: "/avatars/02.png",
  },
  {
    name: "User 5",
    email: "<EMAIL>",
    avatar: "/avatars/04.png",
  },
] as const;

type User = (typeof users)[number];

const AddCollaboratorsDialog = () => {
  const [open, setOpen] = React.useState(false);
  const [selectedUsers, setSelectedUsers] = React.useState<User[]>([]);

  const [messages, setMessages] = React.useState([
    {
      role: "agent",
      content: "Hi, how can I help you today?",
    },
    {
      role: "user",
      content: "Hey, I'm having trouble with my account.",
    },
    {
      role: "agent",
      content: "What seems to be the problem?",
    },
    {
      role: "user",
      content: "I can't log in.",
    },
  ]);
  const [input, setInput] = React.useState("");
  const inputLength = input.trim().length;

  return (
    <>
      <Dialog open={open} onOpenChange={setOpen}>
        <DialogContent className="gap-0 p-0 outline-none">
          <DialogHeader className="px-4 pb-4 pt-5">
            <DialogTitle className="custom-navyblue-text text-2xl font-bold">
              You've done it!
            </DialogTitle>
            <DialogDescription className="text-[#0a2245]">
              Review is complete!
              <br />
              Let's send it to the team for review.
            </DialogDescription>
          </DialogHeader>
          <Command className="overflow-hidden rounded-t-none border-t">
            <CommandInput placeholder="Search user..." />
            <CommandList>
              <CommandEmpty>No users found.</CommandEmpty>
              <CommandGroup className="p-2">
                {users.map((user) => (
                  <CommandItem
                    key={user.email}
                    className="flex items-center px-2"
                    onSelect={() => {
                      if (selectedUsers.includes(user)) {
                        return setSelectedUsers(
                          selectedUsers.filter(
                            (selectedUser) => selectedUser !== user,
                          ),
                        );
                      }

                      return setSelectedUsers(
                        [...users].filter((u) =>
                          [...selectedUsers, user].includes(u),
                        ),
                      );
                    }}
                  >
                    <Avatar>
                      <AvatarImage src={user.avatar} alt="Image" />
                      <AvatarFallback>{user.name[0]}</AvatarFallback>
                    </Avatar>
                    <div className="ml-2">
                      <p className="text-sm font-medium leading-none">
                        {user.name}
                      </p>
                      <p className="text-sm text-muted-foreground">
                        {user.email}
                      </p>
                    </div>
                    {selectedUsers.includes(user) ? (
                      <Check className="ml-auto flex h-5 w-5 text-primary" />
                    ) : null}
                  </CommandItem>
                ))}
              </CommandGroup>
            </CommandList>
          </Command>
          <DialogFooter className="flex items-center border-t p-4 sm:justify-between">
            {selectedUsers.length > 0 ? (
              <div className="flex -space-x-2 overflow-hidden">
                {selectedUsers.map((user) => (
                  <Avatar
                    key={user.email}
                    className="inline-block border-2 border-background"
                  >
                    <AvatarImage src={user.avatar} />
                    <AvatarFallback>{user.name[0]}</AvatarFallback>
                  </Avatar>
                ))}
              </div>
            ) : (
              <p className="text-sm text-muted-foreground">
                Select users to add.
              </p>
            )}
            <div className="flex gap-2">
              <Link href="/reviews/feedback">
                <Button
                  variant="outline"
                  className="border border-primary text-primary hover:bg-primary hover:text-white"
                >
                  Review Answers
                </Button>
              </Link>
              <Link href="/reviews/manage">
                <Button
                  disabled={selectedUsers.length < 1}
                  onClick={() => {
                    setOpen(false);
                  }}
                >
                  Send to the team
                </Button>
              </Link>
            </div>
          </DialogFooter>
        </DialogContent>
      </Dialog>
      <Button
        onClick={() => setOpen(true)}
        className="ml-auto bg-[#138213] hover:bg-green-600"
      >
        Complete
        <CheckCircle className="ml-2 h-4 w-4" />
      </Button>
    </>
  );
};

export default AddCollaboratorsDialog;
