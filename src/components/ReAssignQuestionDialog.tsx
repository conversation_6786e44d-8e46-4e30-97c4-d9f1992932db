import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Share2 } from "lucide-react";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import { Avatar, AvatarImage, AvatarFallback } from "@/components/ui/avatar";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

const ReAssignQuestionDialog = () => {
  return (
    <Dialog>
      <DialogTrigger asChild>
        <Button
          size="sm"
          variant="outline"
          className="rounded-full border border-[#138213] text-[#138213] transition-all duration-300 hover:bg-[#138213] hover:text-white"
        >
          <Share2 className="mr-2 h-4 w-4" />
          Re-assign question
        </Button>
      </DialogTrigger>
      <DialogContent>
        <DialogHeader className="px-6">
          <DialogTitle className="text-2xl">
            Assign question to a team member
          </DialogTitle>
          <DialogDescription>
            You can assign this question or its entire category to another team
            member. You will still have access.
          </DialogDescription>
        </DialogHeader>
        {/* <Separator /> */}
        <Card className="border-none py-6">
          <CardContent>
            <div className="flex flex-col gap-4 sm:flex-row sm:justify-between">
              <div className="flex flex-1 flex-col gap-2">
                <Label htmlFor="member">Select user</Label>
                <Select defaultValue="select-member">
                  <SelectTrigger
                    id="member"
                    aria-label="Team Member"
                    className="mt-1"
                  >
                    <SelectValue placeholder="Select" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="select-member">Select</SelectItem>
                    <SelectItem value="user-1">User 1</SelectItem>
                    <SelectItem value="user-2">User 2</SelectItem>
                    <SelectItem value="user-3">User 3</SelectItem>
                    <SelectItem value="user-4">User 4</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="flex flex-1 flex-col gap-2">
                <Label htmlFor="assignment">Assign question</Label>
                <Select defaultValue="select-assignment">
                  <SelectTrigger
                    id="assignment"
                    aria-label="Assignment"
                    className="mt-1"
                  >
                    <SelectValue placeholder="Select" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="select-assignment">Select</SelectItem>
                    <SelectItem value="single">Single question</SelectItem>
                    <SelectItem value="category">Entire category</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="flex flex-1 flex-col gap-2">
                <Label htmlFor="add-assignment">Add user</Label>
                <Button
                  variant="outline"
                  className="mt-1 border-primary text-primary hover:bg-primary hover:text-white"
                >
                  Invite user
                </Button>
              </div>
            </div>
            <Separator className="my-4" />
            <div className="space-y-4">
              <h4 className="text-sm font-medium">People with access</h4>
              <div className="grid gap-6">
                <div className="flex items-center justify-between space-x-4">
                  <div className="flex items-center space-x-4">
                    <Avatar>
                      <AvatarImage src="/avatars/03.png" alt="Image" />
                      <AvatarFallback>U1</AvatarFallback>
                    </Avatar>
                    <div>
                      <p className="text-sm font-medium leading-none">
                        Username 1
                      </p>
                      <p className="text-sm text-muted-foreground">
                        <EMAIL>
                      </p>
                    </div>
                  </div>
                  <Select defaultValue="edit">
                    <SelectTrigger
                      className="ml-auto w-[110px]"
                      aria-label="Edit"
                    >
                      <SelectValue placeholder="Select" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="edit">Can edit</SelectItem>
                      <SelectItem value="view">Can view</SelectItem>
                      <Separator />
                      <SelectItem value="remove">Remove access</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="flex items-center justify-between space-x-4">
                  <div className="flex items-center space-x-4">
                    <Avatar>
                      <AvatarImage src="/avatars/05.png" alt="Image" />
                      <AvatarFallback>U2</AvatarFallback>
                    </Avatar>
                    <div>
                      <p className="text-sm font-medium leading-none">
                        Username 2
                      </p>
                      <p className="text-sm text-muted-foreground">
                        <EMAIL>
                      </p>
                    </div>
                  </div>
                  <Select defaultValue="view">
                    <SelectTrigger
                      className="ml-auto w-[110px]"
                      aria-label="Edit"
                    >
                      <SelectValue placeholder="Select" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="edit">Can edit</SelectItem>
                      <SelectItem value="view">Can view</SelectItem>
                      <Separator />
                      <SelectItem value="remove">Remove access</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="flex items-center justify-between space-x-4">
                  <div className="flex items-center space-x-4">
                    <Avatar>
                      <AvatarImage src="/avatars/01.png" alt="Image" />
                      <AvatarFallback>U3</AvatarFallback>
                    </Avatar>
                    <div>
                      <p className="text-sm font-medium leading-none">
                        Username 3
                      </p>
                      <p className="text-sm text-muted-foreground">
                        <EMAIL>
                      </p>
                    </div>
                  </div>
                  <Select defaultValue="view">
                    <SelectTrigger
                      className="ml-auto w-[110px]"
                      aria-label="Edit"
                    >
                      <SelectValue placeholder="Select" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="edit">Can edit</SelectItem>
                      <SelectItem value="view">Can view</SelectItem>
                      <Separator />
                      <SelectItem value="remove">Remove access</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
        <DialogFooter className="px-6">
          <Button type="submit">Save changes</Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default ReAssignQuestionDialog;
