"use client";

import { useState, useTransition } from "react";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { type TaskWithDetails, updateTaskStatus } from "@/server/actions/tasks";
import { useToast } from "@/components/hooks/use-toast";
import { Loader2 } from "lucide-react";
import { type TaskStatusEnum } from "@/server/drizzle/schema";

interface TaskStatusSelectProps {
  task: TaskWithDetails;
}

export default function TaskStatusSelect({ task }: TaskStatusSelectProps) {
  const { toast } = useToast();
  const [isPending, startTransition] = useTransition();

  const handleStatusChange = (
    newStatus: (typeof TaskStatusEnum.enumValues)[number],
  ) => {
    startTransition(async () => {
      try {
        const result = await updateTaskStatus(task.id, newStatus);
        if (!result.success) {
          toast({
            variant: "destructive",
            title: "Error updating task status",
            description: result.error || "Failed to update task status",
          });
        }
      } catch (error) {
        console.error("Error updating task:", error);
        toast({
          variant: "destructive",
          title: "Error",
          description: "Failed to update task status",
        });
      }
    });
  };

  return (
    <Select
      defaultValue={task.status}
      onValueChange={handleStatusChange}
      disabled={isPending}
    >
      <SelectTrigger className="w-full text-xs sm:w-[130px] sm:text-sm">
        {isPending ? (
          <Loader2 className="mr-2 h-3 w-3 animate-spin sm:h-4 sm:w-4" />
        ) : null}
        <SelectValue />
      </SelectTrigger>
      <SelectContent>
        <SelectItem value="To Do">To Do</SelectItem>
        <SelectItem value="In Progress">In Progress</SelectItem>
        <SelectItem value="Completed">Completed</SelectItem>
        <SelectItem value="Archived">Archived</SelectItem>
      </SelectContent>
    </Select>
  );
}
