"use client";

import { But<PERSON> } from "@/components/ui/button";
import { Download } from "lucide-react";
import { downloadCSV } from "@/lib/utils/csv";

interface ExportButtonProps {
  data: any[];
  filename: string;
}

export function ExportButton({ data, filename }: ExportButtonProps) {
  return (
    <Button
      onClick={() => downloadCSV(data, filename)}
      variant="outline"
      size="sm"
    >
      <Download className="mr-2 h-4 w-4" />
      Export CSV
    </Button>
  );
}
