"use client";

import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { useToast } from "@/components/hooks/use-toast";
import {
  addComment,
  updateComment,
  deleteComment,
} from "@/server/actions/comments";
import { type Comment } from "@/types";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { formatDistanceToNow } from "date-fns";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { MoreHorizontal, Pencil, Trash2, Reply } from "lucide-react";

interface CommentListProps {
  reviewId: string;
  questionId: string;
  userId: string;
  userName: string;
  comments: Comment[];
  onCommentAdded: () => void;
}

export default function CommentList({
  reviewId,
  questionId,
  userId,
  userName,
  comments,
  onCommentAdded,
}: CommentListProps) {
  const [newComment, setNewComment] = useState("");
  const [editingComment, setEditingComment] = useState<string | null>(null);
  const [editText, setEditText] = useState("");
  const [replyingTo, setReplyingTo] = useState<string | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const { toast } = useToast();

  const handleSubmit = async (e: React.FormEvent, parentId?: string) => {
    e.preventDefault();
    if (!newComment.trim()) return;

    setIsSubmitting(true);
    try {
      const result = await addComment({
        reviewId,
        questionId,
        userId,
        text: newComment,
        parentId,
      });

      if (result.success) {
        setNewComment("");
        setReplyingTo(null);
        onCommentAdded();
        toast({
          title: "Success",
          description: "Comment added successfully",
        });
      } else {
        toast({
          title: "Error",
          description: "Failed to add comment",
          variant: "destructive",
        });
      }
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleEdit = async (commentId: string) => {
    if (!editText.trim()) return;

    setIsSubmitting(true);
    try {
      const result = await updateComment({
        commentId,
        text: editText,
      });

      if (result.success) {
        setEditingComment(null);
        onCommentAdded();
        toast({
          title: "Success",
          description: "Comment updated successfully",
        });
      } else {
        toast({
          title: "Error",
          description: "Failed to update comment",
          variant: "destructive",
        });
      }
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleDelete = async (commentId: string) => {
    if (!confirm("Are you sure you want to delete this comment?")) return;

    setIsSubmitting(true);
    try {
      const result = await deleteComment(commentId);

      if (result.success) {
        onCommentAdded();
        toast({
          title: "Success",
          description: "Comment deleted successfully",
        });
      } else {
        toast({
          title: "Error",
          description: "Failed to delete comment",
          variant: "destructive",
        });
      }
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="space-y-6">
      <div className="space-y-4">
        {comments.map((comment) => (
          <div
            key={comment.id}
            className="flex items-start space-x-4 rounded-lg border bg-card p-4"
          >
            <Avatar className="h-8 w-8">
              <AvatarImage src="" />
              <AvatarFallback>
                {comment.userName.charAt(0).toUpperCase()}
              </AvatarFallback>
            </Avatar>
            <div className="flex-1 space-y-1">
              <div className="flex items-center justify-between">
                <p className="text-sm font-medium">{comment.userName}</p>
                <span className="text-xs text-muted-foreground">
                  {formatDistanceToNow(new Date(comment.createdAt), {
                    addSuffix: true,
                  })}
                </span>
              </div>
              <p className="text-sm text-muted-foreground">{comment.text}</p>
            </div>
          </div>
        ))}
      </div>

      <form onSubmit={handleSubmit} className="space-y-4">
        <Textarea
          value={newComment}
          onChange={(e) => setNewComment(e.target.value)}
          placeholder="Add your comment..."
          className="min-h-[100px]"
        />
        <Button
          type="submit"
          disabled={isSubmitting || !newComment.trim()}
          className="w-full"
        >
          {isSubmitting ? "Adding..." : "Add Comment"}
        </Button>
      </form>
    </div>
  );
}
