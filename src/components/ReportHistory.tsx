"use client";

import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { formatDistanceToNow } from "date-fns";
import ReportComparison from "./ReportComparison";
import { type Report } from "@/types";
import { ArrowUpDown, FileText } from "lucide-react";
import { Card } from "@/components/ui/card";
import { Checkbox } from "@/components/ui/checkbox";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { formatDate } from "@/lib/utils";

interface ReportWithScores extends Report {
  ragScores: {
    red: number;
    amber: number;
    green: number;
    total: number;
  };
  categoryScores: Record<
    string,
    {
      red: number;
      amber: number;
      green: number;
      total: number;
    }
  >;
}

interface ReportHistoryProps {
  reports: ReportWithScores[];
  onSelect?: (reportId: string) => void;
  selectedReportId?: string;
}

export default function ReportHistory({
  reports,
  onSelect,
  selectedReportId,
}: ReportHistoryProps) {
  return (
    <Table>
      <TableHeader>
        <TableRow>
          {onSelect && <TableHead className="w-[50px]"></TableHead>}
          <TableHead>Date</TableHead>
          <TableHead>Score</TableHead>
          <TableHead>Status</TableHead>
          <TableHead className="text-right">Actions</TableHead>
        </TableRow>
      </TableHeader>
      <TableBody>
        {reports.map((report) => (
          <TableRow key={report.id}>
            {onSelect && (
              <TableCell>
                <Checkbox
                  checked={selectedReportId === report.id}
                  onCheckedChange={() => onSelect(report.id)}
                />
              </TableCell>
            )}
            <TableCell>{formatDate(report.createdAt)}</TableCell>
            <TableCell>
              {report.ragScores.green}/{report.ragScores.total}
            </TableCell>
            <TableCell className="capitalize">{report.status}</TableCell>
            <TableCell className="text-right">
              <Button variant="ghost" size="sm">
                View
              </Button>
            </TableCell>
          </TableRow>
        ))}
      </TableBody>
    </Table>
  );
}

function calculateScore(ragScores: { green: number; total: number }) {
  return Math.round((ragScores.green / ragScores.total) * 100);
}
