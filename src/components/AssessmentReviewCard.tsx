"use client";

import React, { useState, useEffect } from "react";
import {
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  Card<PERSON>ontent,
  CardFooter,
} from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import { cn } from "@/lib/utils";
import {
  CalendarDays,
  MessageCircle,
  HelpCircle,
  ChevronDown,
  UserCircle,
} from "lucide-react";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuTrigger,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuCheckboxItem,
} from "@/components/ui/dropdown-menu";
import {
  Collapsible,
  CollapsibleTrigger,
  CollapsibleContent,
} from "@/components/ui/collapsible";
import { type QuestionWithCategory } from "@/server/actions/reviews";
import { type Comment, type ReviewComment } from "@/types/comment";
import {
  saveAnswer,
  getQuestionComments,
  saveComment,
  updateComment as updateReviewComment,
} from "@/server/actions/reviews";
import { useToast } from "./hooks/use-toast";
import CommentsBoxCard from "./CommentsBoxCard";
import { getComments } from "@/server/actions/comments";
import { updateComment as updateCommentTable } from "@/server/actions/comments";
import { CommentDialog } from "@/components/CommentDialog";

interface AssessmentReviewCardProps {
  question: QuestionWithCategory;
  reviewId: string;
  userId: string;
  firstName: string;
  lastName: string;
  isSelected: boolean;
  onGuideChange?: (guide: { description: string; image: string }) => void;
  questionNumber: number;
  onAnswerUpdate: (
    questionId: string,
    answer: "yes" | "no" | "partially" | "na",
    comment?: string,
  ) => Promise<void>;
}

const AssessmentReviewCard = ({
  question,
  reviewId,
  userId,
  firstName,
  lastName,
  isSelected,
  onGuideChange,
  questionNumber,
  onAnswerUpdate,
}: AssessmentReviewCardProps) => {
  const { toast } = useToast();
  const [openComments, setOpenComments] = useState(false);
  const [comments, setComments] = useState<ReviewComment[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [editingCommentId, setEditingCommentId] = useState<string | null>(null);
  const [localAnswer, setLocalAnswer] = useState(question.answer || "Yes");
  const [isEditingComment, setIsEditingComment] = useState(false);
  const [showCommentDialog, setShowCommentDialog] = useState(false);
  const [pendingAnswer, setPendingAnswer] = useState<
    "no" | "partially" | "na" | null
  >(null);
  const [isSavingAnswer, setIsSavingAnswer] = useState(false);

  useEffect(() => {
    loadComments();
  }, [reviewId, question.id]);

  const loadComments = async () => {
    try {
      setIsLoading(true);
      const result = await getComments(reviewId, question.id);
      if (result.success && result.data) {
        const reviewComments: ReviewComment[] = result.data.map((c) => ({
          ...c,
          firstName: c.userName.split(" ")[0],
          lastName: c.userName.split(" ")[1] || "",
          updatedAt: c.createdAt,
          source: "comments",
        }));
        setComments(reviewComments);
      } else {
        toast({
          title: "Error",
          description: "Failed to load comments",
          variant: "destructive",
        });
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to load comments",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleAnswerChange = async (
    answer: "yes" | "no" | "partially" | "na",
  ) => {
    // Prevent multiple simultaneous operations
    if (isSavingAnswer) return;

    if (answer === "yes") {
      await saveAnswerToServer(answer);
    } else {
      // Update UI immediately to show the selected answer
      setLocalAnswer(answer);
      setPendingAnswer(answer);
      setShowCommentDialog(true);
    }
  };

  const handleCommentSubmit = async (comment: string) => {
    if (!pendingAnswer) return;

    await saveAnswerToServer(pendingAnswer);

    const result = await saveComment({
      reviewId,
      questionId: question.id,
      text: comment,
      userId,
      firstName,
      lastName,
    });

    if (result.success) {
      void loadComments();
    }

    setPendingAnswer(null);
  };

  const saveAnswerToServer = async (
    answer: "yes" | "no" | "partially" | "na",
  ) => {
    setIsSavingAnswer(true);
    setLocalAnswer(answer);

    try {
      const result = await saveAnswer({
        reviewId,
        questionId: question.id,
        answer,
        userId,
        comment: question.comment,
      });

      if (result.success) {
        // Notify parent component about the answer update
        await onAnswerUpdate(question.id, answer, question.comment);
      } else {
        setLocalAnswer(question.answer || "Yes");
        toast({
          title: "Error",
          description: "Failed to save answer",
          variant: "destructive",
        });
      }
    } catch (error) {
      setLocalAnswer(question.answer || "Yes");
      toast({
        title: "Error",
        description: "Failed to save answer",
        variant: "destructive",
      });
    } finally {
      setIsSavingAnswer(false);
    }
  };

  const handleCommentChange = async (text: string) => {
    try {
      const result = await saveComment({
        reviewId,
        questionId: question.id,
        text,
        userId,
        firstName,
        lastName,
      });

      if (result.success && result.data) {
        const newComment: ReviewComment = {
          ...result.data,
          firstName: result.data.firstName || firstName,
          lastName: result.data.lastName || lastName,
          updatedAt: result.data.createdAt,
          edited: result.data.edited ?? false,
        };
        setComments((prev) => [newComment, ...prev]);
      } else {
        toast({
          title: "Error",
          description: "Failed to save comment",
          variant: "destructive",
        });
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to save comment",
        variant: "destructive",
      });
    }
  };

  const handleEditComment = async (commentId: string, newText: string) => {
    try {
      const commentToEdit = comments.find((c) => c.id === commentId);
      if (!commentToEdit) return;

      setIsEditingComment(true);

      setComments((prevComments: ReviewComment[]) =>
        prevComments.map((comment: ReviewComment) =>
          comment.id === commentId
            ? { ...comment, text: newText, edited: true }
            : comment,
        ),
      );

      const result =
        commentToEdit.source === "comments"
          ? await updateCommentTable({ commentId, text: newText })
          : await updateReviewComment({ commentId, text: newText });

      if (result.success && result.data) {
        if (commentToEdit.source === "comments") {
          const commentData = result.data as unknown as Comment;
          const updatedComment: ReviewComment = {
            id: commentData.id,
            reviewId,
            questionId: question.id,
            userId: commentData.userId,
            firstName: commentData.userName.split(" ")[0],
            lastName: commentData.userName.split(" ")[1] || "",
            text: commentData.text,
            edited: commentData.edited,
            createdAt: commentData.createdAt,
            updatedAt: commentData.createdAt,
            source: "comments",
          };
          setComments((prevComments: ReviewComment[]) =>
            prevComments.map((comment: ReviewComment) =>
              comment.id === commentId ? updatedComment : comment,
            ),
          );
        } else {
          setComments((prevComments: ReviewComment[]) =>
            prevComments.map((comment: ReviewComment) =>
              comment.id === commentId
                ? (result.data as ReviewComment)
                : comment,
            ),
          );
        }
        setEditingCommentId(null);
      }
    } catch (error) {
      loadComments();
      toast({
        title: "Error",
        description: "Failed to update comment",
        variant: "destructive",
      });
    } finally {
      setIsEditingComment(false);
    }
  };

  const formatDate = (date: Date) => {
    return new Date(date).toLocaleDateString("en-GB", {
      day: "2-digit",
      month: "2-digit",
      year: "numeric",
    });
  };

  const handleGetHelpClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    if (onGuideChange && question.questionGuide) {
      onGuideChange(question.questionGuide);
    }
  };

  return (
    <>
      <Card
        onClick={handleGetHelpClick}
        className={cn(
          "cursor-pointer transition-all duration-300 hover:bg-primary/5 hover:shadow-lg",
          isSelected && "border-2 border-primary shadow-lg",
        )}
      >
        <CardHeader>
          <CardTitle className="flex flex-row justify-between space-x-2">
            <div className="flex flex-col items-start justify-start gap-2">
              <div className="flex items-center text-sm text-gray-600">
                <UserCircle className="mr-2 h-4 w-4" />
                <span className="font-semibold">Answered By: </span>
                <span className="font-normal">
                  &nbsp;{firstName}&nbsp;{lastName}
                </span>
              </div>
              <div className="text-wrap text-xl font-bold">
                {questionNumber && `${questionNumber}. `}
                {question.text}
              </div>
            </div>
            <div className="flex items-center space-x-1 rounded-md">
              <Button disabled size="sm" className="bg-primary">
                {localAnswer}
              </Button>
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button
                    size="sm"
                    variant="outline"
                    className="shadow-none"
                    disabled={isSavingAnswer}
                  >
                    {isSavingAnswer ? "Saving..." : "Edit"} &nbsp;
                    <ChevronDown className="h-4 w-4" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent
                  align="end"
                  alignOffset={-5}
                  className="w-[200px]"
                  forceMount
                >
                  <DropdownMenuLabel>Edit Answer</DropdownMenuLabel>
                  <DropdownMenuSeparator />
                  {[
                    { label: "Yes", value: "yes" },
                    { label: "No", value: "no" },
                    { label: "Partially", value: "partially" },
                    { label: "Not Applicable", value: "na" },
                  ].map((answer) => (
                    <DropdownMenuCheckboxItem
                      key={answer.value}
                      checked={localAnswer === answer.label}
                      disabled={isSavingAnswer}
                      onClick={() =>
                        !isSavingAnswer &&
                        handleAnswerChange(
                          answer.value as "yes" | "no" | "partially" | "na",
                        )
                      }
                    >
                      {answer.label}
                    </DropdownMenuCheckboxItem>
                  ))}
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          </CardTitle>
        </CardHeader>
        <Separator />
        <CardFooter>
          <div className="flex w-full flex-col pt-4">
            <div className="flex w-full justify-between">
              <div className="custom-navyblue-text flex space-x-4 text-sm">
                <div className="flex items-center">
                  <span className="inline-flex cursor-default items-center rounded-full py-1 pr-3 text-sm font-medium transition-colors duration-200 ease-in-out">
                    <CalendarDays className="mr-2 h-4 w-4" />
                    {formatDate(new Date())}
                  </span>
                  <Collapsible open={openComments}>
                    <CollapsibleTrigger asChild>
                      <Button
                        size="sm"
                        variant="outline"
                        className={cn(
                          "rounded-full border-white hover:bg-primary/10",
                          openComments &&
                            "border-primary bg-primary text-white hover:bg-primary/80 hover:text-white",
                        )}
                        onClick={(e) => {
                          e.stopPropagation();
                          setOpenComments(!openComments);
                        }}
                      >
                        <MessageCircle className="mr-2 h-4 w-4" />
                        {isLoading ? "..." : `${comments.length} Comments`}
                      </Button>
                    </CollapsibleTrigger>
                  </Collapsible>
                </div>
              </div>
              <Button
                onClick={handleGetHelpClick}
                size="sm"
                variant="outline"
                className={cn(
                  "ml-auto hidden rounded-full font-semibold text-white transition-all duration-300 lg:flex",
                  isSelected
                    ? "border-primary text-primary hover:border-primary/80 hover:bg-primary/80"
                    : "border-[#0a2245] bg-[#0a2245] hover:border-[#0a2245]/80 hover:bg-[#0a2245]/80 hover:text-white",
                )}
              >
                <HelpCircle className="mr-2 h-4 w-4" />
                Get help
              </Button>
              <Button
                onClick={handleGetHelpClick}
                size="sm"
                variant="outline"
                className={cn(
                  "ml-auto flex rounded-full font-semibold text-white transition-all duration-300 lg:hidden",
                  isSelected
                    ? "border-primary text-primary hover:border-primary/80 hover:bg-primary/80"
                    : "border-[#0a2245] bg-[#0a2245] hover:border-[#0a2245]/80 hover:bg-[#0a2245]/80 hover:text-white",
                )}
              >
                <HelpCircle className="h-4 w-4" />
              </Button>
            </div>
            {openComments && (
              <CommentsBoxCard
                comments={comments}
                onCommentChange={handleCommentChange}
                isLoading={isLoading}
                editingCommentId={editingCommentId}
                onEditComment={handleEditComment}
                onStartEdit={setEditingCommentId}
                currentUserId={userId}
                currentUserFirstName={firstName}
                currentUserLastName={lastName}
                isEditingComment={isEditingComment}
              />
            )}
          </div>
        </CardFooter>
      </Card>
      <CommentDialog
        isOpen={showCommentDialog}
        onClose={() => {
          setShowCommentDialog(false);
          setPendingAnswer(null);
          // Reset local answer to original value when canceling
          setLocalAnswer(question.answer || "Yes");
        }}
        onSubmit={handleCommentSubmit}
        answer={pendingAnswer!}
      />
    </>
  );
};

export default AssessmentReviewCard;
