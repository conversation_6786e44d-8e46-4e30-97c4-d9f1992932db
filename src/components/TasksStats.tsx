import { type TaskStats } from "@/types/task";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";

interface TasksStatsProps {
  stats: TaskStats | null;
}

export default function TasksStats({ stats }: TasksStatsProps) {
  if (!stats) return null;

  const statsData = [
    {
      title: "Total Tasks",
      value: stats.total,
    },
    {
      title: "Completed",
      value: stats.completed,
    },
    {
      title: "In Progress",
      value: stats.inProgress,
    },
    {
      title: "To Do",
      value: stats.toDo,
    },
    {
      title: "Archived",
      value: stats.archived,
    },
    {
      title: "Drafts",
      value: stats.draft,
    },
  ];

  return (
    <div className="grid grid-cols-2 gap-4 sm:grid-cols-3 md:grid-cols-6">
      {statsData.map((data, index) => (
        <Card key={index} className="p-1 sm:p-2">
          <CardHeader className="flex flex-col items-start space-y-0 pb-1 sm:pb-2">
            <CardTitle className="text-[11px] font-medium sm:text-sm">
              {data.title}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-base font-bold sm:text-xl">{data.value}</div>
          </CardContent>
        </Card>
      ))}
    </div>
  );
}
