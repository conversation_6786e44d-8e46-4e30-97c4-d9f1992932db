import React from "react";
import NoDataImage from "./NoDataImage";

const CommentCardEmpty = () => {
  return (
    <div className="w-full">
      <h3 className="text-lg font-bold">Contributor Comments</h3>
      <div className="flex flex-1 flex-col gap-4 py-4 lg:gap-6">
        <div className="flex w-full flex-1 items-center justify-center rounded-lg border shadow-sm">
          <div className="overflow-none flex h-[250px] w-full flex-col items-center justify-center p-8 text-center md:p-16 lg:p-24">
            <div className="max-w-md">
              <NoDataImage />
              <h5 className="mb-2 text-lg font-medium tracking-tight">
                You have no contributor comments yet.
              </h5>
              <p className="mb-6 text-sm text-muted-foreground">
                Inputs and comments from different stakeholders invited to
                reviews will appear below
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CommentCardEmpty;
