"use client";

import React from "react";
import { <PERSON><PERSON><PERSON>, <PERSON>, ChevronRight } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import NoDataImage from "./NoDataImage";
import { type ReviewComment } from "@/types";
import Link from "next/link";

interface CommentCardProps {
  comments: ReviewComment[];
}

const CommentCard = ({ comments }: CommentCardProps) => {
  if (!comments || comments.length === 0) {
    return (
      <div className="w-full">
        <div className="flex items-center justify-between pb-2 sm:pb-0">
          <h3 className="text-base font-bold sm:text-lg">
            Contributor Comments
          </h3>
        </div>
        <div className="flex flex-1 flex-col gap-2 py-2 sm:gap-4 sm:py-4 lg:gap-6">
          <div className="flex w-full flex-1 items-center justify-center rounded-lg border shadow-sm">
            <div className="overflow-none flex h-[250px] w-full flex-col items-center justify-center p-8 text-center md:p-16 lg:p-24">
              <div className="max-w-md">
                <NoDataImage />
                <h5 className="mb-2 text-lg font-medium tracking-tight">
                  You have no contributor comments yet.
                </h5>
                <p className="mb-6 text-sm text-muted-foreground">
                  Inputs and comments from different stakeholders invited to
                  reviews will appear below
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="w-full">
      <div className="flex items-center justify-between pb-2 sm:pb-0">
        <h3 className="text-base font-bold sm:text-lg">Contributor Comments</h3>
      </div>
      <div className="flex w-full flex-1 items-center justify-center rounded-lg border shadow-sm">
        <div className="w-full px-3 py-3 sm:px-4 sm:py-4 md:px-8">
          {comments.map((comment, index) => (
            <div
              key={index}
              className="mb-4 border-b pb-4 last:mb-0 last:border-b-0 last:pb-0"
            >
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <span className="flex h-8 w-8 items-center justify-center rounded-full bg-muted text-xs font-semibold">
                    <CircleUser className="h-6 w-6" />
                  </span>
                  <span className="text-sm font-bold">{comment.username}</span>
                </div>
                <div className="text-xs text-muted-foreground">
                  {comment.timestamp}
                </div>
              </div>
              <div className="mt-2 flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
                <div className="py-2 text-left text-sm leading-5 text-muted-foreground">
                  {comment.content}
                </div>
                <div className="flex gap-2">
                  <Button
                    size="sm"
                    className="bg-green-700 text-white hover:bg-green-500"
                  >
                    Mark as Resolved
                  </Button>
                  <Button size="sm" variant="outline">
                    <Link
                      href={`/reviews/${comment.reviewId}/feedback`}
                      className="flex items-center"
                    >
                      View
                      <ChevronRight className="ml-1 h-4 w-4" />
                    </Link>
                  </Button>
                </div>
              </div>
              <div className="flex flex-wrap items-center gap-2 pt-2">
                <div className="flex items-center gap-1">
                  <Hammer className="h-4 w-4" />
                  <span className="text-xs">{comment.category}</span>
                </div>
                <Link href={`/reviews/${comment.reviewId}/feedback`}>
                  <Badge
                    variant="outline"
                    className="cursor-pointer rounded-full border-primary text-primary hover:bg-primary/10"
                  >
                    Question
                  </Badge>
                </Link>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default CommentCard;
