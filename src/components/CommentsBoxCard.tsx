"use client";

import React, { useState } from "react";
import {
  <PERSON>,
  <PERSON>H<PERSON>er,
  CardT<PERSON>le,
  CardDescription,
  CardContent,
} from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import { CircleUser, Trash2, Pencil } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
  TooltipProvider,
} from "@/components/ui/tooltip";
import { Paperclip, CornerDownLeft, Loader2 } from "lucide-react";
import { type ReviewComment } from "@/types/comment";

interface CommentsBoxCardProps {
  comments: ReviewComment[];
  onCommentChange: (comment: string) => void;
  isLoading?: boolean;
  editingCommentId: string | null;
  onEditComment: (commentId: string, newText: string) => void;
  onStartEdit: (commentId: string) => void;
  currentUserId: string;
  currentUserFirstName: string;
  currentUserLastName: string;
  isSubmitting?: boolean;
  isEditingComment?: boolean;
}

const CommentsBoxCard: React.FC<CommentsBoxCardProps> = ({
  comments,
  onCommentChange,
  isLoading,
  editingCommentId,
  onEditComment,
  onStartEdit,
  currentUserId,
  currentUserFirstName,
  currentUserLastName,
  isSubmitting = false,
  isEditingComment = false,
}) => {
  const [newComment, setNewComment] = useState("");
  const [editText, setEditText] = useState("");

  const formatDateTime = (date: Date) => {
    return new Date(date).toLocaleString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (newComment.trim()) {
      onCommentChange(newComment);
      setNewComment("");
    }
  };

  const handleEditSubmit = (commentId: string) => {
    if (editText.trim()) {
      onEditComment(commentId, editText);
      setEditText("");
    }
  };

  return (
    <>
      <Separator className="my-3 border-t border-primary/50" />
      <Card className="overflow-hidden border-none pl-0 pt-3 shadow-none">
        <CardHeader className="flex flex-row items-start">
          <div className="grid gap-0.5">
            <CardTitle className="text-md group flex items-center gap-2">
              Contributor Comments
            </CardTitle>
            <CardDescription>
              Inputs and comments from stakeholders
            </CardDescription>
          </div>
        </CardHeader>
        <CardContent>
          {isLoading ? (
            <div className="py-4 text-center text-sm text-muted-foreground">
              Loading comments...
            </div>
          ) : (
            comments.map((comment) => (
              <div key={comment.id}>
                <div className="items-start py-3 text-sm">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <span className="flex h-8 w-8 items-center justify-center rounded-full bg-muted text-xs font-semibold">
                        <CircleUser className="h-15 w-15" />
                      </span>
                      <div className="flex flex-col">
                        <span className="text-xs font-semibold">
                          {`${comment.firstName} ${comment.lastName}`}
                        </span>
                        <span className="text-xs text-muted-foreground">
                          {formatDateTime(comment.createdAt)}
                        </span>
                      </div>
                    </div>
                  </div>
                  {editingCommentId === comment.id ? (
                    <div className="mt-2">
                      <Textarea
                        value={editText || comment.text}
                        onChange={(e) => setEditText(e.target.value)}
                        className="min-h-[100px]"
                      />
                      <div className="mt-2 flex justify-end gap-2">
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => onStartEdit("")}
                          disabled={isEditingComment}
                        >
                          Cancel
                        </Button>
                        <Button
                          size="sm"
                          onClick={() => handleEditSubmit(comment.id)}
                          disabled={isEditingComment}
                        >
                          {isEditingComment &&
                          editingCommentId === comment.id ? (
                            <>
                              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                              Saving...
                            </>
                          ) : (
                            "Save"
                          )}
                        </Button>
                      </div>
                    </div>
                  ) : (
                    <>
                      <div className="text-md py-3 leading-5 text-muted-foreground">
                        {comment.text}
                        {comment.edited && (
                          <span className="ml-2 text-xs text-muted-foreground">
                            (edited)
                          </span>
                        )}
                      </div>
                      {comment.userId === currentUserId && (
                        <div className="flex items-center gap-2 pt-1">
                          <Button
                            size="sm"
                            variant="ghost"
                            onClick={() => onStartEdit(comment.id)}
                            disabled={isEditingComment}
                          >
                            {isEditingComment &&
                            editingCommentId === comment.id ? (
                              <>
                                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                                Saving...
                              </>
                            ) : (
                              <>
                                <Pencil className="mr-2 h-4 w-4" />
                                Edit
                              </>
                            )}
                          </Button>
                        </div>
                      )}
                    </>
                  )}
                </div>
                <Separator className="my-3" />
              </div>
            ))
          )}
          <div className="flex flex-col gap-1 py-8">
            <h3 className="custom-text-navyblue break-words text-lg font-bold leading-6">
              Have another comment? Please provide your inputs below
            </h3>
            <p className="text-sm text-muted-foreground">
              We'll share your inputs with the admins for consideration
            </p>
          </div>
          <form
            onSubmit={handleSubmit}
            className="relative overflow-hidden rounded-lg border bg-background focus-within:ring-1 focus-within:ring-ring"
          >
            <Label htmlFor="message" className="sr-only">
              Comment
            </Label>
            <Textarea
              id="message"
              placeholder="Add a comment..."
              value={newComment}
              onChange={(e) => setNewComment(e.target.value)}
              className="min-h-12 resize-none border-0 p-3 shadow-none focus-visible:ring-0"
            />
            <div className="flex items-center p-3 pt-0">
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button variant="ghost" size="icon">
                      <Paperclip className="size-4" />
                      <span className="sr-only">Attach file</span>
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent side="top">Attach File</TooltipContent>
                </Tooltip>
              </TooltipProvider>
              <Button
                type="submit"
                size="sm"
                className="ml-auto gap-1.5"
                disabled={isSubmitting}
              >
                {isSubmitting ? (
                  <>
                    <Loader2 className="h-3.5 w-3.5 animate-spin" />
                    Posting...
                  </>
                ) : (
                  <>
                    Post Comment
                    <CornerDownLeft className="size-3.5" />
                  </>
                )}
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>
    </>
  );
};

export default CommentsBoxCard;
