"use client";

import { useState } from "react";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import { Button } from "@/components/ui/button";
import { Trash2 } from "lucide-react";
import {
  type TeamMember,
  updateMemberRole,
  removeMember,
} from "@/server/actions/team";
import { useToast } from "@/components/hooks/use-toast";

export default function TeamTable({
  members,
  currentUserId,
  isAdmin,
}: {
  members: TeamMember[];
  currentUserId: string;
  isAdmin: boolean;
}) {
  const { toast } = useToast();
  const [loading, setLoading] = useState<string | null>(null);

  console.log("TeamTable Props:", { members, currentUserId, isAdmin });

  const handleRoleUpdate = async (
    userId: string,
    newRole: "admin" | "member",
  ) => {
    setLoading(userId);
    const result = await updateMemberRole(userId, newRole);
    setLoading(null);

    if (result.success) {
      toast({
        title: "Success",
        description: "Member role updated successfully",
      });
    } else {
      toast({
        title: "Error",
        description: "Failed to update member role",
        variant: "destructive",
      });
    }
  };

  const handleRemove = async (userId: string) => {
    if (!confirm("Are you sure you want to remove this member?")) return;

    setLoading(userId);
    const result = await removeMember(userId);
    setLoading(null);

    if (result.success) {
      toast({
        title: "Success",
        description: "Member removed successfully",
      });
    } else {
      toast({
        title: "Error",
        description: "Failed to remove member",
        variant: "destructive",
      });
    }
  };

  return (
    <div className="overflow-x-auto">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead className="w-12 px-2 sm:px-4">#</TableHead>
            <TableHead className="min-w-[200px]">Member</TableHead>
            <TableHead className="hidden sm:table-cell">Email</TableHead>
            <TableHead className="min-w-[100px]">Role</TableHead>
            <TableHead className="hidden sm:table-cell">Position</TableHead>
            <TableHead className="w-12 px-2 text-right sm:px-4">
              Actions
            </TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {members.map((member, index) => (
            <TableRow key={member.id}>
              <TableCell className="px-2 sm:px-4">{index + 1}</TableCell>
              <TableCell>
                <div className="flex items-center gap-2 sm:gap-3">
                  <Avatar className="h-8 w-8 sm:h-10 sm:w-10">
                    <AvatarFallback className="text-xs sm:text-sm">
                      {member.firstName?.[0]}
                      {member.lastName?.[0]}
                    </AvatarFallback>
                  </Avatar>
                  <div>
                    <div className="font-medium">
                      {member.firstName} {member.lastName}
                    </div>
                    <div className="text-xs text-muted-foreground sm:hidden">
                      {member.email}
                    </div>
                    <div className="text-xs text-muted-foreground sm:hidden">
                      {member.position || "Not set"}
                    </div>
                  </div>
                </div>
              </TableCell>
              <TableCell className="hidden sm:table-cell">
                {member.email}
              </TableCell>
              <TableCell>
                {isAdmin && member.clerkUserId !== currentUserId ? (
                  <Select
                    defaultValue={member.role}
                    onValueChange={(value: "admin" | "member") =>
                      handleRoleUpdate(member.clerkUserId, value)
                    }
                    disabled={loading === member.clerkUserId}
                  >
                    <SelectTrigger className="h-8 w-[90px] text-xs sm:h-10 sm:w-[110px] sm:text-sm">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="admin">Admin</SelectItem>
                      <SelectItem value="member">Member</SelectItem>
                    </SelectContent>
                  </Select>
                ) : (
                  <span className="text-xs capitalize sm:text-sm">
                    {member.role}
                  </span>
                )}
              </TableCell>
              <TableCell className="hidden sm:table-cell">
                {member.position || "Not set"}
              </TableCell>
              <TableCell className="px-2 text-right sm:px-4">
                {isAdmin && member.clerkUserId !== currentUserId && (
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => handleRemove(member.clerkUserId)}
                    disabled={loading === member.clerkUserId}
                    className="h-8 w-8 p-0 sm:h-9 sm:w-9"
                  >
                    <Trash2 className="h-4 w-4 text-red-500" />
                  </Button>
                )}
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
  );
}
