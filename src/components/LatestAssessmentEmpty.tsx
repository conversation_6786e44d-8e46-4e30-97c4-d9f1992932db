"use client";

import React from "react";
import { Button } from "@/components/ui/button";
import { ListChecks } from "lucide-react";
import Link from "next/link";
import Image from "next/image";
import { useRouter } from "next/navigation";
import { useState } from "react";
import { LoadingSpinner } from "./LoadingSpinner";

export default function LatestAssessmentEmpty() {
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(false);

  const handleClick = () => {
    setIsLoading(true);
    router.push("/reviews/select");
  };

  return (
    <div className="w-full">
      <h3 className="text-lg font-bold">Latest Reviews</h3>
      <div className="flex flex-1 flex-col gap-4 py-4 lg:gap-6">
        <div className="custom-primary-outline flex w-full flex-1 items-center justify-center rounded-lg border shadow-sm">
          <div className="overflow-none flex h-[320px] w-full flex-col items-center justify-center p-8 text-center md:p-16 lg:p-24">
            <div className="max-w-md">
              <Image
                src="/assets/no-data.png"
                alt="No reviews"
                width={100}
                height={100}
                className="mx-auto"
              />
              <h5 className="mb-2 text-lg font-medium tracking-tight">
                You have no reviews yet.
              </h5>
              <p className="mb-6 text-sm text-muted-foreground">
                You have not yet started a review, click below to begin.
              </p>
              <Button onClick={handleClick} disabled={isLoading}>
                {isLoading ? (
                  <>
                    <LoadingSpinner className="mr-2 h-4 w-4" />
                    Please wait...
                  </>
                ) : (
                  <>
                    <ListChecks className="mr-2 h-4 w-4" />
                    Select Review Type
                  </>
                )}
              </Button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
