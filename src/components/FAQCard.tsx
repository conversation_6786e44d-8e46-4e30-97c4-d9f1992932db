import React from "react";
import {
  Card,
  Card<PERSON>eader,
  CardTitle,
  CardDescription,
  CardContent,
} from "@/components/ui/card";
import {
  Accordion,
  AccordionItem,
  AccordionTrigger,
  AccordionContent,
} from "@/components/ui/accordion";
import { List } from "lucide-react";
import { type FAQCardProps } from "@/types";

const FAQCard = ({ title, description, faqs }: FAQCardProps) => {
  return (
    <Card className="overflow-hidden">
      <CardHeader className="custom-gray flex flex-row items-start">
        <div className="grid gap-0.5">
          <CardTitle className="group flex items-center gap-2 text-lg">
            {title}
          </CardTitle>
          <CardDescription>{description}</CardDescription>
        </div>
      </CardHeader>
      <CardContent className="items-start p-6 text-sm">
        <div className="grid w-full gap-3">
          <Accordion type="single" collapsible className="w-full">
            {faqs.map((faq, index) => (
              <AccordionItem key={index} value={`item-${index + 1}`}>
                <AccordionTrigger className="flex justify-between">
                  <div className="flex items-center gap-3">
                    <List className="mr-2 h-4 w-4 flex-shrink-0 text-primary" />
                    <span className="text-left">{faq.question}</span>
                  </div>
                </AccordionTrigger>
                <AccordionContent>{faq.answer}</AccordionContent>
              </AccordionItem>
            ))}
          </Accordion>
        </div>
      </CardContent>
    </Card>
  );
};

export default FAQCard;
