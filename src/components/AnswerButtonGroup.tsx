import React from "react";
import { Button } from "@/components/ui/button";
import { defaultAnswers } from "@/constants";
import { type AnswerButton } from "@/types";

interface AnswerButtonGroupProps {
  answers?: AnswerButton[];
  onAnswer: (answer: "yes" | "no" | "partially" | "na") => void;
  selectedAnswer?: string | null;
}

export default function AnswerButtonGroup({
  answers = defaultAnswers,
  onAnswer,
  selectedAnswer,
}: AnswerButtonGroupProps) {
  return (
    <div className="grid grid-cols-2 gap-6">
      {answers.map((answer) => {
        const Icon = answer.icon;
        return (
          <Button
            key={answer.value}
            size="lg"
            variant={selectedAnswer === answer.value ? "default" : "outline"}
            className={`custom-primary-outline border-color-[#d6673e] transition-all duration-300 ${
              selectedAnswer === answer.value
                ? "bg-[#d6673e] text-white"
                : "hover:bg-[#d6673e] hover:text-white"
            }`}
            onClick={() =>
              onAnswer(answer.value as "yes" | "no" | "partially" | "na")
            }
          >
            {Icon && <Icon className="mr-2 h-4 w-4" />}
            {answer.text}
          </Button>
        );
      })}
    </div>
  );
}
