"use client";

import { ChartContainer } from "@/components/ChartContainer";
import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>A<PERSON><PERSON> } from "recharts";
import { Card, CardContent, CardHeader } from "@/components/ui/card";
import { CheckCircle, XCircle, MinusCircle, HelpCircle } from "lucide-react";

interface ReviewFeedbackChartProps {
  category: string;
  scores: {
    total: number;
    yes: number;
    no: number;
    partially: number;
    na: number;
  };
}

export function ReviewFeedbackChart({
  category,
  scores,
}: ReviewFeedbackChartProps) {
  return (
    <Card className="w-full border-none shadow-none">
      <CardHeader className="flex flex-row items-center space-x-2 rounded-lg border p-2 shadow-none">
        <div className="rounded-md bg-[#f7e0d8] p-2">
          {scores.yes > scores.no ? (
            <CheckCircle className="h-6 w-6 text-primary" />
          ) : (
            <XCircle className="h-6 w-6 text-primary" />
          )}
        </div>
        <div>
          <h2 className="text-sm font-bold">{category}</h2>
          <p className="text-sm text-gray-500">{scores.total} questions</p>
        </div>
      </CardHeader>
      <CardContent className="px-0 py-1">
        <div className="space-y-4">
          {/* Yes answers */}
          <div className="grid auto-rows-min gap-2">
            <div className="flex items-baseline justify-between px-1 text-sm tabular-nums leading-none">
              <div className="flex items-center gap-2">
                <CheckCircle className="h-4 w-4 text-green-600" />
                <span>Yes</span>
              </div>
              <span className="text-sm font-bold text-green-600">
                {scores.yes}/{scores.total}
              </span>
            </div>
            <ChartContainer
              config={{
                steps: {
                  label: "Steps",
                  color: "hsl(142.1 76.2% 36.3%)",
                },
              }}
              className="aspect-auto h-[10px] w-full"
            >
              <BarChart
                layout="vertical"
                width={100}
                height={12}
                data={[
                  { value: scores.yes, remaining: scores.total - scores.yes },
                ]}
              >
                <Bar
                  dataKey="value"
                  fill="var(--color-steps)"
                  radius={4}
                  stackId="a"
                  barSize={12}
                />
                <Bar
                  dataKey="remaining"
                  fill="hsl(var(--muted-foreground) / 0.2)"
                  radius={4}
                  stackId="a"
                  barSize={12}
                />
                <XAxis type="number" domain={[0, scores.total]} hide />
                <YAxis type="category" hide />
              </BarChart>
            </ChartContainer>
          </div>

          {/* No answers */}
          <div className="grid auto-rows-min gap-2">
            <div className="flex items-baseline justify-between px-1 text-sm tabular-nums leading-none">
              <div className="flex items-center gap-2">
                <XCircle className="h-4 w-4 text-red-600" />
                <span>No</span>
              </div>
              <span className="text-sm font-bold text-red-600">
                {scores.no}/{scores.total}
              </span>
            </div>
            <ChartContainer
              config={{
                steps: {
                  label: "Steps",
                  color: "hsl(0 84.2% 60.2%)",
                },
              }}
              className="aspect-auto h-[10px] w-full"
            >
              <BarChart
                layout="vertical"
                width={100}
                height={12}
                data={[
                  { value: scores.no, remaining: scores.total - scores.no },
                ]}
              >
                <Bar
                  dataKey="value"
                  fill="var(--color-steps)"
                  radius={4}
                  stackId="a"
                  barSize={12}
                />
                <Bar
                  dataKey="remaining"
                  fill="hsl(var(--muted-foreground) / 0.2)"
                  radius={4}
                  stackId="a"
                  barSize={12}
                />
                <XAxis type="number" domain={[0, scores.total]} hide />
                <YAxis type="category" hide />
              </BarChart>
            </ChartContainer>
          </div>

          {/* Partially answers */}
          <div className="grid auto-rows-min gap-2">
            <div className="flex items-baseline justify-between px-1 text-sm tabular-nums leading-none">
              <div className="flex items-center gap-2">
                <MinusCircle className="h-4 w-4 text-yellow-600" />
                <span>Partially</span>
              </div>
              <span className="text-sm font-bold text-yellow-600">
                {scores.partially}/{scores.total}
              </span>
            </div>
            <ChartContainer
              config={{
                steps: {
                  label: "Steps",
                  color: "hsl(48 96% 53%)",
                },
              }}
              className="aspect-auto h-[10px] w-full"
            >
              <BarChart
                layout="vertical"
                width={100}
                height={12}
                data={[
                  {
                    value: scores.partially,
                    remaining: scores.total - scores.partially,
                  },
                ]}
              >
                <Bar
                  dataKey="value"
                  fill="var(--color-steps)"
                  radius={4}
                  stackId="a"
                  barSize={12}
                />
                <Bar
                  dataKey="remaining"
                  fill="hsl(var(--muted-foreground) / 0.2)"
                  radius={4}
                  stackId="a"
                  barSize={12}
                />
                <XAxis type="number" domain={[0, scores.total]} hide />
                <YAxis type="category" hide />
              </BarChart>
            </ChartContainer>
          </div>

          {/* N/A answers */}
          <div className="grid auto-rows-min gap-2">
            <div className="flex items-baseline justify-between px-1 text-sm tabular-nums leading-none">
              <div className="flex items-center gap-2">
                <HelpCircle className="h-4 w-4 text-gray-600" />
                <span>N/A</span>
              </div>
              <span className="text-sm font-bold text-gray-600">
                {scores.na}/{scores.total}
              </span>
            </div>
            <ChartContainer
              config={{
                steps: {
                  label: "Steps",
                  color: "hsl(215 20% 65%)",
                },
              }}
              className="aspect-auto h-[10px] w-full"
            >
              <BarChart
                layout="vertical"
                width={100}
                height={12}
                data={[
                  { value: scores.na, remaining: scores.total - scores.na },
                ]}
              >
                <Bar
                  dataKey="value"
                  fill="var(--color-steps)"
                  radius={4}
                  stackId="a"
                  barSize={12}
                />
                <Bar
                  dataKey="remaining"
                  fill="hsl(var(--muted-foreground) / 0.2)"
                  radius={4}
                  stackId="a"
                  barSize={12}
                />
                <XAxis type="number" domain={[0, scores.total]} hide />
                <YAxis type="category" hide />
              </BarChart>
            </ChartContainer>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
