"use client";

import { Card } from "@/components/ui/card";
import {
  <PERSON><PERSON>hart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  <PERSON>lt<PERSON>,
  Legend,
} from "recharts";
import { type Report } from "@/types";

interface ReportComparisonProps {
  reports: [Report, Report];
}

export default function ReportComparison({
  reports: [report1, report2],
}: ReportComparisonProps) {
  // Transform data for comparison
  const ragData = [
    {
      name: formatDate(report1.createdAt),
      Red: (report1.ragScores.red / report1.ragScores.total) * 100,
      Amber: (report1.ragScores.amber / report1.ragScores.total) * 100,
      Green: (report1.ragScores.green / report1.ragScores.total) * 100,
    },
    {
      name: formatDate(report2.createdAt),
      Red: (report2.ragScores.red / report2.ragScores.total) * 100,
      Amber: (report2.ragScores.amber / report2.ragScores.total) * 100,
      Green: (report2.ragScores.green / report2.ragScores.total) * 100,
    },
  ];

  // Get all unique categories
  const allCategories = new Set([
    ...Object.keys(report1.categoryScores),
    ...Object.keys(report2.categoryScores),
  ]);

  // Transform category data for comparison
  const categoryData = Array.from(allCategories).map((category) => {
    const score1 = calculateCategoryScore(report1.categoryScores[category]);
    const score2 = calculateCategoryScore(report2.categoryScores[category]);

    return {
      category,
      [formatDate(report1.createdAt)]: score1,
      [formatDate(report2.createdAt)]: score2,
      change: score2 - score1,
    };
  });

  return (
    <div className="space-y-8">
      <Card className="p-6">
        <h3 className="mb-4 text-lg font-semibold">RAG Score Comparison</h3>
        <BarChart width={600} height={300} data={ragData}>
          <CartesianGrid strokeDasharray="3 3" />
          <XAxis dataKey="name" />
          <YAxis />
          <Tooltip />
          <Legend />
          <Bar dataKey="Red" fill="#ef4444" />
          <Bar dataKey="Amber" fill="#f59e0b" />
          <Bar dataKey="Green" fill="#22c55e" />
        </BarChart>
      </Card>

      <Card className="p-6">
        <h3 className="mb-4 text-lg font-semibold">Category Score Changes</h3>
        <div className="space-y-4">
          {categoryData.map(({ category, change, ...scores }) => (
            <div key={category} className="flex items-center justify-between">
              <div className="flex-1">
                <div className="font-medium">{category}</div>
                <div className="text-sm text-muted-foreground">
                  {Object.entries(scores).map(([date, score]) => (
                    <span key={date}>
                      {date}: {score}%{" "}
                    </span>
                  ))}
                </div>
              </div>
              <div
                className={`text-sm font-medium ${
                  change > 0
                    ? "text-green-600"
                    : change < 0
                      ? "text-red-600"
                      : "text-gray-600"
                }`}
              >
                {change > 0 ? "+" : ""}
                {change}%
              </div>
            </div>
          ))}
        </div>
      </Card>
    </div>
  );
}

function formatDate(date: Date) {
  return new Date(date).toLocaleDateString("en-GB", {
    day: "2-digit",
    month: "2-digit",
    year: "numeric",
  });
}

function calculateCategoryScore(
  scores:
    | {
        green: number;
        total: number;
      }
    | undefined,
) {
  if (!scores) return 0;
  return Math.round((scores.green / scores.total) * 100);
}
