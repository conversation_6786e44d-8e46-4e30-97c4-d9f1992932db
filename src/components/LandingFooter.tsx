import React from "react";
import Link from "next/link";
import Image from "next/image";

export function LandingFooter() {
  return (
    <footer className="bg-white py-12">
      <div className="container mx-auto px-4">
        <div className="mb-8 grid grid-cols-1 gap-8 md:grid-cols-5">
          <div>
            <div className="mb-4 flex items-center">
              <Image
                src="/assets/wakari-logo.png"
                alt="Wakari Logo"
                width={150}
                height={150}
              />
            </div>
            <p className="text-sm text-gray-600">
              Transform your workplace with our tech-driven accessibility
              assessment tool and expert training.
            </p>
          </div>

          <div>
            <h4 className="mb-4 font-bold">Navigation</h4>
            <ul className="space-y-2 text-sm text-gray-600">
              <li>
                <Link href="#home" className="hover:text-[#FF5C28]">
                  Home
                </Link>
              </li>
              <li>
                <Link href="#our-story" className="hover:text-[#FF5C28]">
                  Our Story
                </Link>
              </li>
              <li>
                <Link href="#contact" className="hover:text-[#FF5C28]">
                  Contact
                </Link>
              </li>
            </ul>
          </div>

          <div>
            <h4 className="mb-4 font-bold">What We Do</h4>
            <ul className="space-y-2 text-sm text-gray-600">
              <li>
                <Link href="#features" className="hover:text-[#FF5C28]">
                  Features
                </Link>
              </li>
              <li>
                <Link href="/training" className="hover:text-[#FF5C28]">
                  Training
                </Link>
              </li>
              <li>
                <Link href="/consulting" className="hover:text-[#FF5C28]">
                  Consulting
                </Link>
              </li>
            </ul>
          </div>

          <div>
            <h4 className="mb-4 font-bold">Team</h4>
            <ul className="space-y-2 text-sm text-gray-600">
              <li>
                <Link href="#founder" className="hover:text-[#FF5C28]">
                  Founder's Story
                </Link>
              </li>
              <li>
                <Link href="#team" className="hover:text-[#FF5C28]">
                  Our People
                </Link>
              </li>
            </ul>
          </div>

          <div>
            <h4 className="mb-4 font-bold">Legal</h4>
            <ul className="space-y-2 text-sm text-gray-600">
              <li>
                <Link href="/policy">Privacy Policy</Link>
              </li>
              <li>
                <Link href="/terms" className="hover:text-[#FF5C28]">
                  Terms
                </Link>
              </li>
              {/* <li>
                <Link href="/accessibility">Accessibility</Link>
              </li> */}
            </ul>
          </div>
        </div>
        <div className="border-t border-gray-200 pt-8">
          <p className="text-center text-sm text-gray-600">
            &copy;{new Date().getFullYear()} Wakari. All rights reserved.
          </p>
        </div>
      </div>
    </footer>
  );
}
