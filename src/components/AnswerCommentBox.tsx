"use client";

import React, { useState } from "react";
import { Textarea } from "@/components/ui/textarea";
import { But<PERSON> } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import {
  TooltipProvider,
  Tooltip,
  TooltipTrigger,
  TooltipContent,
} from "@/components/ui/tooltip";
import { Separator } from "@/components/ui/separator";
import {
  CircleUser,
  Paperclip,
  CornerDownLeft,
  Loader2,
  Pencil,
} from "lucide-react";
import { type Comment } from "@/types";
import { LoadingSpinner } from "@/components/LoadingSpinner";
import { useToast } from "@/components/hooks/use-toast";
import { addComment } from "@/server/actions/comments";

interface AnswerCommentBoxProps {
  value: string;
  onChange: (comment: string) => void;
  required?: boolean;
  reviewId: string;
  questionId: string;
  userId: string;
  comments?: Comment[];
  onCommentAdded?: (comment: Comment) => void;
  isSubmitting?: boolean;
  editingCommentId?: string | null;
  onEditComment?: (commentId: string, newText: string) => void;
  onStartEdit?: (commentId: string) => void;
  isEditingComment?: boolean;
}

const AnswerCommentBox: React.FC<AnswerCommentBoxProps> = ({
  value,
  onChange,
  required = false,
  reviewId,
  questionId,
  userId,
  comments = [],
  onCommentAdded,
  isSubmitting = false,
  editingCommentId,
  onEditComment,
  onStartEdit,
  isEditingComment,
}) => {
  const [newComment, setNewComment] = useState("");
  const [isPostingComment, setIsPostingComment] = useState(false);
  const { toast } = useToast();
  const [editText, setEditText] = useState("");

  const formatDateTime = (date: Date) => {
    return new Date(date).toLocaleString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!newComment.trim() || isPostingComment) return;

    setIsPostingComment(true);

    const optimisticComment: Comment = {
      id: "temp-" + Date.now(),
      reviewId,
      questionId,
      userId,
      userName: "You",
      text: newComment,
      createdAt: new Date(),
      edited: false,
    };
    onCommentAdded?.(optimisticComment);
    setNewComment("");

    try {
      const result = await addComment({
        reviewId,
        questionId,
        userId,
        text: newComment,
      });

      if (!result.success) {
        toast({
          title: "Error",
          description: result.error || "Failed to add comment",
          variant: "destructive",
        });
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to add comment",
        variant: "destructive",
      });
    } finally {
      setIsPostingComment(false);
    }
  };

  const handleEditSubmit = (commentId: string) => {
    if (editText.trim() && onEditComment) {
      onEditComment(commentId, editText);
      setEditText("");
    }
  };

  return (
    <div className="grid gap-3">
      <div>
        <h3 className="custom-text-navyblue text-md mb-2 font-bold">
          Comments
          {required && <span className="ml-1 text-red-500">*</span>}
        </h3>
        <p className="mb-4 text-sm text-gray-600">
          {required
            ? "A comment is required for this answer. Please provide a detailed explanation."
            : "Please provide a detailed explanation for your answer. Include any relevant information or examples that support your response."}
        </p>
      </div>

      {/* Display existing comments */}
      <div className="space-y-4">
        {comments.map((comment) => (
          <div key={comment.id}>
            <div className="items-start py-3 text-sm">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <span className="flex h-8 w-8 items-center justify-center rounded-full bg-muted text-xs font-semibold">
                    <CircleUser className="h-15 w-15" />
                  </span>
                  <div className="flex flex-col">
                    <span className="text-xs font-semibold">
                      {comment.userName}
                    </span>
                    <span className="text-xs text-muted-foreground">
                      {formatDateTime(comment.createdAt)}
                    </span>
                  </div>
                </div>
              </div>
              {editingCommentId === comment.id ? (
                <div className="mt-2">
                  <Textarea
                    value={editText || comment.text}
                    onChange={(e) => setEditText(e.target.value)}
                    className="min-h-[100px]"
                    disabled={isEditingComment}
                  />
                  <div className="mt-2 flex justify-end gap-2">
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => onStartEdit?.("")}
                      disabled={isEditingComment}
                    >
                      Cancel
                    </Button>
                    <Button
                      size="sm"
                      onClick={() => handleEditSubmit(comment.id)}
                      disabled={isEditingComment}
                    >
                      {isEditingComment ? (
                        <>
                          <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                          Saving...
                        </>
                      ) : (
                        "Save"
                      )}
                    </Button>
                  </div>
                </div>
              ) : (
                <>
                  <div className="text-md py-3 leading-5 text-muted-foreground">
                    {comment.text}
                    {comment.edited && (
                      <span className="ml-2 text-xs text-muted-foreground">
                        (edited)
                      </span>
                    )}
                  </div>
                  {comment.userId === userId && (
                    <div className="flex items-center gap-2 pt-1">
                      <Button
                        size="sm"
                        variant="ghost"
                        onClick={() => onStartEdit?.(comment.id)}
                        disabled={isEditingComment}
                      >
                        {isEditingComment && editingCommentId === comment.id ? (
                          <>
                            <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                            Saving...
                          </>
                        ) : (
                          <>
                            <Pencil className="mr-2 h-4 w-4" />
                            Edit
                          </>
                        )}
                      </Button>
                    </div>
                  )}
                </>
              )}
            </div>
            <Separator className="my-3" />
          </div>
        ))}
      </div>

      {/* Comment input form */}
      <form
        onSubmit={handleSubmit}
        className="relative overflow-hidden rounded-lg border bg-background focus-within:ring-1 focus-within:ring-ring"
      >
        <Label htmlFor="comment" className="sr-only">
          Comment
        </Label>
        <Textarea
          id="comment"
          placeholder="Add a comment..."
          value={newComment}
          onChange={(e) => setNewComment(e.target.value)}
          className="min-h-12 resize-none border-0 p-3 shadow-none focus-visible:ring-0"
          required={required}
          disabled={isPostingComment}
        />
        <div className="flex items-center p-3 pt-0">
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button variant="ghost" size="icon" disabled={isPostingComment}>
                  <Paperclip className="size-4" />
                  <span className="sr-only">Attach file</span>
                </Button>
              </TooltipTrigger>
              <TooltipContent side="top">Attach File</TooltipContent>
            </Tooltip>
          </TooltipProvider>
          <Button
            type="submit"
            size="sm"
            className="ml-auto gap-1.5"
            disabled={isPostingComment || !newComment.trim()}
          >
            {isPostingComment ? (
              <>
                <Loader2 className="h-3.5 w-3.5 animate-spin" />
                Posting...
              </>
            ) : (
              <>
                Post Comment
                <CornerDownLeft className="size-3.5" />
              </>
            )}
          </Button>
        </div>
      </form>
    </div>
  );
};

export default AnswerCommentBox;
