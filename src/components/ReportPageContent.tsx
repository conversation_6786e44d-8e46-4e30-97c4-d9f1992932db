"use client";

import HeaderBox from "@/components/HeaderBox";
import ReportView from "@/components/ReportView";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardHeader, CardTitle, CardContent } from "@/components/ui/card";
import ReportActions from "@/components/ReportActions";
import { ListCheck, HelpCircle, ChevronLeft } from "lucide-react";
import { ChartContainer } from "@/components/ui/chart";
import { <PERSON>, BarChart, YAxis, XAxis } from "recharts";
import { useRouter } from "next/navigation";
import { Progress } from "@/components/ui/progress";
import { CheckCircle, XCircle, MinusCircle } from "lucide-react";
import { type RAGScore } from "@/types";
import { getReviewTitle } from "@/server/actions/reviews";
import { useState, useEffect } from "react";
import {
  Radar,
  RadarChart,
  PolarGrid,
  PolarAngleAxis,
  PolarRadiusAxis,
  Tooltip as RechartsTooltip,
  Legend,
} from "recharts";
import { sortCategoryEntries } from "@/lib/utils/categories";

interface ReportPageContentProps {
  reviewId: string;
  isAdmin: boolean;
  ragScores: RAGScore;
  categoryScores: Record<string, RAGScore>;
  recommendations: Array<{
    id: string;
    questionId: string;
    text: string;
    priority: string;
    estimatedEffort?: string | null;
    category: string;
    question: string;
    description?: string | null;
  }>;
}

// Removed unused interfaces

interface CategoryScore {
  total: number;
  yes: number;
  no: number;
  partially: number;
  na: number;
}

const COLORS = {
  green: "#059669",
  amber: "hsl(38 92% 50%)",
  red: "#dc2626",
  gray: "hsl(var(--muted-foreground))",
  muted: "hsl(var(--muted-foreground) / 0.2)",
} as const;

export function ReportPageContent({
  reviewId,
  ragScores,
  categoryScores,
  recommendations,
  isAdmin,
}: ReportPageContentProps) {
  const router = useRouter();
  const [reviewTitle, setReviewTitle] = useState<string>("");

  useEffect(() => {
    const fetchReviewTitle = async () => {
      try {
        const result = await getReviewTitle(reviewId);
        if (result.success && result.data) {
          setReviewTitle(result.data);
        }
      } catch (error) {
        console.error("Error fetching review title:", error);
      }
    };

    void fetchReviewTitle();
  }, [reviewId]);

  // No longer needed

  // Transform category scores for the chart bars with percentages and RAG status
  const categoryBars = sortCategoryEntries(Object.entries(categoryScores)).map(
    ([category, scores]) => {
      const total = scores.total - (scores.na || 0); // Exclude N/A from total
      const weightedScore = Math.round(
        ((scores.green + scores.amber * 0.5) / total) * 100,
      );

      // Determine RAG status
      let status;
      if (weightedScore >= 80) status = "green";
      else if (weightedScore >= 50) status = "amber";
      else status = "red";

      return {
        title: category,
        value: scores.green + Math.floor(scores.amber * 0.5), // Weighted value
        total,
        percentage: weightedScore,
        status,
      };
    },
  );

  // Removed unused taskData variable

  const transformToQuestionScores = (scores: RAGScore): CategoryScore => {
    return {
      total: scores.total,
      yes: scores.green,
      no: scores.red,
      partially: scores.amber,
      na: scores.na || 0,
    };
  };

  // Sort categories according to the defined order
  const sortedCategories = sortCategoryEntries(Object.entries(categoryScores));

  return (
    <div className="w-full space-y-4 md:space-y-8">
      {!isAdmin && (
        <div className="flex flex-col gap-4 md:flex-row md:items-center">
          <Button
            variant="ghost"
            size="icon"
            onClick={() => router.push("/reviews/manage")}
            className="h-9 w-9"
          >
            <ChevronLeft className="h-6 w-6" />
            <span className="sr-only">Back to reviews</span>
          </Button>

          <div className="flex w-full flex-col gap-4 md:flex-row md:items-center md:justify-between">
            <HeaderBox
              type="title"
              title={`Review Report - ${reviewTitle}`}
              subtext="Review analysis and recommendations"
            />
            <ReportActions reviewId={reviewId} />
          </div>
        </div>
      )}

      {/* RAG Analysis */}
      <div>
        <h3 className="mb-4 text-lg font-semibold">RAG Analysis</h3>
        <div className="grid grid-cols-2 gap-2 sm:grid-cols-2 md:grid-cols-4 md:gap-4">
          <Card className="p-3 transition-colors hover:bg-muted/50 md:p-6">
            <div className="space-y-1.5 md:space-y-2">
              <div className="text-sm font-bold" style={{ color: COLORS.red }}>
                Red Items
              </div>
              <div className="text-xl font-bold md:text-2xl">
                {ragScores.red}
              </div>
              <div className="text-xs text-muted-foreground md:text-sm">
                Require immediate attention
              </div>
            </div>
          </Card>
          <Card className="p-3 transition-colors hover:bg-muted/50 md:p-6">
            <div className="space-y-1.5 md:space-y-2">
              <div
                className="text-sm font-bold"
                style={{ color: COLORS.amber }}
              >
                Amber Items
              </div>
              <div className="text-xl font-bold md:text-2xl">
                {ragScores.amber}
              </div>
              <div className="text-xs text-muted-foreground md:text-sm">
                Need improvement
              </div>
            </div>
          </Card>
          <Card className="p-3 transition-colors hover:bg-muted/50 md:p-6">
            <div className="space-y-1.5 md:space-y-2">
              <div
                className="text-sm font-bold"
                style={{ color: COLORS.green }}
              >
                Green Items
              </div>
              <div className="text-xl font-bold md:text-2xl">
                {ragScores.green}
              </div>
              <div className="text-xs text-muted-foreground md:text-sm">
                Meeting requirements
              </div>
            </div>
          </Card>
          <Card className="p-3 transition-colors hover:bg-muted/50 md:p-6">
            <div className="space-y-1.5 md:space-y-2">
              <div className="text-sm font-bold" style={{ color: COLORS.gray }}>
                N/A Items
              </div>
              <div className="text-xl font-bold md:text-2xl">
                {ragScores.na}
              </div>
              <div className="text-xs text-muted-foreground md:text-sm">
                Not applicable
              </div>
            </div>
          </Card>
        </div>
      </div>

      {/* Category Breakdown - Most detailed view */}
      <div>
        <h3 className="mb-4 text-lg font-semibold">Category Breakdown</h3>
        <div className="mb-4 flex flex-col gap-2 rounded-md bg-muted/50 p-4 text-sm md:flex-row md:items-start md:gap-4">
          <div className="flex h-8 w-8 flex-shrink-0 items-center justify-center rounded-full bg-primary/10">
            <ListCheck className="h-4 w-4 text-primary" />
          </div>
          <div className="space-y-1">
            <p className="font-medium leading-none">Understanding the scores</p>
            <p className="text-muted-foreground">
              Detailed breakdown per category showing:
              <span className="mx-1" style={{ color: COLORS.red }}>
                No (Non-compliant)
              </span>
              &middot;
              <span className="mx-1" style={{ color: COLORS.amber }}>
                Partial (In progress)
              </span>
              &middot;
              <span className="mx-1" style={{ color: COLORS.green }}>
                Yes (Compliant)
              </span>
              &middot;
              <span className="mx-1" style={{ color: COLORS.gray }}>
                N/A (Not applicable)
              </span>
            </p>
          </div>
        </div>
        <div className="grid gap-2 sm:grid-cols-2 md:gap-4 lg:grid-cols-4">
          {sortedCategories.map(([category, scores]) => {
            const questionScores = transformToQuestionScores(scores);
            return (
              <Card key={category} className="w-full border-none shadow-none">
                <CardHeader className="flex flex-row items-center space-x-2 rounded-lg border p-2 shadow-none">
                  <div className="rounded-md bg-[#f7e0d8] p-2">
                    {questionScores.yes > questionScores.no ? (
                      <CheckCircle className="h-6 w-6 text-primary" />
                    ) : (
                      <XCircle className="h-6 w-6 text-primary" />
                    )}
                  </div>
                  <div>
                    <h2 className="text-sm font-bold">{category}</h2>
                    <p className="text-sm text-gray-500">
                      {questionScores.total} questions
                    </p>
                  </div>
                </CardHeader>
                <CardContent className="px-0 py-1">
                  <div className="space-y-4">
                    {/* Yes progress bar */}
                    <div className="flex items-center justify-between">
                      <div className="flex w-3/4 items-center gap-2">
                        <CheckCircle
                          className="h-4 w-4"
                          style={{ color: COLORS.green }}
                        />
                        <Progress
                          value={
                            (questionScores.yes / questionScores.total) * 100
                          }
                          className="h-2"
                          indicatorClassName="bg-green-600"
                        />
                      </div>
                      <span className="text-sm font-bold text-green-600">
                        {questionScores.yes}/{questionScores.total}
                      </span>
                    </div>

                    {/* No progress bar */}
                    <div className="flex items-center justify-between">
                      <div className="flex w-3/4 items-center gap-2">
                        <XCircle
                          className="h-4 w-4"
                          style={{ color: COLORS.red }}
                        />
                        <Progress
                          value={
                            (questionScores.no / questionScores.total) * 100
                          }
                          className="h-2"
                          indicatorClassName="bg-red-600"
                        />
                      </div>
                      <span className="text-sm font-bold text-red-600">
                        {questionScores.no}/{questionScores.total}
                      </span>
                    </div>

                    {/* Partially progress bar */}
                    <div className="flex items-center justify-between">
                      <div className="flex w-3/4 items-center gap-2">
                        <MinusCircle
                          className="h-4 w-4"
                          style={{ color: COLORS.amber }}
                        />
                        <Progress
                          value={
                            (questionScores.partially / questionScores.total) *
                            100
                          }
                          className="h-2"
                          indicatorClassName="!bg-[hsl(38_92%_50%)]"
                        />
                      </div>
                      <span
                        className="text-sm font-bold"
                        style={{ color: "hsl(38 92% 50%)" }}
                      >
                        {questionScores.partially}/{questionScores.total}
                      </span>
                    </div>

                    {/* N/A progress bar */}
                    <div className="flex items-center justify-between">
                      <div className="flex w-3/4 items-center gap-2">
                        <HelpCircle
                          className="h-4 w-4"
                          style={{ color: COLORS.gray }}
                        />
                        <Progress
                          value={
                            (questionScores.na / questionScores.total) * 100
                          }
                          className="h-2"
                          indicatorClassName="bg-gray-600"
                        />
                      </div>
                      <span className="text-sm font-bold text-gray-600">
                        {questionScores.na}/{questionScores.total}
                      </span>
                    </div>
                  </div>
                </CardContent>
              </Card>
            );
          })}
        </div>
      </div>

      {/* Overall Compliance Progress */}
      <Card className="h-full w-full rounded-lg border shadow-sm md:pb-3 lg:pb-4">
        <CardHeader>
          <CardTitle>
            <div className="flex items-center justify-between">
              <div className="text-base font-bold tracking-wide md:text-[18px]">
                Category Compliance Overview
              </div>
            </div>
          </CardTitle>
          <div className="mt-2 flex flex-col gap-2 rounded-md bg-muted/50 p-4 text-sm md:flex-row md:items-start md:gap-4">
            <div className="flex h-8 w-8 flex-shrink-0 items-center justify-center rounded-full bg-primary/10">
              <ListCheck className="h-4 w-4 text-primary" />
            </div>
            <div className="space-y-1">
              <p className="font-medium leading-none">
                Understanding the radar chart
              </p>
              <p className="text-muted-foreground">
                The radar chart shows weighted compliance by category:
                <span className="mx-1">
                  Each axis represents a category (0-100%), weighted as Red
                  (0%), Amber (50%), Green (100%)
                </span>
                •
                <span className="mx-1">
                  Higher values indicate better compliance
                </span>
                •
                <span className="mx-1">
                  Shape uniformity suggests balanced compliance
                </span>
              </p>
            </div>
          </div>
        </CardHeader>
        <CardContent className="flex flex-col items-center gap-6 overflow-x-auto pt-0 md:flex-row lg:h-[280px] lg:pl-0">
          {/* Left side: Radar chart */}
          <div className="w-full min-w-[300px] md:w-1/2">
            <div className="flex justify-center">
              <RadarChart
                width={350}
                height={350}
                data={categoryBars}
                margin={{ top: 30, right: 30, bottom: 30, left: 30 }}
                className="md:h-[400px] md:w-[450px]"
              >
                <PolarGrid />
                <PolarAngleAxis
                  dataKey="title"
                  tick={({ x, y, payload }) => {
                    const words = payload.value.split(" & ");
                    const lineHeight = 12;
                    const radius = 170; // Distance from center
                    const radian =
                      (((payload.angle || 0) - 90) * Math.PI) / 180;
                    const cx = x + (radius - 170) * Math.cos(radian);
                    const cy = y + (radius - 170) * Math.sin(radian);

                    return (
                      <g transform={`translate(${cx},${cy})`}>
                        {words.map((word: string, index: number) => (
                          <text
                            key={index}
                            x={0}
                            y={index * lineHeight}
                            textAnchor="middle"
                            fill="hsl(var(--foreground))"
                            fontSize={11}
                            fontWeight="500"
                          >
                            {word}
                          </text>
                        ))}
                      </g>
                    );
                  }}
                />
                <PolarRadiusAxis
                  angle={30}
                  domain={[0, 100]}
                  tick={{
                    fill: "hsl(var(--foreground))",
                    fontSize: 10,
                  }}
                />
                <Radar
                  name="Category Score"
                  dataKey="percentage"
                  stroke={COLORS.green}
                  fill={COLORS.green}
                  fillOpacity={0.6}
                />
                <RechartsTooltip />
                <Legend />
              </RadarChart>
            </div>
          </div>

          {/* Right side: Category progress bars */}
          <div className="w-full min-w-[300px] md:w-1/2">
            {categoryBars.map((item, index) => (
              <div key={index} className="mb-4 grid auto-rows-min gap-2">
                <div className="flex items-center justify-between px-1 text-sm">
                  <div className="flex items-center gap-2">
                    <div
                      className={`h-2 w-2 rounded-full ${
                        item.status === "red"
                          ? "bg-[#dc2626]"
                          : item.status === "amber"
                            ? "bg-[#fbbf24]"
                            : "bg-[#059669]"
                      }`}
                    />
                    <span className="font-medium">{item.title}</span>
                  </div>
                  <div className="flex items-center gap-2 tabular-nums">
                    <span className="text-sm font-bold">
                      {item.percentage}%
                    </span>
                    <span className="text-xs text-muted-foreground">
                      ({item.value}/{item.total})
                    </span>
                  </div>
                </div>
                <ChartContainer
                  config={{
                    steps: {
                      label: "Steps",
                      color:
                        item.status === "red"
                          ? "#dc2626"
                          : item.status === "amber"
                            ? "#fbbf24"
                            : "#059669",
                    },
                  }}
                  className="aspect-auto h-[10px] w-full"
                >
                  <BarChart
                    accessibilityLayer
                    layout="vertical"
                    width={100}
                    height={12}
                    data={[
                      { value: item.value, remaining: item.total - item.value },
                    ]}
                    stackOffset="none"
                  >
                    <Bar
                      dataKey="value"
                      fill={
                        item.status === "red"
                          ? "#dc2626"
                          : item.status === "amber"
                            ? "hsl(38 92% 50%)"
                            : "#059669"
                      }
                      radius={4}
                      stackId="a"
                      barSize={12}
                    />
                    <Bar
                      dataKey="remaining"
                      fill="hsl(var(--muted-foreground) / 0.2)"
                      radius={4}
                      stackId="a"
                      barSize={12}
                    />
                    <XAxis type="number" domain={[0, item.total]} hide />
                    <YAxis type="category" hide />
                  </BarChart>
                </ChartContainer>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Detailed Report */}
      <ReportView
        reviewId={reviewId}
        report={{
          id: reviewId,
          ragScores,
          categoryScores,
          recommendations,
        }}
      />
    </div>
  );
}
