"use client";

import React, { startTransition, useTransition } from "react";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { ListCheck, RefreshCcw } from "lucide-react";
import { LoadingSpinner } from "./LoadingSpinner";
import { useRouter } from "next/navigation";
interface ConfirmAssessmentDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: () => void;
  onCancel: () => void;
  type: "basic" | "comprehensive";
}

export default function ConfirmAssessmentDialog({
  isOpen,
  onClose,
  onConfirm,
  onCancel,
  type,
}: ConfirmAssessmentDialogProps) {
  const [isConfirmPending, startConfirmTransition] = useTransition();
  const [isSwitchPending, startSwitchTransition] = useTransition();
  const router = useRouter();

  const handleConfirm = () => {
    startConfirmTransition(() => {
      onConfirm();
    });
  };

  const handleSwitch = () => {
    startSwitchTransition(() => {
      onCancel();
    });
  };

  const handleOutsideClick = () => {
    startSwitchTransition(() => {
      // Navigate back to review selection
      router.push("/reviews/select");
    });
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleOutsideClick}>
      <DialogContent className="max-h-[90vh] overflow-y-auto p-4 sm:max-h-none sm:max-w-[600px] sm:overflow-y-visible sm:p-6">
        <DialogHeader>
          <DialogTitle>
            <div className="custom-navyblue-text text-xs font-semibold">
              Confirm Review
            </div>
          </DialogTitle>
          <DialogDescription>
            <div className="custom-navyblue-text text-lg font-bold sm:text-2xl">
              You have selected the {type} Review
            </div>
          </DialogDescription>
        </DialogHeader>
        <div className="grid gap-3 py-2 sm:gap-4 sm:py-4">
          <div className="custom-navyblue-text text-sm text-muted-foreground">
            This review contains questions in all 4 categories
          </div>
          <div className="custom-navyblue-text text-md font-semibold">
            Review Breakdown
          </div>
          <div className="relative aspect-video w-full overflow-hidden rounded-lg">
            <iframe
              src="https://www.youtube.com/embed/9rvBZnqYnGw?si=MNbrpDqFcRX9FJFu"
              className="absolute inset-0 h-full w-full"
              loading="lazy"
              frameBorder="0"
              allowFullScreen
            />
          </div>
          <div className="custom-navyblue-text text-md mt-1 font-semibold sm:mt-2">
            How switching between reviews works
          </div>
          <div className="text-sm text-muted-foreground">
            Once you have started a review, you cannot switch between review
            types yet.
          </div>
        </div>

        <DialogFooter className="mt-4 flex-col gap-2 sm:flex-row sm:justify-between">
          <Button
            size="sm"
            variant="outline"
            className="w-full border border-primary text-primary hover:bg-primary hover:text-white sm:w-auto"
            onClick={handleSwitch}
            disabled={isSwitchPending || isConfirmPending}
          >
            {isSwitchPending ? (
              <>
                <LoadingSpinner className="mr-2 h-4 w-4" />
                <span className="hidden sm:inline">Switching...</span>
                <span className="sm:hidden">Switch...</span>
              </>
            ) : (
              <>
                <RefreshCcw className="mr-2 h-4 w-4" />
                <span className="hidden sm:inline">Switch Review</span>
                <span className="sm:hidden">Switch Review</span>
              </>
            )}
          </Button>
          <Button
            onClick={handleConfirm}
            disabled={isConfirmPending || isSwitchPending}
            size="sm"
            className="w-full items-center justify-center whitespace-nowrap rounded-md bg-primary sm:w-auto"
          >
            {isConfirmPending ? (
              <>
                <LoadingSpinner className="mr-2 h-4 w-4" />
                <span className="hidden sm:inline">Starting...</span>
                <span className="sm:hidden">Starting...</span>
              </>
            ) : (
              <>
                <ListCheck className="mr-2 h-4 w-4" />
                <span className="hidden sm:inline">Start Review</span>
                <span className="sm:hidden">Start Review</span>
              </>
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
