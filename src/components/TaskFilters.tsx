"use client";

import { useEffect, useState } from "react";
import { usePathname, useRouter, useSearchParams } from "next/navigation";
import { useTransition } from "react";
import { Button } from "@/components/ui/button";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Trash2, Loader2 } from "lucide-react";

export default function TaskFilters() {
  const router = useRouter();
  const pathname = usePathname();
  const searchParams = useSearchParams();
  const [isPending, startTransition] = useTransition();
  const [activeFilter, setActiveFilter] = useState<string | null>(null);

  const updateFilter = (key: string, value: string | null) => {
    setActiveFilter(key);
    startTransition(() => {
      const params = new URLSearchParams(searchParams.toString());
      if (value && value !== "all") {
        params.set(key, value);
      } else {
        params.delete(key);
      }
      router.push(`${pathname}?${params.toString()}`);
    });
  };

  const handleClearFilters = () => {
    setActiveFilter("clear");
    startTransition(() => {
      const reviewId = searchParams.get("reviewId");
      const newParams = new URLSearchParams();
      if (reviewId) {
        newParams.set("reviewId", reviewId);
      }
      router.push(`${pathname}?${newParams.toString()}`);
    });
  };

  return (
    <div className="mb-4 grid grid-cols-2 gap-2 sm:mb-6 sm:flex sm:flex-wrap sm:gap-4">
      <Select
        onValueChange={(value) => updateFilter("status", value)}
        defaultValue={searchParams.get("status") ?? "all"}
        disabled={isPending}
      >
        <SelectTrigger className="w-full text-xs sm:w-[180px] sm:text-sm">
          {isPending && activeFilter === "status" ? (
            <Loader2 className="mr-2 h-3 w-3 animate-spin sm:h-4 sm:w-4" />
          ) : null}
          <SelectValue placeholder="Filter by Status" />
        </SelectTrigger>
        <SelectContent>
          <SelectItem value="all">All Status</SelectItem>
          <SelectItem value="To Do">To Do</SelectItem>
          <SelectItem value="In Progress">In Progress</SelectItem>
          <SelectItem value="Completed">Completed</SelectItem>
          <SelectItem value="Archived">Archived</SelectItem>
        </SelectContent>
      </Select>

      <Select
        onValueChange={(value) => updateFilter("priority", value || null)}
        defaultValue={searchParams.get("priority") ?? "all"}
        disabled={isPending}
      >
        <SelectTrigger className="w-full text-xs sm:w-[180px] sm:text-sm">
          {isPending && activeFilter === "priority" ? (
            <Loader2 className="mr-2 h-3 w-3 animate-spin sm:h-4 sm:w-4" />
          ) : null}
          <SelectValue placeholder="Filter by Priority" />
        </SelectTrigger>
        <SelectContent>
          <SelectItem value="all">All Priorities</SelectItem>
          <SelectItem value="high">High</SelectItem>
          <SelectItem value="medium">Medium</SelectItem>
          <SelectItem value="low">Low</SelectItem>
        </SelectContent>
      </Select>

      <Select
        onValueChange={(value) => updateFilter("category", value || null)}
        defaultValue={searchParams.get("category") ?? "all"}
        disabled={isPending}
      >
        <SelectTrigger className="w-full text-xs sm:w-[180px] sm:text-sm">
          {isPending && activeFilter === "category" ? (
            <Loader2 className="mr-2 h-3 w-3 animate-spin sm:h-4 sm:w-4" />
          ) : null}
          <SelectValue placeholder="Filter by Category" />
        </SelectTrigger>
        <SelectContent>
          <SelectItem value="all">All Categories</SelectItem>
          <SelectItem value="Building & Infrastructure">
            Building & Infrastructure
          </SelectItem>
          <SelectItem value="Equipment & Ergonomics">
            Equipment & Ergonomics
          </SelectItem>
          <SelectItem value="Work culture & experience">
            Work culture & experience
          </SelectItem>
          <SelectItem value="Job, Role & Time Structure">
            Job, Role & Time Structure
          </SelectItem>
        </SelectContent>
      </Select>

      <Button
        variant="outline"
        onClick={handleClearFilters}
        className="h-9 w-full text-xs sm:h-10 sm:w-[180px] sm:text-sm"
        disabled={isPending}
      >
        {isPending && activeFilter === "clear" ? (
          <Loader2 className="mr-2 h-3 w-3 animate-spin sm:h-4 sm:w-4" />
        ) : (
          <Trash2 className="mr-2 h-3 w-3 sm:h-4 sm:w-4" />
        )}
        Clear filters
      </Button>
    </div>
  );
}
