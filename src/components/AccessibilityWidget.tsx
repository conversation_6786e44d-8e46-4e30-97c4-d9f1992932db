"use client";

import { useEffect } from "react";

const AccessibilityWidget = () => {
  useEffect(() => {
    if (typeof window !== "undefined") {
      const script = document.createElement("script");

      script.setAttribute("data-account", "72TyTUFzVS");
      script.setAttribute("src", "https://cdn.userway.org/widget.js");
      script.setAttribute("data-position", "3");

      // Uncomment and customize options as needed
      // script.setAttribute("data-position", "3");
      // script.setAttribute("data-size", "small");
      // script.setAttribute("data-language", "en");
      // script.setAttribute("data-color", "#053e67");
      // script.setAttribute("data-type", "1");
      // script.setAttribute("data-statement_text", "Our Accessibility Statement");
      // script.setAttribute("data-statement_url", "http://www.example.com/accessibility");
      // script.setAttribute("data-mobile", "true");
      // script.setAttribute("data-trigger", "triggerId");

      document.body.appendChild(script);
    }
  }, []);

  return null; // No UI needed, just loads the script
};

export default AccessibilityWidget;
