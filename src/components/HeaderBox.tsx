import React from "react";
import { type HeaderBoxProps } from "@/types";

const HeaderBox = ({
  type = "title",
  title,
  user,
  subtext,
}: HeaderBoxProps) => {
  return (
    <div>
      <h1 className="text-navyblue text-2xl font-bold">
        {title}
        {type === "greeting" && user && (
          <span className="font-normal">&nbsp;{user}</span>
        )}
      </h1>
      <p className="">{subtext}</p>
    </div>
  );
};

export default HeaderBox;
