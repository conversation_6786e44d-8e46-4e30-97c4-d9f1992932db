"use client";

import { useState, useEffect } from "react";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Loader2 } from "lucide-react";

interface CommentDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (comment: string) => Promise<void>;
  answer: "no" | "partially" | "na";
}

export function CommentDialog({
  isOpen,
  onClose,
  onSubmit,
  answer,
}: CommentDialogProps) {
  const [comment, setComment] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleSubmit = async () => {
    if (!comment.trim()) return;

    setIsSubmitting(true);
    try {
      await onSubmit(comment);
      setComment("");
      // Add a small delay to ensure dialog cleanup
      setTimeout(() => {
        // Ensure body styles are cleaned up
        document.body.style.removeProperty("pointer-events");
        onClose();
      }, 100);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleOpenChange = (open: boolean) => {
    if (!open) {
      // Clear comment when dialog closes
      setComment("");
      // Force cleanup of any lingering modal styles
      setTimeout(() => {
        // Remove any pointer-events: none from body
        document.body.style.removeProperty("pointer-events");
        onClose();
      }, 50);
    }
  };

  // Cleanup effect to ensure body styles are reset
  useEffect(() => {
    // Force cleanup when dialog state changes
    if (!isOpen) {
      const timer = setTimeout(() => {
        document.body.style.removeProperty("pointer-events");
      }, 200);
      return () => clearTimeout(timer);
    }

    return () => {
      // Cleanup on unmount
      document.body.style.removeProperty("pointer-events");
    };
  }, [isOpen]);

  // Additional cleanup on window focus/click to ensure UI is responsive
  useEffect(() => {
    const handleWindowClick = () => {
      if (!isOpen) {
        document.body.style.removeProperty("pointer-events");
      }
    };

    const handleWindowFocus = () => {
      if (!isOpen) {
        document.body.style.removeProperty("pointer-events");
      }
    };

    window.addEventListener("click", handleWindowClick);
    window.addEventListener("focus", handleWindowFocus);

    return () => {
      window.removeEventListener("click", handleWindowClick);
      window.removeEventListener("focus", handleWindowFocus);
    };
  }, [isOpen]);

  return (
    <Dialog open={isOpen} onOpenChange={handleOpenChange}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Add Comment</DialogTitle>
          <DialogDescription>
            Please provide a detailed explanation for your {answer} answer.
          </DialogDescription>
        </DialogHeader>
        <Textarea
          value={comment}
          onChange={(e) => setComment(e.target.value)}
          placeholder="Enter your comment..."
          className="min-h-[100px]"
        />
        <DialogFooter>
          <Button variant="outline" onClick={onClose} disabled={isSubmitting}>
            Cancel
          </Button>
          <Button
            onClick={handleSubmit}
            disabled={!comment.trim() || isSubmitting}
          >
            {isSubmitting ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Saving...
              </>
            ) : (
              "Save Comment"
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
