"use client";

import React from "react";
import Link from "next/link";
// import { SidebarLinks } from "@/constants/";
import * as LucideIcons from "lucide-react";
import { cn } from "@/lib/utils";
import { usePathname, useRouter } from "next/navigation";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { type SideBarProps, type CompanyData } from "@/types/index";
import { useState, useEffect, useMemo } from "react";
import { AdminLink } from "@/components/AdminLink";
import { FeedbackButton } from "./FeedbackButton";
import { Menu, X } from "lucide-react";

interface SidebarLink {
  icon: string;
  route: string;
  label: string;
  subLinks?: Array<{
    route: string;
    label: string;
  }>;
}

export const SidebarLinks: SidebarLink[] = [
  {
    icon: "Home",
    route: "/dashboard/home",
    label: "Home",
  },
  {
    icon: "ListChecks",
    route: "/reviews/manage",
    label: "Reviews",
  },
  {
    icon: "ClipboardCheck",
    route: "/tasks",
    label: "Tasks",
    subLinks: [
      {
        route: "/all-tasks",
        label: "All",
      },
      {
        route: "/tasks-in-progress",
        label: "In Progress",
      },
      {
        route: "/tasks-completed",
        label: "Completed",
      },
      {
        route: "/tasks-archived",
        label: "Archived",
      },
    ],
  },
  // {
  //   icon: "BarChart2",
  //   route: "/reports",
  //   label: "Reports",
  // },
  {
    icon: "Users",
    route: "/team",
    label: "Team",
  },
  {
    icon: "Settings",
    route: "/settings",
    label: "Company Settings",
  },
  // {
  //   icon: "HelpCircle",
  //   route: "/help",
  //   label: "Help",
  // },
];

interface SidebarProps {
  user: {
    $id: string;
    firstName: string;
    lastName: string;
    email: string;
    UserId: string;
  };
  company: CompanyData;
}

function formatCityName(city: string | undefined | null): string {
  if (!city) return "Not set";

  return city
    .split("_")
    .map((word) => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
    .join(" ");
}

const SideBar = ({ user, company }: SidebarProps) => {
  const pathname = usePathname();
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(false);
  const [activeRoute, setActiveRoute] = useState(pathname);
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);

  const links = useMemo(() => {
    return SidebarLinks;
  }, []);

  useEffect(() => {
    if (pathname !== activeRoute) {
      setIsLoading(false);
      setActiveRoute(pathname);
      setIsMobileMenuOpen(false);
    }
  }, [pathname, activeRoute]);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const sidebar = document.getElementById("sidebar-container");
      if (sidebar && !sidebar.contains(event.target as Node)) {
        setIsMobileMenuOpen(false);
      }
    };

    if (isMobileMenuOpen) {
      document.addEventListener("mousedown", handleClickOutside);
    }

    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [isMobileMenuOpen]);

  if (!links) return null;

  return (
    <>
      {/* Mobile Menu Button */}
      <button
        onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
        className="fixed left-4 top-3.5 z-[60] block lg:hidden"
        aria-label={isMobileMenuOpen ? "Close menu" : "Open menu"}
      >
        {isMobileMenuOpen ? (
          <X size={20} className="text-gray-700" />
        ) : (
          <Menu size={20} className="text-gray-700" />
        )}
      </button>

      {/* Backdrop */}
      {isMobileMenuOpen && (
        <div
          className="fixed inset-0 z-[45] bg-black/50 lg:hidden"
          onClick={() => setIsMobileMenuOpen(false)}
        />
      )}

      {/* Sidebar */}
      <div
        id="sidebar-container"
        className={cn(
          "fixed inset-y-0 left-0 z-[50] h-screen w-[280px] transform border-r bg-[#f8f8fa] transition-transform duration-300 ease-in-out lg:translate-x-0",
          isMobileMenuOpen ? "translate-x-0" : "-translate-x-full",
        )}
      >
        {isLoading && (
          <div className="absolute left-0 top-0 z-[51] h-1 w-full">
            <div className="animate-loading-bar h-full bg-primary" />
          </div>
        )}

        <div className="flex h-full flex-col">
          <div className="flex h-14 items-center border-b px-4 lg:h-[60px] lg:px-6">
            <p className="custom-navyblue-text font-bold">
              Location:{" "}
              <span className="font-normal">
                {formatCityName(company.headquarterCity)}
              </span>
            </p>
          </div>

          <div className="flex flex-1 flex-col justify-between">
            <nav className="grid items-start gap-2 px-2 py-6 text-sm font-semibold lg:px-4">
              {links.map((link: SidebarLink) => {
                const IconComponent = LucideIcons[
                  link.icon as keyof typeof LucideIcons
                ] as React.ComponentType<{ size: number }>;
                const isActive =
                  pathname === link.route ||
                  pathname.startsWith(
                    link.route.split("/").slice(0, 2).join("/"),
                  );
                return (
                  <Link
                    href={link.route}
                    key={link.label}
                    onClick={() => {
                      setIsLoading(true);
                      setActiveRoute(link.route);
                      setIsMobileMenuOpen(false);
                    }}
                    className={cn(
                      "custom-navyblue-text flex items-center gap-3 rounded-lg px-3 py-2 text-base transition-all hover:bg-primary/10 hover:text-primary",
                      {
                        "bg-primary text-white hover:bg-primary hover:text-white":
                          isActive,
                      },
                      isLoading && isActive && "opacity-70",
                    )}
                  >
                    {IconComponent && <IconComponent size={24} />}
                    {link.label}
                  </Link>
                );
              })}
              <AdminLink userId={user.UserId} />
            </nav>

            <div className="mt-auto flex flex-col gap-6 p-2 pt-0 md:p-4 md:pt-0">
              <FeedbackButton />
              <p className="text-center text-xs text-[#0a2245]">Ver 0.01</p>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default SideBar;
