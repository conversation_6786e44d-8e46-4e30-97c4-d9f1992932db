"use client";

import * as React from "react";
import { <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, Cell } from "recharts";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import {
  ChartContainer,
  ChartTooltip,
  ChartTooltipContent,
} from "@/components/ui/chart";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, <PERSON><PERSON><PERSON>, <PERSON>Axis, XAxis } from "recharts";
import Link from "next/link";
import {
  type RemediationData,
  type TaskPieChartData,
  type TaskBarChartData,
} from "@/types";
import RemediationsEmpty from "./RemediationEmpty";
import { ChevronRight, Loader2 } from "lucide-react";
import { useState } from "react";

interface RemediationProps {
  tasks: RemediationData | null;
  latestReviewId: string;
}

// Update chart configuration to ensure all statuses are included
const chartConfig = {
  "To Do": {
    label: "To Do",
    color: "hsl(var(--chart-1))",
  },
  "In Progress": {
    label: "In Progress",
    color: "hsl(var(--chart-2))",
  },
  Completed: {
    label: "Completed",
    color: "hsl(var(--chart-3))",
  },
  Archived: {
    label: "Archived",
    color: "hsl(var(--chart-4))",
  },
};

// Add the colors array for the Pie chart
const pieColors = [
  "hsl(var(--chart-1))",
  "hsl(var(--chart-2))",
  "hsl(var(--chart-3))",
  "hsl(var(--chart-4))",
];

export default function Remediation({
  tasks,
  latestReviewId,
}: RemediationProps) {
  const [isLoading, setIsLoading] = useState(false);

  if (!tasks || tasks.tasks.length === 0) {
    return <RemediationsEmpty />;
  }

  const detailsLink = latestReviewId
    ? `/tasks?reviewId=${latestReviewId}`
    : "/tasks";

  return (
    <div className="w-full">
      <div className="flex items-center justify-between">
        <h3 className="text-base font-bold sm:text-lg">Latest Remediations</h3>
        <Link
          href={detailsLink}
          className="text-xs text-primary hover:underline sm:text-sm"
          onClick={() => setIsLoading(true)}
        >
          (All Tasks)
        </Link>
      </div>

      <div className="flex-0 flex flex-col gap-4 py-4 lg:gap-6">
        <Card className="h-full w-full rounded-lg border shadow-sm md:pb-3 lg:pb-4">
          <CardHeader>
            <CardTitle>
              <div className="flex items-center justify-between">
                <div className="text-base font-bold tracking-wide sm:text-[18px]">
                  Tasks
                </div>
                <Link
                  href={detailsLink}
                  className="text-sm text-primary hover:underline"
                  onClick={() => setIsLoading(true)}
                >
                  <Button
                    variant="outline"
                    className="w-auto border-primary text-primary hover:bg-primary/10 hover:text-primary"
                    disabled={isLoading}
                  >
                    {isLoading ? (
                      <Loader2 className="mr-1 h-4 w-4 animate-spin" />
                    ) : null}
                    Details
                    <ChevronRight className="ml-1 h-4 w-4" />
                  </Button>
                </Link>
              </div>
            </CardTitle>
          </CardHeader>
          <CardContent className="flex flex-col items-center gap-4 overflow-x-auto pt-0 sm:gap-6 md:flex-row lg:h-[280px] lg:pl-0">
            {/* Left side: Pie chart */}
            <div className="w-full min-w-[200px] md:w-2/5">
              <ChartContainer
                config={chartConfig}
                className="mx-auto aspect-square max-h-[160px] sm:max-h-[180px]"
              >
                <PieChart>
                  <ChartTooltip
                    cursor={false}
                    content={<ChartTooltipContent hideLabel />}
                  />
                  <Pie
                    data={tasks.pieChartData}
                    dataKey="visitors"
                    nameKey="browser"
                    innerRadius={55}
                    strokeWidth={3}
                    fill="#8884d8"
                  >
                    {tasks.pieChartData.map((entry, index) => (
                      <Cell
                        key={`cell-${index}`}
                        fill={pieColors[index % pieColors.length]}
                      />
                    ))}
                    <Label
                      content={({ viewBox }) => {
                        if (viewBox && "cx" in viewBox && "cy" in viewBox) {
                          return (
                            <text
                              x={viewBox.cx}
                              y={viewBox.cy}
                              textAnchor="middle"
                              dominantBaseline="middle"
                            >
                              <tspan
                                x={viewBox.cx}
                                y={viewBox.cy}
                                className="fill-foreground text-2xl font-bold sm:text-3xl"
                              >
                                {tasks.totalTasks.toLocaleString()}
                              </tspan>
                              <tspan
                                x={viewBox.cx}
                                y={(viewBox.cy || 0) + 20}
                                className="fill-muted-foreground text-xs sm:text-sm"
                              >
                                Total Tasks
                              </tspan>
                            </text>
                          );
                        }
                        return null;
                      }}
                    />
                  </Pie>
                </PieChart>
              </ChartContainer>
            </div>
            {/* Right side: Bar charts */}
            <div className="w-full min-w-[250px] px-2 sm:px-0 md:w-1/2">
              {tasks.barChartData.map((item, index) => (
                <div key={index} className="mb-4 grid auto-rows-min gap-2">
                  <div className="flex items-baseline justify-between px-1 text-xs tabular-nums leading-none sm:text-sm">
                    <span className="line-clamp-1">{item.title}</span>
                    <span className="text-xs font-bold sm:text-sm">
                      {item.value}/{item.total}
                    </span>
                  </div>
                  <ChartContainer
                    config={{
                      steps: {
                        label: "Steps",
                        color: "hsl(var(--chart-1))",
                      },
                    }}
                    className="aspect-auto h-[10px] w-full"
                  >
                    <BarChart
                      accessibilityLayer
                      layout="vertical"
                      width={100}
                      height={12}
                      data={[
                        {
                          value: item.value,
                          remaining: item.total - item.value,
                        },
                      ]}
                      stackOffset="none"
                    >
                      <Bar
                        dataKey="value"
                        fill="var(--color-steps)"
                        radius={4}
                        stackId="a"
                        barSize={12}
                      />
                      <Bar
                        dataKey="remaining"
                        fill="hsl(var(--muted-foreground) / 0.2)"
                        radius={4}
                        stackId="a"
                        barSize={12}
                      />
                      <XAxis type="number" domain={[0, item.total]} hide />
                      <YAxis type="category" hide />
                    </BarChart>
                  </ChartContainer>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
