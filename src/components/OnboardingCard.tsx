"use client";

import React, { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { LoadingSpinner } from "./LoadingSpinner";
import Image from "next/image";
import Link from "next/link";
import OnboardingProgressBar from "./OnboardingProgressBar";
import { data, steps } from "@/constants/index";
import { useUser } from "@clerk/nextjs";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { checkUserCompany, createCompany } from "@/server/actions/companies";
import { companyCreateSchema } from "@/server/zod-schemas/create-company";
import { useToast } from "@/components/hooks/use-toast";
import { updateOnboardingStep } from "@/server/actions/onboarding";
import { onboardingStepSchemas } from "@/server/zod-schemas/onboarding";
import type {
  UserRole,
  TeamSize,
  Industry,
  LocationCount,
  City,
  Country,
} from "@/server/actions/onboarding";

// Add type for form data
type FormData = {
  role?: UserRole;
  teamSize?: TeamSize;
  industry?: Industry;
  locationCount?: LocationCount;
  headquarterCity?: City;
  headquarterCountry?: Country;
  email?: string;
};

// Add type for company check result
type CompanyCheckResult =
  | {
      success: true;
      requiresCompanyCreation: true;
      companyDomain: string;
    }
  | {
      success: false;
      error: string;
    };

function getFieldName(step: number): string {
  switch (step) {
    case 3:
      return "role";
    case 4:
      return "teamSize";
    case 5:
      return "industry";
    case 6:
      return "locationCount";
    case 7:
      return "headquarters";
    case 8:
      return "email";
    default:
      return "";
  }
}

function getStepSchema(step: number) {
  const stepToKey = [
    "userPosition",
    "companySize",
    "companyIndustry",
    "companyLocations",
    "headquarters",
    "teamInvites",
  ] as const;

  return step >= 3 && step <= 8
    ? onboardingStepSchemas[stepToKey[step - 3]]
    : null;
}

const OnboardingCard = () => {
  const { user } = useUser();
  const { toast } = useToast();
  const [step, setStep] = useState(1);
  const [companyCheckStatus, setCompanyCheckStatus] = useState<{
    checking: boolean;
    requiresCreation: boolean;
    companyDomain?: string;
  }>({
    checking: true,
    requiresCreation: false,
  });

  const [isNextButtonDisabled, setIsNextButtonDisabled] = useState(true);
  const [companyName, setCompanyName] = useState<string>("");
  const [isLoading, setIsLoading] = useState(false);
  const [isDashboardLoading, setIsDashboardLoading] = useState(false);

  const companyForm = useForm({
    resolver: zodResolver(companyCreateSchema),
    defaultValues: {
      companyName: "",
      companyDomain: "",
    },
  });

  // Update form initialization
  // const form = useForm<FormData>({
  //   resolver: zodResolver(getStepSchema(step) || companyCreateSchema),
  //   defaultValues: {
  //     role: "",
  //     teamSize: "",
  //     industry: "",
  //     locationCount: "",
  //     headquarterCity: "",
  //     headquarterCountry: "",
  //     email: "",
  //   },

  // Add a state to store all form answers
  const [formAnswers, setFormAnswers] = useState<FormData>({
    role: undefined,
    teamSize: undefined,
    industry: undefined,
    locationCount: undefined,
    headquarterCity: undefined,
    headquarterCountry: undefined,
    email: undefined,
  });

  // Add loading state
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Modify handleStepSubmit to save answers
  const handleStepSubmit = async (data: FormData) => {
    setIsSubmitting(true);
    if (!user?.id) return;

    try {
      if (!user?.id) {
        toast({
          title: "Error",
          description: "User not authenticated",
          variant: "destructive",
        });
        return;
      }

      // Show loading state
      setIsLoading(true);

      // Cast the form data to match OnboardingData type
      const result = await updateOnboardingStep(
        step,
        {
          role: data.role,
          teamSize: data.teamSize,
          industry: data.industry,
          locationCount: data.locationCount,
          headquarterCity: data.headquarterCity,
          headquarterCountry: data.headquarterCountry,
          invites: data.email ? [data.email] : undefined,
        },
        user.id,
      );

      if (result.success) {
        // Save current answers
        setFormAnswers((prev) => ({ ...prev, ...data }));

        // Update step
        setStep(step + 1);

        toast({
          title: "Success",
          description: "Your preferences have been saved",
        });
      } else {
        toast({
          title: "Error",
          description: result.error || "Failed to update preferences",
          variant: "destructive",
        });
      }
    } catch (error) {
      console.error("Error updating step:", error);
      toast({
        title: "Error",
        description: "An unexpected error occurred",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
    setIsSubmitting(false);
  };

  // Update form initialization to use saved answers
  const form = useForm<FormData>({
    resolver: zodResolver(getStepSchema(step) || companyCreateSchema),
    defaultValues: formAnswers, // Use saved answers as default values
    mode: "onChange", // Enable real-time validation
  });

  // Add validation state
  const isStepValid = form.formState.isValid;

  // Update useEffect to reset form values when step changes
  useEffect(() => {
    if (step > 2) {
      form.reset(formAnswers); // Reset form with saved answers
    }
  }, [step, formAnswers, form]);

  useEffect(() => {
    async function checkCompany() {
      if (!user?.emailAddresses[0]?.emailAddress) return;

      try {
        const result = (await checkUserCompany(
          user.emailAddresses[0].emailAddress,
        )) as CompanyCheckResult;

        if (result.success && result.requiresCompanyCreation) {
          setCompanyCheckStatus({
            checking: false,
            requiresCreation: true,
            companyDomain: result.companyDomain,
          });
          companyForm.setValue("companyDomain", result.companyDomain);
        } else {
          setStep(3);
          setCompanyCheckStatus({
            checking: false,
            requiresCreation: false,
          });
          if (!result.success) {
            toast({
              title: "Error",
              description: result.error || "Failed to check company status",
              variant: "destructive",
            });
          }
        }
      } catch (error) {
        // Handle redirect error specifically
        if (error instanceof Error && error.message === "NEXT_REDIRECT") {
          // Let Next.js handle the redirect
          throw error;
        }

        console.error("Error checking company:", error);
        setCompanyCheckStatus({
          checking: false,
          requiresCreation: false,
        });
        toast({
          title: "Error",
          description: "Failed to check company status",
          variant: "destructive",
        });
      }
    }

    if (step === 1) {
      checkCompany();
    }

    if (step === 2) {
      setIsLoading(true);
      setIsNextButtonDisabled(true);

      const timer = setTimeout(() => {
        setIsLoading(false);
        setIsNextButtonDisabled(false);
      }, 2000);

      return () => clearTimeout(timer);
    }
  }, [user, step, toast, companyForm]);

  const onCreateCompany = async (data: { companyName: string }) => {
    if (!companyCheckStatus.companyDomain) {
      toast({
        title: "Error",
        description: "Company domain is required",
        variant: "destructive",
      });
      return;
    }

    setIsSubmitting(true);
    try {
      const result = await createCompany({
        companyName: data.companyName,
        companyDomain: companyCheckStatus.companyDomain,
      });

      if (result.success) {
        setCompanyName(data.companyName);
        setStep(2);

        // Add delay before moving to next step
        setTimeout(() => {
          setStep(3);
        }, 2000);

        toast({
          title: "Success",
          description: "Company created successfully",
        });
      } else {
        throw new Error(result.error || "Failed to create company");
      }
    } catch (error) {
      console.error("Error creating company:", error);
      toast({
        title: "Error",
        description:
          error instanceof Error
            ? error.message
            : "An unexpected error occurred",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  // const handleStepSubmit = async (data: FormData) => {
  //   console.log("Form submitted with data:", data);
  //   if (!user?.id) {
  //     console.log("User ID not found");
  //     return;
  //   }

  //   try {
  //     console.log("Calling updateOnboardingStep with:", {
  //       step,
  //       data,
  //       userId: user.id,
  //     });
  //     const result = await updateOnboardingStep(step, data, user.id);

  //     if (result.success) {
  //       console.log("Step updated successfully");
  //       setStep(step + 1);
  //     } else {
  //       console.log("Failed to update step:", result.error);
  //       toast({
  //         title: "Error",
  //         description: result.error || "Failed to update",
  //         variant: "destructive",
  //       });
  //     }
  //   } catch (error) {
  //     console.error("Error updating step:", error);
  //     toast({
  //       title: "Error",
  //       description: "An unexpected error occurred",
  //       variant: "destructive",
  //     });
  //   }
  // };

  // Add this function to handle going back
  const handleBack = () => {
    // Get the previous step's field name
    const prevFieldName = getFieldName(step - 1) as keyof FormData;

    // For step 7 (headquarters), handle both city and country
    if (step === 7) {
      if (formAnswers.headquarterCity) {
        form.setValue("headquarterCity", formAnswers.headquarterCity);
      }
      if (formAnswers.headquarterCountry) {
        form.setValue("headquarterCountry", formAnswers.headquarterCountry);
      }
    } else {
      // For other steps with single select
      const savedValue = formAnswers[prevFieldName];
      if (savedValue) {
        form.setValue(prevFieldName, savedValue);
      }
    }

    // Reset validation state
    form.trigger(prevFieldName);

    setStep(step - 1);
  };

  return (
    <>
      <OnboardingProgressBar step={step} totalSteps={steps.length} />
      <div className="flex flex-col">
        <Card
          className={`m-auto flex h-auto min-h-[500px] w-[95%] flex-col p-3 drop-shadow-2xl sm:w-[450px] sm:p-5 ${
            step === 9 ? "sm:w-[600px]" : ""
          }`}
        >
          <CardHeader
            className={`px-2 sm:px-4 ${step <= 2 || step === 9 ? "text-center" : " "}`}
          >
            <CardTitle className="leading-normal">
              {(step <= 2 || step === 9) && steps[step - 1].img && (
                <div className="flex items-center justify-center pb-0">
                  <Image
                    src={steps[step - 1].img as string}
                    alt="Celebrate image"
                    className="dark:invert"
                    width={45}
                    height={45}
                    priority
                  />
                </div>
              )}
              <div className="flex flex-col gap-1">
                <span className="text-balance text-lg tracking-normal">
                  {step === 1 && companyCheckStatus.requiresCreation
                    ? `Hi ${user?.firstName}!`
                    : step === 2 && !isLoading
                      ? `Great work, ${user?.firstName}!`
                      : steps[step - 1].tagline}
                </span>
                <span className="h1-custom text-balance font-bold">
                  {step === 1 && companyCheckStatus.requiresCreation
                    ? "Welcome to Wakari!"
                    : step === 2 && !isLoading
                      ? `${companyName} has been set up successfully.`
                      : steps[step - 1].title}
                </span>
              </div>
            </CardTitle>
            <CardDescription>
              <span className="h5-custom text-balance">
                {step === 1 && companyCheckStatus.requiresCreation
                  ? "You're the first person from your company to join. Please set up your company profile."
                  : step === 2 && !isLoading
                    ? "Let's continue setting up your workspace."
                    : steps[step - 1].content}
              </span>
            </CardDescription>
          </CardHeader>

          <CardContent className="flex flex-grow px-4">
            {step === 1 && companyCheckStatus.checking ? (
              <div className="flex h-full w-full items-center justify-center pt-2">
                <LoadingSpinner className="custom-orange-text h-14 w-14" />
              </div>
            ) : step === 1 && companyCheckStatus.requiresCreation ? (
              <form
                onSubmit={(e) => {
                  e.preventDefault();
                  companyForm.handleSubmit((data) => onCreateCompany(data))(e);
                }}
                className="flex w-full flex-grow flex-col"
              >
                <div className="space-y-4">
                  <div className="flex flex-col space-y-1.5">
                    <Label htmlFor="companyName">Company Name</Label>
                    <Input
                      id="companyName"
                      placeholder="Enter your company name"
                      {...companyForm.register("companyName")}
                    />
                    {companyForm.formState.errors.companyName && (
                      <p className="text-sm text-red-500">
                        {companyForm.formState.errors.companyName.message}
                      </p>
                    )}
                  </div>
                </div>
                <div className="mt-auto">
                  <Button
                    type="submit"
                    className="w-full"
                    disabled={companyForm.formState.isSubmitting}
                  >
                    {companyForm.formState.isSubmitting
                      ? "Saving..."
                      : "Continue"}
                  </Button>
                </div>
              </form>
            ) : step === 2 ? (
              <div className="flex h-full w-full flex-col items-center justify-center space-y-4 text-center">
                {isLoading ? (
                  <LoadingSpinner className="custom-orange-text h-14 w-14" />
                ) : null}
              </div>
            ) : step > 2 && step < 9 ? (
              <form
                id="onboarding-form"
                onSubmit={form.handleSubmit(handleStepSubmit)}
                className="w-full"
              >
                <div className="grid w-full items-center gap-4">
                  {step === 8 ? (
                    <div className="flex flex-col space-y-1.5">
                      <Label htmlFor="email">Email Address (Optional)</Label>
                      <Input
                        id="email"
                        placeholder="Enter an email address"
                        {...form.register("email")}
                      />
                      {form.formState.errors.email && (
                        <p className="text-sm text-red-500">
                          {form.formState.errors.email.message as string}
                        </p>
                      )}
                      <Button
                        type="button"
                        variant="outline"
                        onClick={() => setStep(step + 1)}
                        className="mt-4"
                      >
                        Skip for now
                      </Button>
                    </div>
                  ) : step === 7 ? (
                    <>
                      <div className="flex flex-col space-y-1.5">
                        <Label htmlFor="headquarterCity">City</Label>
                        <Select
                          onValueChange={(value) =>
                            form.setValue("headquarterCity", value as any, {
                              shouldValidate: true,
                            })
                          }
                        >
                          <SelectTrigger id="headquarterCity">
                            <SelectValue placeholder="Select city" />
                          </SelectTrigger>
                          <SelectContent position="popper">
                            {steps[step - 1].selectOptions.map((option) => (
                              <SelectItem
                                key={option.value}
                                value={option.value}
                              >
                                {option.label}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        {form.formState.errors.headquarterCity && (
                          <p className="text-sm text-red-500">
                            {
                              form.formState.errors.headquarterCity
                                .message as string
                            }
                          </p>
                        )}
                      </div>

                      <div className="flex flex-col space-y-1.5">
                        <Label htmlFor="headquarterCountry">
                          {steps[step - 1].additionalinputlabel}
                        </Label>
                        <Select
                          onValueChange={(value) =>
                            form.setValue("headquarterCountry", value as any, {
                              shouldValidate: true,
                            })
                          }
                        >
                          <SelectTrigger id="headquarterCountry">
                            <SelectValue placeholder="Select country" />
                          </SelectTrigger>
                          <SelectContent position="popper">
                            {steps[step - 1].additionalselectOptions?.map(
                              (option) => (
                                <SelectItem
                                  key={option.value}
                                  value={option.value}
                                >
                                  {option.label}
                                </SelectItem>
                              ),
                            )}
                          </SelectContent>
                        </Select>
                        {form.formState.errors.headquarterCountry && (
                          <p className="text-sm text-red-500">
                            {
                              form.formState.errors.headquarterCountry
                                .message as string
                            }
                          </p>
                        )}
                      </div>
                    </>
                  ) : (
                    <div className="flex flex-col space-y-1.5">
                      <Label htmlFor={getFieldName(step)}>
                        {steps[step - 1].inputlabel}
                      </Label>
                      <Select
                        onValueChange={(value) =>
                          form.setValue(
                            getFieldName(step) as keyof FormData,
                            value as any,
                            {
                              shouldValidate: true,
                            },
                          )
                        }
                      >
                        <SelectTrigger id={getFieldName(step)}>
                          <SelectValue placeholder="Select" />
                        </SelectTrigger>
                        <SelectContent position="popper">
                          {steps[step - 1].selectOptions.map((option) => (
                            <SelectItem key={option.value} value={option.value}>
                              {option.label}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                      {form.formState.errors[
                        getFieldName(step) as keyof typeof form.formState.errors
                      ] && (
                        <p className="text-sm text-red-500">
                          {
                            form.formState.errors[
                              getFieldName(
                                step,
                              ) as keyof typeof form.formState.errors
                            ]?.message as string
                          }
                        </p>
                      )}
                    </div>
                  )}

                  <div className="mt-2 flex items-start justify-start space-x-2 rounded-md bg-gray-100 p-4">
                    <div className="flex-shrink-0">
                      {steps[step - 1].helpicon &&
                        React.createElement(steps[step - 1].helpicon ?? "div")}
                    </div>
                    <div className="flex-grow">
                      <Label
                        htmlFor="help"
                        className="flex items-center text-sm font-bold text-gray-700"
                      >
                        {steps[step - 1].helptitle}
                      </Label>
                      <p className="text-xs text-gray-700">
                        {steps[step - 1].helpcontent}
                      </p>
                    </div>
                  </div>
                </div>
              </form>
            ) : null}

            {step === 9 && (
              <div className="flex w-full items-center justify-center">
                <iframe
                  src={steps[step - 1].video}
                  width="100%"
                  height="200"
                  loading="lazy"
                  frameBorder="0"
                  allowFullScreen
                  className="max-w-[600px]"
                />
              </div>
            )}
          </CardContent>

          <CardFooter className="relative mt-auto items-end justify-between px-4 pb-6">
            {step > 2 && (
              <Button variant="outline" onClick={handleBack}>
                Back
              </Button>
            )}
            {step === 9 ? (
              <Link href="/dashboard/home">
                <Button
                  className="w-full"
                  onClick={() => setIsDashboardLoading(true)}
                  disabled={isDashboardLoading}
                >
                  {isDashboardLoading ? (
                    <>
                      <LoadingSpinner className="mr-2 h-4 w-4" />
                      Redirecting to dashboard...
                    </>
                  ) : (
                    "Go to Dashboard"
                  )}
                </Button>
              </Link>
            ) : (
              step !== 1 && (
                <Button
                  type={step > 2 ? "submit" : "button"}
                  onClick={step <= 2 ? () => setStep(step + 1) : undefined}
                  className={step <= 2 ? "w-full" : ""}
                  disabled={
                    (step === 2 && isNextButtonDisabled) ||
                    (step > 2 && !isStepValid)
                  }
                  form={step > 2 ? "onboarding-form" : undefined}
                >
                  {isSubmitting ? (
                    <LoadingSpinner className="mr-2 h-4 w-4" />
                  ) : null}
                  {isSubmitting ? "Saving..." : "Next"}
                </Button>
              )
            )}
          </CardFooter>
        </Card>
      </div>
    </>
  );
};

export default OnboardingCard;
