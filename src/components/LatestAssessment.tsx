"use client";

import React from "react";
import { Button } from "@/components/ui/button";
import {
  ListCheck,
  CalendarDays,
  Users,
  MapPin,
  Clock,
  ChevronRight,
} from "lucide-react";
import { <PERSON>, <PERSON>H<PERSON>er, CardT<PERSON>le, CardContent } from "@/components/ui/card";
import { <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Axis } from "recharts";
import { ChartContainer } from "@/components/ui/chart";
import { type Review } from "@/types";
import LatestAssessmentEmpty from "./LatestAssessmentEmpty";
import Link from "next/link";
import { useState } from "react";
import { Loader2 } from "lucide-react";

interface LatestAssessmentProps {
  review: Review | null;
  latestAssessmentData: Array<{
    title: string;
    value: number;
    total: number;
  }>;
}

// Helper function to format date consistently
const formatDate = (date: Date) => {
  return new Date(date).toLocaleDateString("en-GB", {
    day: "2-digit",
    month: "2-digit",
    year: "numeric",
  });
};

const LatestAssessment = ({
  review,
  latestAssessmentData,
}: LatestAssessmentProps) => {
  const [isLoading, setIsLoading] = useState(false);

  if (!review) {
    return <LatestAssessmentEmpty />;
  }

  return (
    <div className="w-full">
      <div className="flex items-center justify-between">
        <h3 className="text-base font-bold sm:text-lg">Latest Review</h3>
        <Link
          href="/reviews/manage"
          className="text-xs text-primary hover:underline sm:text-sm"
        >
          (View All Reviews)
        </Link>
      </div>

      <div className="flex flex-1 flex-col gap-4 py-4 lg:gap-6">
        <Card className="custom-primary-outline h-full w-full rounded-lg border bg-primary/10 shadow-sm md:pb-3 lg:pb-4">
          <CardHeader>
            <CardTitle>
              <div className="flex items-center justify-between">
                <div className="text-base font-bold tracking-wide sm:text-[18px]">
                  {review.title}
                </div>
                <Link
                  href={`/reviews/${review.id}${review.status.toLowerCase() === "completed" ? "/report" : "/review"}`}
                  onClick={() => setIsLoading(true)}
                >
                  <Button
                    className="w-auto bg-primary text-primary-foreground hover:bg-primary/90"
                    disabled={isLoading}
                  >
                    {isLoading ? (
                      <Loader2 className="mr-1 h-4 w-4 animate-spin" />
                    ) : null}
                    View{" "}
                    {review.status.toLowerCase() === "completed"
                      ? "Report"
                      : "Review"}
                    <ChevronRight className="ml-1 h-4 w-4" />
                  </Button>
                </Link>
              </div>

              <div className="flex flex-wrap gap-2 text-sm sm:mt-1 sm:flex-row sm:space-x-4">
                <span className="flex items-center text-sm text-gray-700">
                  <ListCheck className="mr-2 h-4 w-4" />
                  <span className="capitalize">{review.type}</span>
                </span>
                <span className="flex items-center text-sm text-gray-700">
                  <CalendarDays className="mr-2 h-4 w-4" />
                  {formatDate(review.createdAt)}
                </span>
                <span className="flex items-center text-sm capitalize text-gray-700">
                  <Clock className="mr-2 h-4 w-4" />
                  {review.status}
                </span>
              </div>
            </CardTitle>
          </CardHeader>

          <CardContent className="grid gap-4 pt-3 sm:gap-6">
            {latestAssessmentData.map(
              (
                item: { title: string; value: number; total: number },
                index: number,
              ) => (
                <div key={index} className="grid auto-rows-min gap-2">
                  <div className="flex items-baseline justify-between px-1 text-sm tabular-nums leading-none">
                    {item.title}
                    <span className="text-sm font-bold">
                      {item.value}/{item.total}
                    </span>
                  </div>
                  <ChartContainer
                    config={{
                      steps: {
                        label: "Steps",
                        color: "hsl(var(--chart-1))",
                      },
                    }}
                    className="aspect-auto h-[10px] w-full"
                  >
                    <BarChart
                      accessibilityLayer
                      layout="vertical"
                      width={100}
                      height={12}
                      data={[
                        {
                          value: item.value,
                          remaining: item.total - item.value,
                        },
                      ]}
                      stackOffset="none"
                    >
                      <Bar
                        dataKey="value"
                        fill="var(--color-steps)"
                        radius={4}
                        stackId="a"
                        barSize={12}
                      />
                      <Bar
                        dataKey="remaining"
                        fill="hsl(var(--muted-foreground) / 0.2)"
                        radius={4}
                        stackId="a"
                        barSize={12}
                      />
                      <XAxis type="number" domain={[0, item.total]} hide />
                      <YAxis type="category" hide />
                    </BarChart>
                  </ChartContainer>
                </div>
              ),
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default LatestAssessment;
