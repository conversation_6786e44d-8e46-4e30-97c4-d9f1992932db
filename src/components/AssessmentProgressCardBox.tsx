import React from "react";
import AssessmentProgressCard from "@/components/AssessmentProgressCard";
import { iconMap } from "@/constants/index";
import { LucideIcon } from "lucide-react";

interface AssessmentProgressCardBoxProps {
  title: string;
  assignedTo: string;
  progress: number;
  total: number;
  isActive?: boolean;
  onClick?: () => void;
}

export default function AssessmentProgressCardBox({
  title,
  assignedTo,
  progress,
  total,
  isActive = false,
  onClick,
}: AssessmentProgressCardBoxProps) {
  const Icon = iconMap[title];

  return (
    <AssessmentProgressCard
      title={title}
      assignedTo={assignedTo}
      progress={progress}
      total={total}
      icon={Icon as LucideIcon}
      isActive={isActive}
      onClick={onClick}
    />
  );
}
