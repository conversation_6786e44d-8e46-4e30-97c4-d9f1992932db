"use client";

import { getCompanyDetails } from "@/server/actions/settings";
import CompanyDetailsForm from "./CompanyDetailsForm";
import {
  type TeamSize,
  type Industry,
  type LocationCount,
  type City,
  type Country,
} from "@/server/actions/onboarding";

interface SettingsContentProps {
  companyId: string;
  companyName: string;
  companyDomain: string;
}

interface CompanyDetailsData {
  companyName: string;
  companyDomain: string;
  teamSize?: TeamSize;
  industry?: Industry;
  locationCount?: LocationCount;
  headquarterCity?: City;
  headquarterCountry?: Country;
}

export default async function SettingsContent({
  companyId,
  companyName,
  companyDomain,
}: SettingsContentProps) {
  const result = await getCompanyDetails(companyId);

  const initialData: CompanyDetailsData = {
    companyName,
    companyDomain,
    teamSize: result.success ? (result.data?.teamSize as TeamSize) : undefined,
    industry: result.success ? (result.data?.industry as Industry) : undefined,
    locationCount: result.success
      ? (result.data?.locationCount as LocationCount)
      : undefined,
    headquarterCity: result.success
      ? (result.data?.headquarterCity as City)
      : undefined,
    headquarterCountry: result.success
      ? (result.data?.headquarterCountry as Country)
      : undefined,
  };

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-navyblue text-2xl font-bold">Company Settings</h1>
        <p className="text-sm text-muted-foreground">
          Manage your company details and preferences.
        </p>
      </div>

      <CompanyDetailsForm companyId={companyId} initialData={initialData} />
    </div>
  );
}
