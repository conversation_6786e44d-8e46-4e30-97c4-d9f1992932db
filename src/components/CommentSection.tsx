"use client";

import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { useToast } from "@/components/hooks/use-toast";
import { addComment } from "@/server/actions/comments";
import { type Comment } from "@/types";
import { LoadingSpinner } from "@/components/LoadingSpinner";

interface CommentSectionProps {
  reviewId: string;
  questionId: string;
  userId: string;
  comments: Comment[];
  onCommentAdded: (comment: Comment) => void;
}

const CommentSection = ({
  reviewId,
  questionId,
  userId,
  comments,
  onCommentAdded,
}: CommentSectionProps) => {
  const [newComment, setNewComment] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);
  const { toast } = useToast();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!newComment.trim()) return;

    setIsSubmitting(true);
    try {
      const result = await addComment({
        reviewId,
        questionId,
        userId,
        text: newComment,
      });

      if (result.success && result.data) {
        setNewComment("");
        onCommentAdded(result.data);
        toast({
          title: "Success",
          description: "Comment added successfully",
        });
      } else {
        toast({
          title: "Error",
          description: result.error ?? "Failed to add comment",
          variant: "destructive",
        });
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "An unexpected error occurred",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="space-y-4">
      <div className="space-y-4">
        {comments.map((comment) => (
          <div
            key={comment.id}
            className="rounded-lg border bg-card p-4 shadow-sm"
          >
            <div className="flex items-center justify-between">
              <span className="font-medium">{comment.userName}</span>
              <span className="text-sm text-muted-foreground">
                {new Date(comment.createdAt).toLocaleDateString("en-GB", {
                  day: "2-digit",
                  month: "2-digit",
                  year: "numeric",
                })}
              </span>
            </div>
            <p className="mt-2 text-sm">{comment.text}</p>
          </div>
        ))}
      </div>

      <form onSubmit={handleSubmit} className="space-y-4">
        <Textarea
          value={newComment}
          onChange={(e) => setNewComment(e.target.value)}
          placeholder="Add a comment..."
          className="min-h-[100px]"
        />
        <Button
          type="submit"
          disabled={isSubmitting || !newComment.trim()}
          className="w-full"
        >
          {isSubmitting ? (
            <>
              <LoadingSpinner className="mr-2 h-4 w-4" />
              Posting...
            </>
          ) : (
            "Post Comment"
          )}
        </Button>
      </form>
    </div>
  );
};

export default CommentSection;
