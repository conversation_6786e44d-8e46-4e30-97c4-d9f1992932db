"use client";

import { useEffect, useState } from "react";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { type TaskWithDetails, assignTask } from "@/server/actions/tasks";
import { useTransition } from "react";
import { getTeamMembers } from "@/server/actions/team";
import { Loader2 } from "lucide-react";

interface TaskAssigneeSelectProps {
  task: TaskWithDetails;
  currentUserId: string;
  companyId: string;
}

interface TeamMember {
  id: string;
  firstName: string;
  lastName: string;
  email: string;
  role: "admin" | "member";
  position: string | null;
  clerkUserId: string;
}

export default function TaskAssigneeSelect({
  task,
  currentUserId,
  companyId,
}: TaskAssigneeSelectProps) {
  const [isPending, startTransition] = useTransition();
  const [teamMembers, setTeamMembers] = useState<TeamMember[]>([]);

  const handleAssigneeChange = (userId: string) => {
    startTransition(async () => {
      await assignTask(task.id, userId);
    });
  };

  useEffect(() => {
    const loadTeamMembers = async () => {
      const result = await getTeamMembers(companyId);
      if (result.success && result.data) {
        setTeamMembers(result.data);
      }
    };
    loadTeamMembers();
  }, [companyId]);

  return (
    <Select
      defaultValue={task.assignedToUserId || "unassigned"}
      onValueChange={handleAssigneeChange}
      disabled={isPending}
    >
      <SelectTrigger className="w-full text-xs sm:w-[180px] sm:text-sm">
        {isPending ? (
          <Loader2 className="mr-2 h-3 w-3 animate-spin sm:h-4 sm:w-4" />
        ) : null}
        <SelectValue placeholder="Unassigned" />
      </SelectTrigger>
      <SelectContent>
        <SelectItem value="unassigned">Unassigned</SelectItem>
        <SelectItem value={currentUserId}>Me</SelectItem>
        {teamMembers
          .filter((member) => member.clerkUserId !== currentUserId)
          .map((member) => (
            <SelectItem key={member.id} value={member.clerkUserId}>
              {member.firstName} {member.lastName}
            </SelectItem>
          ))}
      </SelectContent>
    </Select>
  );
}
