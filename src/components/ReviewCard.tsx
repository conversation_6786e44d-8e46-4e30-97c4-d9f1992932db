"use client";

import {
  Ch<PERSON>ronR<PERSON>,
  CalendarDays,
  <PERSON><PERSON>hart,
  CheckCircle2,
  Circle,
  Timer,
  ListTodo,
  Loader2,
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { type Review } from "@/types";
import { useRouter } from "next/navigation";
import { Progress } from "@/components/ui/progress";
import { useState } from "react";
import { LoadingSpinner } from "@/components/LoadingSpinner";

interface ReviewCardProps {
  review: Review;
  taskStats?: {
    total: number;
    toDo: number;
    inProgress: number;
    completed: number;
    archived: number;
  };
}

const ReviewCard = ({ review, taskStats }: ReviewCardProps) => {
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(false);
  const [isLoadingReport, setIsLoadingReport] = useState(false);

  console.log("ReviewCard Render:", {
    reviewTitle: review.title,
    reviewId: review.id,
    taskStats: {
      total: taskStats?.total || 0,
      toDo: taskStats?.toDo || 0,
      inProgress: taskStats?.inProgress || 0,
      completed: taskStats?.completed || 0,
    },
  });

  const getStatusColor = (status: Review["status"]) => {
    switch (status) {
      case "draft":
        return "text-yellow-600 border-yellow-600";
      case "in_progress":
        return "text-blue-600 border-blue-600";
      case "completed":
        return "text-green-600 border-green-600";
      default:
        return "text-gray-600 border-gray-600";
    }
  };

  const formatDate = (date: Date) => {
    return new Intl.DateTimeFormat("en-GB", {
      day: "2-digit",
      month: "2-digit",
      year: "numeric",
    }).format(new Date(date));
  };

  const getProgressPercentage = () => {
    if (!taskStats?.total) return 0;
    return Math.round((taskStats.completed / taskStats.total) * 100);
  };

  return (
    <div
      className="w-full cursor-pointer rounded-lg border bg-card text-card-foreground shadow-sm transition-all duration-300 hover:border-primary hover:bg-primary/5 hover:shadow-lg"
      onClick={() => router.push(`/tasks?reviewId=${review.id}`)}
    >
      <div className="flex flex-col gap-4 px-4 py-4 sm:flex-row sm:items-center sm:justify-between sm:gap-6 sm:px-6 sm:py-6">
        <div className="flex flex-1 flex-col gap-4 sm:flex-row sm:items-center">
          <div className="flex items-start justify-between gap-4 sm:items-center">
            <div className="flex min-w-0 flex-1 items-center gap-4">
              <div className="flex h-10 w-10 shrink-0 items-center justify-center rounded-full border-2 border-primary bg-white">
                <BarChart className="h-5 w-5 text-primary" />
              </div>
              <div className="min-w-0 flex-1">
                <h3 className="truncate font-semibold">{review.title}</h3>
                <div className="flex flex-col gap-2">
                  <div className="flex items-center gap-2 text-sm text-muted-foreground">
                    <CalendarDays className="h-4 w-4 shrink-0" />
                    <span>{formatDate(review.createdAt)}</span>
                  </div>
                  {taskStats && (
                    <div className="hidden gap-1.5 sm:flex sm:flex-col">
                      <div className="flex flex-wrap items-center gap-2 text-xs sm:gap-4 sm:text-sm">
                        <span className="flex items-center gap-1">
                          <ListTodo className="h-3 w-3 shrink-0 text-gray-500" />
                          {taskStats.total || 0} Total
                        </span>
                        <span className="flex items-center gap-1">
                          <Circle className="h-3 w-3 shrink-0 text-blue-500" />
                          {taskStats.toDo || 0} To Do
                        </span>
                        <span className="flex items-center gap-1">
                          <Timer className="h-3 w-3 shrink-0 text-yellow-500" />
                          {taskStats.inProgress || 0} In Progress
                        </span>
                        <span className="flex items-center gap-1">
                          <CheckCircle2 className="h-3 w-3 shrink-0 text-green-500" />
                          {taskStats.completed || 0} Completed
                        </span>
                      </div>
                      <div className="w-full sm:w-[200px]">
                        <Progress
                          value={getProgressPercentage()}
                          className="h-1.5"
                        />
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </div>

            <Badge
              variant="outline"
              className={`shrink-0 rounded-full capitalize sm:hidden ${getStatusColor(review.status)}`}
            >
              {review.status.replace("_", " ")}
            </Badge>
          </div>

          {taskStats && (
            <div className="flex flex-col gap-1.5 sm:hidden">
              <div className="flex flex-wrap items-center gap-2 text-xs sm:gap-4 sm:text-sm">
                <span className="flex items-center gap-1">
                  <ListTodo className="h-3 w-3 shrink-0 text-gray-500" />
                  {taskStats.total || 0} Total
                </span>
                <span className="flex items-center gap-1">
                  <Circle className="h-3 w-3 shrink-0 text-blue-500" />
                  {taskStats.toDo || 0} To Do
                </span>
                <span className="flex items-center gap-1">
                  <Timer className="h-3 w-3 shrink-0 text-yellow-500" />
                  {taskStats.inProgress || 0} In Progress
                </span>
                <span className="flex items-center gap-1">
                  <CheckCircle2 className="h-3 w-3 shrink-0 text-green-500" />
                  {taskStats.completed || 0} Completed
                </span>
              </div>
              <div className="w-full sm:w-[200px]">
                <Progress value={getProgressPercentage()} className="h-1.5" />
              </div>
            </div>
          )}
        </div>

        <div className="flex items-center justify-end gap-3 sm:gap-4">
          <Badge
            variant="outline"
            className={`hidden shrink-0 rounded-full capitalize sm:inline-flex ${getStatusColor(review.status)}`}
          >
            {review.status.replace("_", " ")}
          </Badge>

          <Button
            size="sm"
            variant="outline"
            className="shrink-0 border-primary text-primary hover:bg-primary/10 hover:text-primary"
            disabled={isLoading}
            onClick={async (e) => {
              e.stopPropagation();
              setIsLoading(true);
              try {
                await new Promise((resolve) => setTimeout(resolve, 500));
                await router.push(`/tasks?reviewId=${review.id}`);
              } catch (error) {
                setIsLoading(false);
                console.error("Navigation failed:", error);
              }
            }}
          >
            {isLoading ? (
              <>
                <Loader2 className="mr-1 h-4 w-4 animate-spin" />
                Loading tasks...
              </>
            ) : (
              <>
                View Tasks
                <ChevronRight className="ml-1 h-4 w-4" />
              </>
            )}
          </Button>
        </div>
      </div>
    </div>
  );
};

export default ReviewCard;
