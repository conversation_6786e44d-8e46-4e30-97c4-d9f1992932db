import { Card } from "@/components/ui/card";
import {
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  XAxis,
  <PERSON><PERSON><PERSON><PERSON>,
  CartesianGrid,
  Tooltip as Re<PERSON>rts<PERSON><PERSON><PERSON>,
  Legend,
  <PERSON>Chart,
  PolarGrid,
  PolarAngleAxis,
  PolarRadiusAxis,
  Radar,
  PieChart,
  Pie,
  Cell,
  Label,
} from "recharts";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { ChartContainer, ChartTooltip } from "@/components/ui/chart";
import {
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { ChartConfig, ChartTooltipContent } from "@/components/ui/chart";
import { Progress } from "@/components/ui/progress";
import { ChevronRight, ListCheck, HelpCircle } from "lucide-react";
import { type RecommendationTable } from "@/server/drizzle/schema";
import { Button } from "@/components/ui/button";
import { ArrowRight } from "lucide-react";
import { use<PERSON>outer } from "next/navigation";
import {
  <PERSON><PERSON><PERSON>,
  Toolt<PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>Provider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { type RAGScore } from "@/types";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
} from "@/components/ui/dialog";
import { useState } from "react";
import {
  CATEGORY_ORDER,
  sortCategoryEntries,
  sortCategories,
} from "@/lib/utils/categories";

interface TaskData {
  title: string;
  value: number;
  total: number;
  color: string;
}

interface ViewBox {
  cx: number;
  cy: number;
}

interface ReportViewProps {
  reviewId: string;
  report: {
    id: string;
    ragScores: RAGScore;
    categoryScores: Record<string, RAGScore>;
    recommendations: Array<{
      id: string;
      questionId: string;
      text: string;
      priority: string;
      estimatedEffort?: string | null;
      category: string;
      question: string;
      description?: string | null;
    }>;
  };
}

const COLORS = {
  green: "#059669",
  amber: "hsl(38 92% 50%)",
  red: "#dc2626",
  gray: "hsl(var(--muted-foreground))",
  muted: "hsl(var(--muted-foreground) / 0.2)",
} as const;

const calculateOverallScore = (scores: RAGScore) => {
  const totalExcludingNA = scores.total - (scores.na || 0);
  return Math.round(
    ((scores.green + scores.amber * 0.5) / totalExcludingNA) * 100,
  );
};

// First, let's define a type for the processed recommendations
interface ProcessedRecommendation {
  category: string;
  question: string;
  text: string;
  priority: string;
  description?: string | null;
}

export default function ReportView({ report }: ReportViewProps) {
  const router = useRouter();
  const [selectedRecommendation, setSelectedRecommendation] =
    useState<ProcessedRecommendation | null>(null);

  // Transform data for bar charts
  const ragData = [
    {
      name: "Overall",
      red: (report.ragScores.red / report.ragScores.total) * 100,
      amber: (report.ragScores.amber / report.ragScores.total) * 100,
      green: (report.ragScores.green / report.ragScores.total) * 100,
      na: (report.ragScores.na || 0 / report.ragScores.total) * 100,
    },
  ];

  const categoryData = sortCategoryEntries(
    Object.entries(report.categoryScores),
  ).map(([category, scores]) => ({
    name: category,
    red: (scores.red / scores.total) * 100,
    amber: (scores.amber / scores.total) * 100,
    green: (scores.green / scores.total) * 100,
    na: (scores.na || 0 / scores.total) * 100,
  }));

  // Transform data for radar chart
  const radarData = sortCategoryEntries(
    Object.entries(report.categoryScores),
  ).map(([category, scores]) => ({
    category,
    score: ((scores.green + scores.amber * 0.5) / scores.total) * 100,
  }));

  // Update the reduce function to use the correct property names
  const recommendationsByCategory = report.recommendations.reduce(
    (acc, rec) => {
      if (!acc[rec.category]) {
        acc[rec.category] = [];
      }
      acc[rec.category].push({
        category: rec.category,
        question: rec.question,
        text: rec.text,
        priority: rec.priority,
        description: rec.description,
      });
      return acc;
    },
    {} as Record<string, ProcessedRecommendation[]>,
  );

  // Get unique categories from the actual recommendations and sort them
  const categories = sortCategories(Object.keys(recommendationsByCategory));

  // Add taskData transformation
  const taskData: TaskData[] = sortCategoryEntries(
    Object.entries(
      report.recommendations.reduce(
        (acc, rec) => {
          const category = rec.category;
          if (!acc[category]) {
            acc[category] = { count: 0, total: 0 };
          }
          acc[category].count += 1;
          acc[category].total = report.recommendations.length;
          return acc;
        },
        {} as Record<string, { count: number; total: number }>,
      ),
    ),
  ).map(([category, data], index) => {
    const chartColors = [
      "hsl(var(--chart-1))",
      "hsl(var(--chart-2))",
      "hsl(var(--chart-3))",
      "hsl(var(--chart-4))",
      "hsl(var(--chart-5))",
    ];

    return {
      title: category,
      value: (data as { count: number; total: number }).count,
      total: (data as { count: number; total: number }).total,
      color: chartColors[index % chartColors.length],
    };
  });

  const ragChartConfig = {
    red: {
      label: "Red",
      color: "hsl(0, 84%, 60%)", // #dc2626
    },
    amber: {
      label: "Amber",
      color: "hsl(38, 92%, 50%)", // #fbbf24
    },
    green: {
      label: "Green",
      color: "hsl(161, 94%, 30%)", // #059669
    },
  } satisfies ChartConfig;

  const categoryChartConfig = {
    red: {
      label: "Red",
      color: "hsl(0, 84%, 60%)",
    },
    amber: {
      label: "Amber",
      color: "hsl(38 92% 50%)",
    },
    green: {
      label: "Green",
      color: "hsl(161, 94%, 30%)",
    },
  } satisfies ChartConfig;

  return (
    <div className="space-y-8">
      {/* Charts section */}
      <div className="space-y-8">
        {/* Overall RAG and Category Overview */}
        <div>
          <div className="mb-4">
            <h2 className="text-lg font-semibold">
              Overall RAG Score Breakdown
            </h2>
          </div>
          <div className="grid gap-4">
            <Card>
              <CardHeader>
                <div className="mt-2 flex items-start gap-4 rounded-md bg-muted/50 p-4 text-sm">
                  <div className="mt-0.5 rounded-full bg-primary/10 p-1">
                    <ListCheck className="h-4 w-4 text-primary" />
                  </div>
                  <div className="space-y-1">
                    <p className="font-medium leading-none">
                      Understanding the scores
                    </p>
                    <p className="text-muted-foreground">
                      The gauge shows overall compliance, weighted by response
                      type:
                      <span className="mx-1 text-[hsl(0,84%,60%)]">
                        Red (0%)
                      </span>
                      •
                      <span
                        className="mx-1"
                        style={{ color: "hsl(38 92% 50%)" }}
                      >
                        Amber (50%)
                      </span>
                      •
                      <span className="mx-1 text-[hsl(161,94%,30%)]">
                        Green (100%)
                      </span>
                    </p>
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <div className="flex flex-col items-center space-y-6">
                  {/* Circular Progress */}
                  {/* <div className="relative flex h-52 w-52 items-center justify-center">
                    <svg className="h-full w-full -rotate-90 transform">
                      <circle
                        className="stroke-current text-muted"
                        strokeWidth="12"
                        stroke="currentColor"
                        fill="transparent"
                        r="90"
                        cx="104"
                        cy="104"
                      />
                      <circle
                        className="stroke-primary transition-all duration-300 ease-in-out"
                        strokeWidth="12"
                        strokeDasharray={`${calculateOverallScore(report.ragScores) * 5.65}, 565.48`}
                        strokeLinecap="round"
                        stroke="currentColor"
                        fill="transparent"
                        r="90"
                        cx="104"
                        cy="104"
                      />
                    </svg>
                    <div className="absolute flex flex-col items-center">
                      <span className="text-3xl font-bold">
                        {calculateOverallScore(report.ragScores)}%
                      </span>
                      <span className="text-sm text-muted-foreground">
                        Overall Score
                      </span>
                    </div>
                  </div> */}

                  {/* Score Breakdown */}
                  <div className="w-full space-y-3">
                    <div className="space-y-1.5">
                      <div className="flex items-center justify-between text-sm">
                        <span className="font-medium">Red</span>
                        <span className="text-muted-foreground">
                          {Math.round(
                            (report.ragScores.red / report.ragScores.total) *
                              100,
                          )}
                          %
                        </span>
                      </div>
                      <Progress
                        value={
                          (report.ragScores.red / report.ragScores.total) * 100
                        }
                        className="h-2 bg-muted"
                        indicatorClassName="bg-[hsl(0,84%,60%)]"
                      />
                    </div>
                    <div className="space-y-1.5">
                      <div className="flex items-center justify-between text-sm">
                        <span className="font-medium">Amber</span>
                        <span className="text-muted-foreground">
                          {Math.round(
                            (report.ragScores.amber / report.ragScores.total) *
                              100,
                          )}
                          %
                        </span>
                      </div>
                      <Progress
                        value={
                          (report.ragScores.amber / report.ragScores.total) *
                          100
                        }
                        className="h-2 bg-muted"
                        indicatorClassName="bg-[hsl(38,92%,50%)]"
                      />
                    </div>
                    <div className="space-y-1.5">
                      <div className="flex items-center justify-between text-sm">
                        <span className="font-medium">Green</span>
                        <span className="text-muted-foreground">
                          {Math.round(
                            (report.ragScores.green / report.ragScores.total) *
                              100,
                          )}
                          %
                        </span>
                      </div>
                      <Progress
                        value={
                          (report.ragScores.green / report.ragScores.total) *
                          100
                        }
                        className="h-2 bg-muted"
                        indicatorClassName="bg-[hsl(161,94%,30%)]"
                      />
                    </div>
                  </div>
                </div>
              </CardContent>
              <CardFooter className="flex-col items-start gap-2 text-sm">
                <div className="leading-none text-muted-foreground">
                  Overall score weighted: Red (0%), Amber (50%), Green (100%)
                </div>
              </CardFooter>
            </Card>
          </div>
        </div>

        {/* Category Scores and Remediation Tasks */}
        <div>
          <div className="mb-4">
            <h2 className="text-lg font-semibold">
              Remediation Tasks by Category
            </h2>
          </div>
          <div className="grid gap-4">
            <Card className="p-6 pb-24 transition-colors hover:bg-muted/50">
              <div className="flex justify-end">
                <Button
                  variant="outline"
                  className="border-primary text-primary hover:bg-primary hover:text-white"
                  onClick={() => router.push(`/tasks?reviewId=${report.id}`)}
                >
                  View Tasks
                  <ChevronRight className="ml-2 h-4 w-4" />
                </Button>
              </div>
              {report.recommendations.length === 0 ? (
                <div className="flex h-full flex-col items-center justify-center space-y-4 text-center">
                  <div className="rounded-full bg-green-100 p-3">
                    <svg
                      className="h-6 w-6 text-green-600"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M5 13l4 4L19 7"
                      />
                    </svg>
                  </div>
                  <h2 className="text-xl font-semibold">Congratulations!</h2>
                  <div className="text-sm text-muted-foreground">
                    You have no tasks to complete.
                    <br />
                    You are fully compliant!
                  </div>
                </div>
              ) : (
                <div className="grid h-full grid-cols-1 gap-6 md:grid-cols-2">
                  {/* Left side: Pie chart */}
                  <div className="flex w-full items-center justify-center">
                    <div className="w-full max-w-[250px]">
                      <ChartContainer
                        config={{
                          tasks: {
                            label: "Tasks",
                            color: "hsl(var(--chart-1))",
                          },
                        }}
                        className="aspect-square w-full"
                      >
                        <PieChart width={250} height={250}>
                          <Pie
                            data={taskData}
                            dataKey="value"
                            nameKey="title"
                            innerRadius={60}
                            outerRadius={100}
                            strokeWidth={0}
                          >
                            {taskData.map((entry, index) => (
                              <Cell key={`cell-${index}`} fill={entry.color} />
                            ))}
                            <Label
                              content={({ viewBox }) => {
                                if (
                                  viewBox &&
                                  "cx" in viewBox &&
                                  "cy" in viewBox
                                ) {
                                  return (
                                    <text
                                      x={viewBox.cx}
                                      y={viewBox.cy}
                                      textAnchor="middle"
                                      dominantBaseline="middle"
                                    >
                                      <tspan
                                        x={viewBox.cx}
                                        y={viewBox.cy}
                                        className="fill-foreground text-3xl font-bold"
                                      >
                                        {report.recommendations.length}
                                      </tspan>
                                      <tspan
                                        x={viewBox.cx}
                                        y={(viewBox.cy || 0) + 24}
                                        className="fill-muted-foreground text-sm"
                                      >
                                        Total Tasks
                                      </tspan>
                                    </text>
                                  );
                                }
                                return null;
                              }}
                            />
                          </Pie>
                        </PieChart>
                      </ChartContainer>
                    </div>
                  </div>

                  {/* Right side: Task progress bars */}
                  <div className="flex w-full flex-col justify-center space-y-4">
                    {taskData.map((task, index) => (
                      <div key={index} className="space-y-2">
                        <div className="flex justify-between text-sm">
                          <span className="font-medium">{task.title}</span>
                          <span className="tabular-nums">
                            {task.value} of {task.total}
                          </span>
                        </div>
                        <div className="h-2 rounded-full bg-muted">
                          <div
                            className="h-2 rounded-full bg-primary"
                            style={{
                              width: `${(task.value / task.total) * 100}%`,
                            }}
                          />
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </Card>
          </div>
        </div>
      </div>

      {/* Recommendations section */}
      <div>
        <h2 className="mb-4 text-lg font-semibold">Recommendations</h2>
        {report.recommendations.length === 0 ? (
          <Card className="p-6">
            <div className="flex flex-col items-center justify-center space-y-4 py-8 text-center">
              <div className="rounded-full bg-green-100 p-3">
                <svg
                  className="h-6 w-6 text-green-600"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M5 13l4 4L19 7"
                  />
                </svg>
              </div>
              <h2 className="text-xl font-semibold">
                No Recommendations Required
              </h2>
              <div className="text-sm text-muted-foreground">
                All compliance requirements have been met successfully.
              </div>
            </div>
          </Card>
        ) : (
          <Tabs defaultValue={categories[0]} className="w-full">
            <TabsList className="mb-4 bg-muted/50">
              {categories.map((category) => (
                <TabsTrigger
                  key={category}
                  value={category}
                  className="data-[state=active]:bg-primary data-[state=active]:text-primary-foreground"
                >
                  {category}
                </TabsTrigger>
              ))}
            </TabsList>
            <Card className="p-6">
              {categories.map((category) => (
                <TabsContent key={category} value={category}>
                  <div className="space-y-6 divide-y divide-border">
                    {recommendationsByCategory[category].map((rec, index) => (
                      <div key={index} className="pt-6 first:pt-0">
                        <div className="mb-2 text-sm text-muted-foreground">
                          {rec.question}
                        </div>
                        <div className="mb-2">
                          {rec.text}
                          {rec.description && (
                            <Button
                              variant="link"
                              className="h-auto px-2 py-0 font-medium text-primary"
                              onClick={() => setSelectedRecommendation(rec)}
                            >
                              Read More
                            </Button>
                          )}
                        </div>
                        <div className="text-sm">
                          Priority:{" "}
                          <span
                            className={`inline-block rounded-full px-2 py-1 text-xs font-semibold ${
                              rec.priority === "high"
                                ? "bg-red-100 text-red-800"
                                : rec.priority === "medium"
                                  ? "bg-yellow-100 text-yellow-800"
                                  : "bg-green-100 text-green-800"
                            }`}
                          >
                            {rec.priority}
                          </span>
                        </div>
                      </div>
                    ))}
                  </div>
                </TabsContent>
              ))}
            </Card>
          </Tabs>
        )}
      </div>

      {/* Recommendation Detail Dialog */}
      <Dialog
        open={!!selectedRecommendation}
        onOpenChange={() => setSelectedRecommendation(null)}
      >
        <DialogContent className="max-h-[90vh] overflow-y-auto p-4 sm:max-w-[500px] sm:p-6">
          <DialogHeader className="mb-4">
            <DialogTitle className="text-lg font-semibold sm:text-xl">
              Detailed Recommendation
            </DialogTitle>
          </DialogHeader>
          <div className="space-y-4">
            <div>
              <h4 className="mb-1 text-sm font-medium sm:text-base">
                Question
              </h4>
              <p className="text-xs text-muted-foreground sm:text-sm">
                {selectedRecommendation?.question}
              </p>
            </div>
            <div>
              <h4 className="mb-1 text-sm font-medium sm:text-base">Summary</h4>
              <p className="text-xs sm:text-sm">
                {selectedRecommendation?.text}
              </p>
            </div>
            <div>
              <h4 className="mb-1 text-sm font-medium sm:text-base">
                Detailed Description
              </h4>
              <p className="whitespace-pre-wrap text-xs sm:text-sm">
                {selectedRecommendation?.description}
              </p>
            </div>
            <div className="flex items-center gap-2 pt-2">
              <span className="text-sm font-medium sm:text-base">
                Priority:
              </span>
              <span
                className={`inline-block rounded-full px-2 py-0.5 text-xs font-semibold ${
                  selectedRecommendation?.priority === "high"
                    ? "bg-red-100 text-red-800"
                    : selectedRecommendation?.priority === "medium"
                      ? "bg-yellow-100 text-yellow-800"
                      : "bg-green-100 text-green-800"
                }`}
              >
                {selectedRecommendation?.priority}
              </span>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
}
