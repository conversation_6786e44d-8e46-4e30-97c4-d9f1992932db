"use client";

import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
} from "@/components/ui/dialog";
import { Textarea } from "@/components/ui/textarea";
import { StarRating } from "@/components/ui/star-rating";
import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";
import { submitFeedback } from "@/server/actions/feedback";
import { useToast } from "@/components/hooks/use-toast";

interface FeedbackDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  type: "post_review" | "voluntary";
}

export function FeedbackDialog({
  open,
  onOpenChange,
  type,
}: FeedbackDialogProps) {
  const { toast } = useToast();
  const [isLoading, setIsLoading] = useState(false);
  const [formData, setFormData] = useState({
    usabilityRating: 0,
    easeOfUseRating: 0,
    helpfulnessRating: 0,
    message: "",
    willingToReview: false,
    willingToPay: false,
  });

  const handleSubmit = async () => {
    setIsLoading(true);
    try {
      await submitFeedback({
        ...formData,
        type,
      });
      toast({
        title: "Thank you for your feedback!",
        description: "Your input helps us improve the product.",
      });
      onOpenChange(false);
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to submit feedback. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-h-[90vh] overflow-y-auto sm:max-w-[600px]">
        <DialogHeader>
          <DialogTitle>Share Your Feedback</DialogTitle>
          <DialogDescription>
            Help us improve your experience by sharing your thoughts.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6 py-4">
          <div className="space-y-4">
            <div className="space-y-2">
              <Label>Usability Rating</Label>
              <StarRating
                value={formData.usabilityRating}
                onChange={(value: number) =>
                  setFormData((prev) => ({ ...prev, usabilityRating: value }))
                }
              />
            </div>

            <div className="space-y-2">
              <Label>Ease of Use</Label>
              <StarRating
                value={formData.easeOfUseRating}
                onChange={(value: number) =>
                  setFormData((prev) => ({ ...prev, easeOfUseRating: value }))
                }
              />
            </div>

            <div className="space-y-2">
              <Label>Helpfulness</Label>
              <StarRating
                value={formData.helpfulnessRating}
                onChange={(value: number) =>
                  setFormData((prev) => ({ ...prev, helpfulnessRating: value }))
                }
              />
            </div>

            <div className="space-y-2">
              <Label>Additional Feedback</Label>
              <Textarea
                value={formData.message}
                onChange={(e) =>
                  setFormData((prev) => ({ ...prev, message: e.target.value }))
                }
                placeholder="Share your thoughts, questions, or suggestions..."
                className="min-h-[100px]"
              />
            </div>

            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <Label>
                  Would you be interested in a comprehensive review?
                </Label>
                <Switch
                  checked={formData.willingToReview}
                  onCheckedChange={(checked: boolean) =>
                    setFormData((prev) => ({
                      ...prev,
                      willingToReview: checked,
                    }))
                  }
                />
              </div>

              <div className="flex items-center justify-between">
                <Label>Would you consider paying for this tool?</Label>
                <Switch
                  checked={formData.willingToPay}
                  onCheckedChange={(checked: boolean) =>
                    setFormData((prev) => ({ ...prev, willingToPay: checked }))
                  }
                />
              </div>
            </div>
          </div>

          <div className="flex justify-end gap-2">
            <Button
              variant="outline"
              onClick={() => onOpenChange(false)}
              disabled={isLoading}
            >
              Cancel
            </Button>
            <Button onClick={handleSubmit} disabled={isLoading}>
              Submit Feedback
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
