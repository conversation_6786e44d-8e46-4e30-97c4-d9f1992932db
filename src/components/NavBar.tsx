import React from "react";
import { But<PERSON> } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuTrigger,
  DropdownMenuContent,
  DropdownMenuLabel,
  DropdownMenuItem,
  DropdownMenuSeparator,
} from "@/components/ui/dropdown-menu";
import { CircleUser } from "lucide-react";
import Link from "next/link";
import Image from "next/image";
import { UserButton } from "@clerk/nextjs";
import { type NavBarProps } from "../../types";

const NavBar: React.FC<NavBarProps> = ({ className }) => {
  return (
    <header
      className={`flex h-14 items-center bg-white px-4 md:px-8 lg:h-[59px] ${className}`}
    >
      <div className="flex w-full items-center justify-between">
        {/* Logo centered on mobile, left on desktop */}
        <div className="flex-shrink-0 lg:flex">
          <Link
            href="/dashboard/home"
            className="absolute left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2 transform lg:static lg:translate-x-0 lg:translate-y-0"
          >
            <Image
              src="/assets/wakari-logo.png"
              alt="Wakari logo"
              width={120}
              height={120}
              className="h-[30px] w-auto"
            />
          </Link>
        </div>

        {/* Empty middle space */}
        <div className="flex-grow"></div>

        {/* UserButton on the far right */}
        <div className="flex-shrink-0">
          <UserButton afterSignOutUrl="/sign-in" />
        </div>
      </div>
    </header>
  );
};

export default NavBar;
