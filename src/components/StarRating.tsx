"use client";

import { Star } from "lucide-react";
import { cn } from "@/lib/utils";

interface StarRatingProps {
  value: number;
  onChange: (value: number) => void;
  max?: number;
}

export function StarRating({ value, onChange, max = 5 }: StarRatingProps) {
  return (
    <div className="flex items-center gap-1">
      {Array.from({ length: max }).map((_, index) => {
        const starValue = index + 1;
        const isFilled = value >= starValue;

        return (
          <button
            key={index}
            type="button"
            onClick={() => onChange(starValue)}
            title={`Rate ${starValue} out of ${max} stars`}
            aria-label={`Rate ${starValue} out of ${max} stars`}
            className={cn(
              "rounded-sm p-0.5 transition-transform hover:scale-110",
              "focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring",
            )}
          >
            <Star
              className={cn(
                "h-6 w-6",
                isFilled
                  ? "fill-primary text-primary"
                  : "fill-none text-muted-foreground",
              )}
            />
          </button>
        );
      })}
    </div>
  );
}
