import React from "react";
import { <PERSON>, CardHeader, CardContent } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { type LucideIcon } from "lucide-react";

interface AssessmentProgressCardProps {
  title: string;
  assignedTo: string;
  progress: number;
  total: number;
  icon?: LucideIcon;
  isActive?: boolean;
  onClick?: () => void;
}

const AssessmentProgressCard: React.FC<AssessmentProgressCardProps> = ({
  title,
  assignedTo,
  progress,
  total,
  icon: Icon,
  isActive = false,
  onClick,
}) => {
  const progressPercentage = (progress / total) * 100;

  return (
    <Card
      className="w-full cursor-pointer border-none shadow-none transition-opacity hover:opacity-80"
      onClick={onClick}
    >
      <CardHeader
        className={`flex flex-row items-center space-x-2 rounded-lg p-2 shadow-none ${
          isActive ? "border border-primary" : "border"
        }`}
      >
        <div className="rounded-md bg-[#f7e0d8] p-2">
          {I<PERSON> && <Icon className="h-6 w-6 text-primary" />}
        </div>
        <div>
          <h2 className="text-sm font-bold">{title}</h2>
          <p className="text-sm text-gray-500">Assigned to {assignedTo}</p>
        </div>
      </CardHeader>
      <CardContent className="px-0 py-1">
        <div className="mb-2 flex items-center justify-between">
          <Progress value={progressPercentage} className="h-2 w-3/4" />
          <span className="custom-navyblue-text text-sm font-bold">
            {progress}/{total}
          </span>
        </div>
      </CardContent>
    </Card>
  );
};

export default AssessmentProgressCard;
