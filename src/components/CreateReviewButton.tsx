"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Loader2, ChevronRight, CirclePlus } from "lucide-react";
import Link from "next/link";
import { useState } from "react";

export default function CreateReviewButton() {
  const [isLoading, setIsLoading] = useState(false);

  return (
    <Link href="/reviews/select" onClick={() => setIsLoading(true)}>
      <Button
        className="bg-primary text-primary-foreground hover:bg-primary/90"
        disabled={isLoading}
      >
        {isLoading ? (
          <>
            <Loader2 className="mr-2 h-4 w-4 animate-spin" />
            Please wait...
          </>
        ) : (
          <>
            <CirclePlus className="mr-2 h-4 w-4" />
            Start Review
          </>
        )}
      </Button>
    </Link>
  );
}
