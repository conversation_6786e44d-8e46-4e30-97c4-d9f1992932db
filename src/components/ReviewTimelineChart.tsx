"use client";

import { Card } from "@/components/ui/card";
import {
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  XAxis,
  <PERSON><PERSON><PERSON><PERSON>,
  Toolt<PERSON>,
  ResponsiveContainer,
} from "recharts";
import { parseISO, format } from "date-fns";

type TimelineData = {
  date: string;
  answers: number;
  comments: number;
  status: number;
  total: number;
  yes: number;
  no: number;
  partially: number;
  na: number;
};

interface ReviewTimelineChartProps {
  timelineData: TimelineData[];
}

export function ReviewTimelineChart({
  timelineData,
}: ReviewTimelineChartProps) {
  console.log("Timeline Data in Chart:", timelineData);

  if (!timelineData || timelineData.length === 0) {
    return (
      <Card className="p-6">
        <h3 className="mb-4 text-lg font-semibold">Changes Timeline</h3>
        <div className="flex h-[300px] items-center justify-center">
          <p className="text-muted-foreground">No changes recorded yet</p>
        </div>
      </Card>
    );
  }

  return (
    <Card className="p-6">
      <h3 className="mb-4 text-lg font-semibold">Changes Timeline</h3>
      <div className="h-[300px] w-full">
        <ResponsiveContainer width="100%" height="100%">
          <BarChart
            data={timelineData}
            margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
          >
            <XAxis
              dataKey="date"
              tickFormatter={(date) => format(parseISO(date), "MMM d")}
            />
            <YAxis />
            <Tooltip
              content={({ active, payload, label }) => {
                if (active && payload && payload.length) {
                  return (
                    <div className="rounded-lg border bg-background p-3 shadow-sm">
                      <p className="font-medium">
                        {format(parseISO(label), "MMMM d, yyyy")}
                      </p>
                      <div className="mt-2 space-y-1">
                        <p className="text-sm font-medium">Scores:</p>
                        <p className="text-sm">
                          <span className="text-green-600">Yes:</span>{" "}
                          {payload.find((p) => p.name === "yes")?.value || 0}
                        </p>
                        <p className="text-sm">
                          <span className="text-red-600">No:</span>{" "}
                          {payload.find((p) => p.name === "no")?.value || 0}
                        </p>
                        <p className="text-sm">
                          <span className="text-amber-600">Partially:</span>{" "}
                          {payload.find((p) => p.name === "partially")?.value ||
                            0}
                        </p>
                        <p className="text-sm">
                          <span className="text-gray-600">N/A:</span>{" "}
                          {payload.find((p) => p.name === "na")?.value || 0}
                        </p>
                      </div>
                    </div>
                  );
                }
                return null;
              }}
            />
            <Bar
              dataKey="yes"
              stackId="a"
              fill="hsl(var(--green-500))"
              name="Yes"
            />
            <Bar
              dataKey="no"
              stackId="a"
              fill="hsl(var(--destructive))"
              name="No"
            />
            <Bar
              dataKey="partially"
              stackId="a"
              fill="hsl(var(--orange-500))"
              name="Partially"
            />
            <Bar
              dataKey="na"
              stackId="a"
              fill="hsl(var(--muted-foreground))"
              name="N/A"
            />
          </BarChart>
        </ResponsiveContainer>
      </div>
    </Card>
  );
}
