"use client";

import { useState } from "react";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Button } from "@/components/ui/button";
import { Trash2, UserPlus } from "lucide-react";
import { useToast } from "@/components/hooks/use-toast";
import {
  inviteCollaborator,
  removeCollaborator,
  updateCollaboratorRole,
} from "@/server/actions/reviews";
import {
  type Collaborator,
  type CollaboratorManagementProps,
  type ApiResponse,
} from "@/types";
import { type Review } from "@/types";

interface InviteParams {
  reviewId: string;
  companyId: string;
  email: string;
  role: "reviewer" | "collaborator";
  inviterName: string;
  reviewTitle: string;
}

const CollaboratorManagement = ({
  reviewId,
  collaborators,
  currentUserId,
  isOwner,
}: CollaboratorManagementProps) => {
  const { toast } = useToast();
  const [loading, setLoading] = useState<string | null>(null);

  const handleInvite = async (email: string, role: "viewer" | "editor") => {
    setLoading("invite");
    try {
      const response = await fetch(`/api/reviews/${reviewId}`);
      const review = (await response.json()) as Review;

      const result = await (inviteCollaborator({
        reviewId,
        email,
        role: role === "viewer" ? "reviewer" : "collaborator",
        inviterName: "User",
        reviewTitle: review.title,
        companyId: review.companyId,
      }) as unknown as Promise<ApiResponse<unknown>>);

      if (result.success) {
        toast({
          title: "Success",
          description: "Collaborator invited successfully",
        });
      } else {
        throw new Error(result.error ?? "Failed to invite collaborator");
      }
    } catch (error) {
      toast({
        title: "Error",
        description:
          error instanceof Error
            ? error.message
            : "Failed to invite collaborator",
        variant: "destructive",
      });
    } finally {
      setLoading(null);
    }
  };

  const handleRemove = async (userId: string) => {
    if (!confirm("Are you sure you want to remove this collaborator?")) return;

    setLoading(userId);
    try {
      const result = await (removeCollaborator(userId) as unknown as Promise<
        ApiResponse<unknown>
      >);

      if (result.success) {
        toast({
          title: "Success",
          description: "Collaborator removed successfully",
        });
      } else {
        throw new Error(result.error ?? "Failed to remove collaborator");
      }
    } catch (error) {
      toast({
        title: "Error",
        description:
          error instanceof Error
            ? error.message
            : "Failed to remove collaborator",
        variant: "destructive",
      });
    } finally {
      setLoading(null);
    }
  };

  const handleUpdateRole = async (
    userId: string,
    newRole: "viewer" | "editor",
  ) => {
    setLoading(userId);
    try {
      const result = await (updateCollaboratorRole(
        userId,
        newRole === "viewer" ? "reviewer" : "collaborator",
      ) as unknown as Promise<ApiResponse<unknown>>);

      if (result.success) {
        toast({
          title: "Success",
          description: "Role updated successfully",
        });
      } else {
        throw new Error(result.error ?? "Failed to update role");
      }
    } catch (error) {
      toast({
        title: "Error",
        description:
          error instanceof Error ? error.message : "Failed to update role",
        variant: "destructive",
      });
    } finally {
      setLoading(null);
    }
  };

  return (
    <div className="space-y-4">
      {isOwner && (
        <Button onClick={() => handleInvite("", "viewer")}>
          <UserPlus className="mr-2 h-4 w-4" />
          Invite Collaborator
        </Button>
      )}

      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>Name</TableHead>
            <TableHead>Email</TableHead>
            <TableHead>Role</TableHead>
            <TableHead className="text-right">Actions</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {collaborators.map((collaborator) => (
            <TableRow key={collaborator.id}>
              <TableCell>{collaborator.name}</TableCell>
              <TableCell>{collaborator.email}</TableCell>
              <TableCell>
                {isOwner && collaborator.userId !== currentUserId ? (
                  <Select
                    defaultValue={collaborator.role}
                    onValueChange={(value: "viewer" | "editor") =>
                      handleUpdateRole(collaborator.userId, value)
                    }
                    disabled={loading === collaborator.userId}
                  >
                    <SelectTrigger className="w-[110px]">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="viewer">Viewer</SelectItem>
                      <SelectItem value="editor">Editor</SelectItem>
                    </SelectContent>
                  </Select>
                ) : (
                  <span className="capitalize">{collaborator.role}</span>
                )}
              </TableCell>
              <TableCell className="text-right">
                {isOwner && collaborator.userId !== currentUserId && (
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => handleRemove(collaborator.userId)}
                    disabled={loading === collaborator.userId}
                  >
                    <Trash2 className="h-4 w-4 text-red-500" />
                  </Button>
                )}
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
  );
};

export default CollaboratorManagement;
