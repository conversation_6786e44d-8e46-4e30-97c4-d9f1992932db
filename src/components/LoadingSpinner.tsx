import { cn } from "@/lib/utils";
import React from "react";

export interface ISVGProps extends React.SVGProps<SVGSVGElement> {
  size?: number;
  className?: string;
}

export const LoadingSpinner = ({
  size = 24,
  className,
  ...props
}: ISVGProps) => {
  return (
    <div className="flex h-full w-full items-center justify-center">
      <div
        className={cn("h-8 w-8 animate-spin rounded-full", className)}
        style={{
          border: "4px solid #f3f3f3",
          borderTop: "4px solid #d6673e",
          borderRadius: "50%",
        }}
      />
    </div>
  );
};
