import React from "react";
import Image from "next/image";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>nt, CardTitle } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";

interface QuestionGuideProps {
  questionGuide?: {
    image: string;
    description: string;
    otherOrganizations?: string[];
  };
}

const QuestionGuideCard: React.FC<QuestionGuideProps> = ({ questionGuide }) => {
  if (!questionGuide) return null;

  const { image, description, otherOrganizations } = questionGuide;

  return (
    <Card className="overflow-hidden">
      <CardHeader>
        <CardTitle className="text-md font-bold">Image Example</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="grid gap-2">
          {image && (
            <Image
              alt="Question Guide Image"
              className="w-full rounded-md object-contain"
              height="300"
              src={image}
              width="300"
              priority
            />
          )}
        </div>
        <Separator className="my-4" />
        <div>
          <h3 className="mb-2 text-lg font-semibold">Description</h3>
          <p className="mb-4 text-sm text-gray-600">{description}</p>
        </div>
        <Separator className="my-4" />
        {otherOrganizations && otherOrganizations.length > 0 && (
          <div>
            <h3 className="mb-2 text-sm font-semibold">
              Other organisations implementing this
            </h3>
            <div className="grid grid-cols-3 gap-2">
              {otherOrganizations.map((org: string, index: number) => (
                <button
                  key={index}
                  aria-label={`View organization ${index + 1}`}
                >
                  <Image
                    alt={`Organization ${index + 1}`}
                    className="aspect-square w-full rounded-md object-cover"
                    height="84"
                    src={org}
                    width="84"
                  />
                </button>
              ))}
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default QuestionGuideCard;
