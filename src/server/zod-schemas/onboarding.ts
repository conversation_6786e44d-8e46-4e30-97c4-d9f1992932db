import { z } from "zod";

export const onboardingStepSchemas = {
  userPosition: z.object({
    role: z.string().min(1, "Please select a role"),
  }),
  companySize: z.object({
    teamSize: z.string().min(1, "Please select a team size"),
  }),
  companyIndustry: z.object({
    industry: z.string().min(1, "Please select an industry"),
  }),
  companyLocations: z.object({
    locationCount: z.string().min(1, "Please select number of locations"),
  }),
  headquarters: z.object({
    headquarterCity: z.string().min(1, "Please select a city"),
    headquarterCountry: z.string().min(1, "Please select a country"),
  }),
  teamInvites: z.object({
    email: z
      .string()
      .email("Please enter a valid email")
      .optional()
      .or(z.literal("")),
    invites: z.array(z.string().email("Please enter a valid email")).optional(),
  }),
} as const;
