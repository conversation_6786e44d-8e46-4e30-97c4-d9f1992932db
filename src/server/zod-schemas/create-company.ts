import { z } from "zod";
import { companies, userCompanies, users } from "@/server/drizzle/schema";

// Zod schema for company creation
export const companyCreateSchema = z.object({
  companyName: z.string().min(1, "Company name is required"),
  companyDomain: z.string().min(1, "Domain is required"),
});

export type Company = typeof companies.$inferSelect;
export type UserCompany = typeof userCompanies.$inferSelect;
export type User = typeof users.$inferSelect;
