import "dotenv/config";

// Define custom environment type
type Environment = "development" | "production" | "local" | undefined;

// Load environment-specific .env file
const env = (process.env.NODE_ENV as Environment) || "local";
const envFile =
  env === "production"
    ? ".env.production"
    : env === "development"
      ? ".env.development"
      : ".env.local";

// Load the appropriate .env file
require("dotenv").config({ path: envFile });

import { testConnection } from "./index";

async function main() {
  console.log("Testing database connection...");
  console.log("Environment:", process.env.NODE_ENV);
  console.log("Using env file:", envFile);
  console.log("Database URL:", process.env.DATABASE_URL);

  const result = await testConnection();
  if (result) {
    console.log("Connection successful!");
  } else {
    console.log("Connection failed!");
    process.exit(1);
  }
}

main().catch(console.error);
