import { neon } from "@neondatabase/serverless";
import { drizzle } from "drizzle-orm/neon-http";
import * as schema from "@/server/drizzle/schema";
import { env } from "@/server/env/server";

if (!env.DATABASE_URL) {
  throw new Error(
    "DATABASE_URL environment variable is not set. Please check your environment-specific .env file",
  );
}

// Validate the URL format
try {
  new URL(env.DATABASE_URL);
} catch (error) {
  throw new Error(
    "DATABASE_URL environment variable is not a valid URL. Please check your environment-specific .env file",
  );
}

const sql = neon(env.DATABASE_URL);
export const db = drizzle(sql, { schema });

export async function testConnection() {
  try {
    const result = await sql`SELECT NOW()`;
    console.log("Database connection successful:", result);
    console.log("Current environment:", process.env.NODE_ENV);
    return true;
  } catch (error) {
    console.error("Database connection failed:", error);
    console.error("Current environment:", process.env.NODE_ENV);
    return false;
  }
}
