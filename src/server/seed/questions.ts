import "dotenv/config";
import { db } from "@/server/db";
import {
  QuestionTable,
  RecommendationTable,
  CategoryTable,
  TaskPriorityEnum,
} from "@/server/drizzle/schema";
// import { assessmentQuestions } from "@/constants";
import { neon } from "@neondatabase/serverless";
import { assessmentQuestions } from "@/constants/index";

// Get DATABASE_URL directly from process.env
const DATABASE_URL = process.env.DATABASE_URL;
if (!DATABASE_URL) {
  throw new Error("DATABASE_URL is not defined");
}

async function seedQuestions() {
  try {
    // Test the connection first
    const sql = neon(DATABASE_URL!);
    const testResult = await sql`SELECT NOW()`;
    console.log("Database connection successful:", testResult);

    console.log("🌱 Cleaning up existing data...");
    // Clean up in reverse order of dependencies
    await db.delete(RecommendationTable);
    await db.delete(QuestionTable);
    await db.delete(CategoryTable);
    console.log("✅ Existing data cleaned up");

    console.log("🌱 Starting seed process...");

    // First, create categories based on unique categories in the questions
    const uniqueCategories = Array.from(
      new Set(assessmentQuestions.map((q) => q.category)),
    );
    const categoryMap = new Map<string, string>();

    console.log("🌱 Creating categories...");
    for (const categoryName of uniqueCategories) {
      const [category] = await db
        .insert(CategoryTable)
        .values({
          name: categoryName,
          description: `Questions related to ${categoryName}`,
          createdAt: new Date(),
          updatedAt: new Date(),
        })
        .returning();

      categoryMap.set(categoryName, category.id);
      console.log(`✅ Created category: ${categoryName}`);
    }

    console.log("🌱 Creating questions and recommendations...");
    for (const question of assessmentQuestions) {
      const categoryId = categoryMap.get(question.category);
      if (!categoryId) {
        throw new Error(`Category not found for question: ${question.text}`);
      }

      // Insert question
      const [insertedQuestion] = await db
        .insert(QuestionTable)
        .values({
          categoryId: categoryId,
          text: question.text,
          description: question.questionGuide.description,
          guideImage: question.questionGuide.image,
          createdAt: new Date(),
          updatedAt: new Date(),
        })
        .returning();

      console.log(`✅ Created question: ${question.text.substring(0, 50)}...`);

      // Insert recommendations
      if (question.recommendations?.length) {
        const recommendationValues = question.recommendations.map((rec) => ({
          questionId: insertedQuestion.id,
          text: rec.text,
          priority: rec.priority.toLowerCase() as keyof typeof TaskPriorityEnum,
          estimatedEffort: rec.effort,
          createdAt: new Date(),
          updatedAt: new Date(),
        }));

        await db.insert(RecommendationTable).values(recommendationValues);

        console.log(
          `✅ Created ${recommendationValues.length} recommendations for question ${insertedQuestion.id}`,
        );
      } else {
        // Create default recommendation if none provided
        await db.insert(RecommendationTable).values({
          questionId: insertedQuestion.id,
          text: `Consider implementing: ${question.text}`,
          priority: "medium",
          estimatedEffort: "2-4 weeks",
          createdAt: new Date(),
          updatedAt: new Date(),
        });

        console.log(
          `✅ Created default recommendation for question ${insertedQuestion.id}`,
        );
      }
    }

    console.log("✅ Seeding completed successfully!");
  } catch (error) {
    console.error("❌ Error seeding database:", error);
    throw error;
  }
}

// Run the seed function
seedQuestions()
  .catch((error) => {
    console.error("Failed to seed database:", error);
    process.exit(1);
  })
  .finally(() => {
    // Close the database connection before exiting
    process.exit(0);
  });
