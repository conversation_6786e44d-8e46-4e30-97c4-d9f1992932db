-- Add composite index for task filtering (based on your tasks.ts queries)
CREATE INDEX idx_tasks_company_status ON tasks(company_id, status);

-- Add composite index for recommendation-based task queries
CREATE INDEX idx_tasks_recommendation_review ON tasks(recommendation_id, review_id, status);

-- Add index for assigned tasks
CREATE INDEX idx_tasks_assigned_status ON tasks(assigned_to_user_id, status, due_date);