-- Add composite index for common review filtering
CREATE INDEX idx_reviews_company_status ON reviews(company_id, status, is_archived);

-- Add index for review collaborator lookups
CREATE INDEX idx_review_collaborators_review_user ON review_collaborators(review_id, user_id);

-- Add index for review answers filtering
CREATE INDEX idx_review_answers_review_question ON review_answers(review_id, question_id, answer);