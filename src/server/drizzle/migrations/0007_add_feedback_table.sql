CREATE TYPE feedback_type AS ENUM ('post_review', 'voluntary');

CREATE TABLE feedbacks (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id TEXT NOT NULL,
  type feedback_type NOT NULL,
  usability_rating INTEGER NOT NULL,
  ease_of_use_rating INTEGER NOT NULL,
  helpfulness_rating INTEGER NOT NULL,
  message TEXT,
  willing_to_review BOOLEAN NOT NULL DEFAULT false,
  willing_to_pay BOOLEAN NOT NULL DEFAULT false,
  created_at TIMESTAMP NOT NULL DEFAULT NOW(),
  updated_at TIMESTAMP NOT NULL DEFAULT NOW()
); 