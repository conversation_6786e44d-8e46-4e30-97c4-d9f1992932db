-- First, create the task_status enum type
DO $$ BEGIN
    CREATE TYPE task_status AS ENUM ('To Do', 'In Progress', 'Completed', 'Archived');
EXCEPTION
    WHEN duplicate_object THEN null;
END $$;

-- Then update the tasks table to use the new enum type
ALTER TABLE tasks 
ALTER COLUMN status TYPE task_status 
USING status::task_status;

-- Add index for better performance
CREATE INDEX IF NOT EXISTS tasks_status_idx ON tasks(status); 