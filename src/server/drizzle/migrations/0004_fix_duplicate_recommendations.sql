-- First, let's find and remove any duplicates
WITH duplicates AS (
  SELECT id, question_id, text,
    ROW_NUMBER() OVER (
      PARTITION BY question_id, text
      ORDER BY created_at
    ) as row_num
  FROM recommendations
)
DELETE FROM recommendations
WHERE id IN (
  SELECT id 
  FROM duplicates 
  WHERE row_num > 1
);

-- Then add a unique constraint to prevent future duplicates
ALTER TABLE recommendations
ADD CONSTRAINT unique_recommendation_per_question 
UNIQUE (question_id, text); 