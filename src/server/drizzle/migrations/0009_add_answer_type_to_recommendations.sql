-- First add the column without NOT NULL constraint
ALTER TABLE recommendations ADD COLUMN answer_type answer_type;

-- Update existing recommendations based on review answers
UPDATE recommendations r
SET answer_type = (
    SELECT ra.answer
    FROM review_answers ra
    WHERE ra.question_id = r.question_id
    ORDER BY ra.created_at DESC
    LIMIT 1
);

-- After populating data, add NOT NULL constraint
ALTER TABLE recommendations 
    ALTER COLUMN answer_type SET NOT NULL;

-- Create tasks for both 'no' and 'partially' answers
INSERT INTO tasks (
    id,
    recommendation_id,
    review_id,
    company_id,
    title,
    description,
    status,
    priority,
    start_date,
    category,
    created_at,
    updated_at
)
SELECT 
    gen_random_uuid(),
    r.id,
    ra.review_id,
    rev.company_id,
    CONCAT('Address: ', q.text) as title,
    r.text as description,
    'To Do' as status,
    r.priority,
    CURRENT_TIMESTAMP as start_date,
    c.name as category,
    CURRENT_TIMESTAMP as created_at,
    CURRENT_TIMESTAMP as updated_at
FROM recommendations r
JOIN questions q ON r.question_id = q.id
JOIN categories c ON q.category_id = c.id
JOIN review_answers ra ON ra.question_id = q.id
JOIN reviews rev ON ra.review_id = rev.id
WHERE ra.answer IN ('no', 'partially')
AND NOT EXISTS (
    SELECT 1 FROM tasks t
    WHERE t.recommendation_id = r.id
); 