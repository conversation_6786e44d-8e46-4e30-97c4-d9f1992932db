-- Add the review_answer_id column if it doesn't exist
DO $$ 
BEGIN
    -- Check if the column doesn't exist before adding it
    IF NOT EXISTS (
        SELECT 1 
        FROM information_schema.columns 
        WHERE table_name = 'tasks' 
        AND column_name = 'review_answer_id'
    ) THEN
        -- Add the column and foreign key constraint
        ALTER TABLE tasks 
        ADD COLUMN review_answer_id UUID REFERENCES review_answers(id);
        
        -- Create the index
        CREATE INDEX idx_tasks_review_answer_id ON tasks(review_answer_id);
    END IF;
END $$;

-- Update existing status values to match the new enum
-- UPDATE tasks SET status = 'To Do' WHERE status = 'todo';
-- UPDATE tasks SET status = 'In Progress' WHERE status = 'in_progress';
-- UPDATE tasks SET status = 'Completed' WHERE status = 'completed';
-- UPDATE tasks SET status = 'Archived' WHERE status = 'archived'; 