import { z } from "zod";
import {
  index,
  timestamp,
  uuid,
  varchar,
  boolean,
  pgTable,
  text,
  integer,
  pgEnum,
  jsonb,
  uniqueIndex,
} from "drizzle-orm/pg-core";
import { relations, type InferModel } from "drizzle-orm";
import { type SQL } from "drizzle-orm";

import {
  roles,
  teamSize,
  industries,
  locationCount,
  cities,
  countries,
} from "@/constants/";

const createdAt = timestamp("createdAt", { withTimezone: true })
  .notNull()
  .defaultNow();
const updatedAt = timestamp("updatedAt", { withTimezone: true })
  .notNull()
  .defaultNow()
  .$onUpdate(() => new Date());

// Enums
export const ReviewTypeEnum = pgEnum("review_type", ["basic", "comprehensive"]);
export const ReviewStatusEnum = pgEnum("review_status", [
  "draft",
  "in_progress",
  "completed",
]);
export const CollaboratorRoleEnum = pgEnum("collaborator_role", [
  "owner",
  "reviewer",
  "collaborator",
]);
export const ReportStatusEnum = pgEnum("report_status", [
  "generated",
  "archived",
]);
export const TaskStatusEnum = pgEnum("task_status", [
  "Draft",
  "To Do",
  "In Progress",
  "Completed",
  "Archived",
]);
export const TaskPriorityEnum = pgEnum("task_priority", [
  "high",
  "medium",
  "low",
]);

// Add type for the options
interface OptionType {
  value: string;
  label: string;
  country?: string;
}

// Define the role values directly in the schema
const roleValues = [
  "executive",
  "director",
  "manager",
  "team_lead",
  "developer",
  "designer",
  "product_manager",
  "project_manager",
  "analyst",
  "consultant",
  "hr",
  "marketing",
  "sales",
  "support",
  "other",
] as const;

const teamSizeValues = ["1-10", "11-50", "50+"] as const;
const industryValues = [
  "technology",
  "financial",
  "healthcare",
  "education",
  "retail",
  "manufacturing",
  "professional_services",
  "hospitality",
  "construction",
  "media",
  "transportation",
  "energy",
  "agriculture",
  "nonprofit",
  "government",
] as const;

const locationCountValues = ["1-5", "11-50", "50+"] as const;
const cityValues = [
  "johannesburg",
  "cape_town",
  "durban",
  "pretoria",
  "lagos",
  "cairo",
  "nairobi",
  "london",
  "paris",
  "berlin",
  "amsterdam",
  "new_york",
  "san_francisco",
  "toronto",
  "tokyo",
  "singapore",
  "dubai",
] as const;

const countryValues = [
  "GB",
  "DE",
  "FR",
  "NL",
  "ES",
  "ZA",
  "NG",
  "KE",
  "EG",
  "GH",
  "US",
  "CA",
  "MX",
  "JP",
  "SG",
  "AE",
  "IN",
  "CN",
  "AU",
  "NZ",
] as const;

// Users table
export const users = pgTable(
  "users",
  {
    id: uuid("id").primaryKey().defaultRandom(),
    firstName: varchar("firstName", { length: 255 }).notNull(),
    lastName: varchar("lastName", { length: 255 }).notNull(),
    email: varchar("email", { length: 255 }).notNull(),
    clerkUserId: text("clerkUserId").notNull().unique(),
    isVerified: boolean("isVerified").notNull().default(true),
    position: text("position", {
      enum: roleValues,
    }),
    createdAt,
    updatedAt,
    isSuperAdmin: boolean().notNull().default(false),
  },
  (table) => ({
    emailIdx: index("email_idx").on(table.email),
    clerkUserIdIdx: index("clerkUserId_idx").on(table.clerkUserId),
    idIdx: index("id_idx").on(table.id),
  }),
);

// Company table
export const companies = pgTable(
  "companies",
  {
    id: uuid("id").defaultRandom().primaryKey(),
    companyName: text("companyName").notNull(),
    companyDomain: text("companyDomain").notNull().unique(),
    createdAt,
    updatedAt,
  },
  (table) => ({
    companyDomainIdx: index("companyDomain_idx").on(table.companyDomain),
  }),
);

// User-Company relationship with roles
export const userCompanies = pgTable(
  "user_companies",
  {
    id: uuid("id").defaultRandom().primaryKey(),
    userId: text("userId").notNull(),
    clerkUserId: text("clerkUserId").notNull().unique(),
    companyId: uuid("companyId").references(() => companies.id),
    role: text("role", { enum: ["admin", "member"] })
      .notNull()
      .default("member"),
    createdAt,
    updatedAt,
  },
  (table) => ({
    userIdIdx: index("userId_idx").on(table.userId),
  }),
);

export const companyDetails = pgTable("company_details", {
  id: uuid("id").defaultRandom().primaryKey(),
  companyId: uuid("companyId").references(() => companies.id),
  teamSize: text("teamSize", {
    enum: teamSizeValues,
  }),
  industry: text("industry", {
    enum: industryValues,
  }),
  locationCount: text("locationCount", {
    enum: locationCountValues,
  }),
  headquarterCity: text("headquarterCity", {
    enum: cityValues,
  }),
  headquarterCountry: text("headquarterCountry", {
    enum: countryValues,
  }),
  createdAt,
  updatedAt,
});

// Reviews Table
export const ReviewTable = pgTable("reviews", {
  id: uuid("id").primaryKey().defaultRandom(),
  companyId: uuid("company_id")
    .notNull()
    .references(() => companies.id),
  title: text("title").notNull(),
  type: ReviewTypeEnum("type").notNull(),
  status: ReviewStatusEnum("status").notNull().default("draft"),
  createdByUserId: text("created_by_user_id").notNull(),
  isArchived: boolean("is_archived").default(false),
  createdAt,
  updatedAt,
});

// Review Collaborators Table
export const ReviewCollaboratorTable = pgTable("review_collaborators", {
  id: uuid("id").primaryKey().defaultRandom(),
  reviewId: uuid("review_id")
    .notNull()
    .references(() => ReviewTable.id, { onDelete: "cascade" }),
  clerkUserId: text("clerk_user_id").notNull(),
  role: CollaboratorRoleEnum("role").notNull(),
  createdAt,
  updatedAt,
});

// Categories Table
export const CategoryTable = pgTable("categories", {
  id: uuid("id").primaryKey().defaultRandom(),
  name: text("name").notNull().unique(),
  description: text("description"),
  createdAt,
  updatedAt,
});

// Questions Table (updated)
export const QuestionTable = pgTable("questions", {
  id: uuid("id").defaultRandom().primaryKey(),
  categoryId: uuid("category_id")
    .notNull()
    .references(() => CategoryTable.id),
  text: text("text").notNull(),
  description: text("description").notNull(),
  guideImage: text("guide_image"),
  isArchived: boolean("is_archived").notNull().default(false),
  createdAt: timestamp("created_at").defaultNow().notNull(),
  updatedAt: timestamp("updated_at").defaultNow(),
});

// Review Answers Table
export const ReviewAnswerTable = pgTable("review_answers", {
  id: uuid("id").defaultRandom().primaryKey(),
  reviewId: uuid("review_id")
    .notNull()
    .references(() => ReviewTable.id),
  questionId: uuid("question_id")
    .notNull()
    .references(() => QuestionTable.id),
  answeredByUserId: text("answered_by_user_id").notNull(),
  answer: text("answer", { enum: ["yes", "no", "partially", "na"] }).notNull(),
  comment: text("comment"),
  createdAt: timestamp("created_at").defaultNow().notNull(),
  updatedAt: timestamp("updated_at").defaultNow(),
});

// RecommendationTable
export const RecommendationTable = pgTable(
  "recommendations",
  {
    id: uuid("id").primaryKey().defaultRandom(),
    questionId: uuid("question_id")
      .notNull()
      .references(() => QuestionTable.id),
    text: text("text").notNull(),
    description: text("description"),
    priority: text("priority").notNull(),
    estimatedEffort: text("estimated_effort"),
    createdAt,
    updatedAt,
  },
  (table) => {
    return {
      unique_recommendation: uniqueIndex(
        "unique_recommendation_per_question",
      ).on(table.questionId, table.text),
    };
  },
);

// TaskTable
export const TaskTable = pgTable("tasks", {
  id: uuid("id").defaultRandom().primaryKey(),
  recommendationId: uuid("recommendation_id").references(
    () => RecommendationTable.id,
  ),
  questionId: uuid("question_id").references(() => QuestionTable.id),
  reviewId: uuid("review_id")
    .notNull()
    .references(() => ReviewTable.id),
  reviewAnswerId: uuid("review_answer_id").references(
    () => ReviewAnswerTable.id,
  ),
  companyId: uuid("company_id")
    .notNull()
    .references(() => companies.id),
  title: text("title").notNull(),
  description: text("description"),
  status: TaskStatusEnum("status").notNull().default("Draft"),
  priority: text("priority", { enum: ["high", "medium", "low"] }).notNull(),
  startDate: timestamp("start_date").notNull(),
  dueDate: timestamp("due_date"),
  category: text("category").notNull(),
  assignedToUserId: text("assigned_to_user_id"),
  createdAt: timestamp("created_at").defaultNow().notNull(),
  updatedAt: timestamp("updated_at").defaultNow(),
});

// Add task relations after the tasks table
export const taskRelations = relations(TaskTable, ({ one }) => ({
  review: one(ReviewTable, {
    fields: [TaskTable.reviewId],
    references: [ReviewTable.id],
  }),
  question: one(QuestionTable, {
    fields: [TaskTable.questionId],
    references: [QuestionTable.id],
  }),
  recommendation: one(RecommendationTable, {
    fields: [TaskTable.recommendationId],
    references: [RecommendationTable.id],
  }),
  answer: one(ReviewAnswerTable, {
    fields: [TaskTable.reviewAnswerId],
    references: [ReviewAnswerTable.id],
  }),
}));

// Comments Table
export const CommentTable = pgTable("comments", {
  id: uuid("id").primaryKey().defaultRandom(),
  reviewId: uuid("review_id")
    .notNull()
    .references(() => ReviewTable.id, { onDelete: "cascade" }),
  questionId: uuid("question_id")
    .notNull()
    .references(() => QuestionTable.id),
  userId: text("user_id").notNull(),
  parentId: uuid("parent_id"),
  text: text("text").notNull(),
  edited: boolean("edited").notNull().default(false),
  createdAt,
  updatedAt,
});

// Add the self-reference after table definition
export const commentRelations = relations(CommentTable, ({ one }) => ({
  parent: one(CommentTable, {
    fields: [CommentTable.parentId],
    references: [CommentTable.id],
  }),
  user: one(users, {
    fields: [CommentTable.userId],
    references: [users.clerkUserId],
  }),
  review: one(ReviewTable, {
    fields: [CommentTable.reviewId],
    references: [ReviewTable.id],
  }),
  question: one(QuestionTable, {
    fields: [CommentTable.questionId],
    references: [QuestionTable.id],
  }),
}));

// Invitations Table
export const InvitationTable = pgTable("invitations", {
  id: uuid("id").primaryKey().defaultRandom(),
  reviewId: uuid("review_id")
    .notNull()
    .references(() => ReviewTable.id, { onDelete: "cascade" }),
  companyId: uuid("company_id")
    .notNull()
    .references(() => companies.id),
  email: text("email").notNull(),
  role: CollaboratorRoleEnum("role").notNull(),
  token: text("token").notNull().unique(),
  status: text("status", { enum: ["pending", "accepted", "expired"] })
    .notNull()
    .default("pending"),
  createdAt,
  updatedAt,
});

// Notifications Table
export const NotificationTable = pgTable("notifications", {
  id: uuid("id").primaryKey().defaultRandom(),
  userId: text("user_id").notNull(),
  reviewId: uuid("review_id")
    .notNull()
    .references(() => ReviewTable.id, { onDelete: "cascade" }),
  questionId: uuid("question_id")
    .notNull()
    .references(() => QuestionTable.id),
  commentId: uuid("comment_id")
    .notNull()
    .references(() => CommentTable.id, { onDelete: "cascade" }),
  type: text("type", { enum: ["comment", "mention", "reply"] }).notNull(),
  read: boolean("read").notNull().default(false),
  createdAt,
  updatedAt,
});

// Reports Table
export const ReportTable = pgTable("reports", {
  id: uuid("id").primaryKey().defaultRandom(),
  reviewId: uuid("review_id")
    .notNull()
    .references(() => ReviewTable.id, { onDelete: "cascade" }),
  companyId: uuid("company_id")
    .notNull()
    .references(() => companies.id),
  status: ReportStatusEnum("status").notNull().default("generated"),
  ragScores: jsonb("rag_scores")
    .$type<{
      red: number;
      amber: number;
      green: number;
      total: number;
    }>()
    .notNull(),
  categoryScores: jsonb("category_scores")
    .$type<
      Record<
        string,
        {
          red: number;
          amber: number;
          green: number;
          total: number;
        }
      >
    >()
    .notNull(),
  generatedBy: text("generated_by").notNull(),
  createdAt,
  updatedAt,
});

// Relations
export const reviewRelations = relations(ReviewTable, ({ many, one }) => ({
  collaborators: many(ReviewCollaboratorTable),
  answers: many(ReviewAnswerTable),
  company: one(companies, {
    fields: [ReviewTable.companyId],
    references: [companies.id],
  }),
}));

export const questionRelations = relations(QuestionTable, ({ one, many }) => ({
  category: one(CategoryTable, {
    fields: [QuestionTable.categoryId],
    references: [CategoryTable.id],
  }),
  answers: many(ReviewAnswerTable),
  recommendations: many(RecommendationTable),
}));

// Zod Schemas
export const reviewSchema = z.object({
  id: z.string().uuid(),
  companyId: z.string().uuid(),
  title: z.string(),
  type: z.enum(["basic", "comprehensive"]),
  status: z.enum(["draft", "in_progress", "completed"]),
  createdByUserId: z.string(),
  createdAt: z.date(),
  updatedAt: z.date(),
});

export const answerSchema = z.object({
  id: z.string().uuid(),
  reviewId: z.string().uuid(),
  questionId: z.string().uuid(),
  answeredByUserId: z.string(),
  answer: z.enum(["yes", "no", "partially", "na"]),
  comment: z.string().optional(),
  createdAt: z.date(),
  updatedAt: z.date(),
});

export type ReviewSchema = z.infer<typeof reviewSchema>;
export type Answer = z.infer<typeof answerSchema>;

// Change the local type declarations to avoid conflicts
export type DbReview = z.infer<typeof reviewSchema>;
export type DbAnswer = z.infer<typeof answerSchema>;
export type DbTask = InferModel<typeof TaskTable>;
export type DbComment = InferModel<typeof CommentTable>;
export type DbNotification = InferModel<typeof NotificationTable>;

// Update the Report interface to use the new type names
export interface Report {
  id: string;
  reviewId: string;
  companyId: string;
  status: "generated" | "archived";
  ragScores: {
    red: number;
    amber: number;
    green: number;
    total: number;
  };
  categoryScores: Record<
    string,
    {
      red: number;
      amber: number;
      green: number;
      total: number;
    }
  >;
  generatedBy: string;
  createdAt: Date;
  updatedAt: Date;
}

// Add ReviewHistoryTable to schema
export const ReviewHistoryTable = pgTable("review_history", {
  id: uuid("id").primaryKey().defaultRandom(),
  reviewId: uuid("review_id")
    .notNull()
    .references(() => ReviewTable.id, { onDelete: "cascade" }),
  changes: jsonb("changes")
    .$type<
      {
        type: "answer" | "comment" | "status";
        userId: string;
        userName: string;
        timestamp: Date;
        details: {
          questionId?: string;
          oldValue?: string;
          newValue: string;
        };
      }[]
    >()
    .notNull(),
  createdAt,
  updatedAt,
});

export const ReviewCommentTable = pgTable("review_comments", {
  id: uuid("id").defaultRandom().primaryKey(),
  reviewId: uuid("review_id")
    .notNull()
    .references(() => ReviewTable.id),
  questionId: uuid("question_id").notNull(),
  text: text("text").notNull(),
  userId: text("user_id").notNull(),
  firstName: text("first_name").notNull(),
  lastName: text("last_name").notNull(),
  createdAt: timestamp("created_at", { withTimezone: true })
    .defaultNow()
    .notNull(),
  updatedAt: timestamp("updated_at", { withTimezone: true }).defaultNow(),
  edited: boolean("edited").default(false),
});

// Add relations
export const reviewCommentsRelations = relations(
  ReviewCommentTable,
  ({ one }) => ({
    review: one(ReviewTable, {
      fields: [ReviewCommentTable.reviewId],
      references: [ReviewTable.id],
    }),
  }),
);

// Add these exports at the bottom of your schema file
export type ReviewWithProgress = typeof ReviewTable.$inferSelect & {
  progressData?: Array<{
    title: string;
    value: number;
    total: number;
  }>;
  collaboratorsCount?: number;
};
export type Task = typeof TaskTable.$inferSelect;

export const feedbacks = pgTable("feedbacks", {
  id: uuid("id").primaryKey().defaultRandom(),
  userId: text("user_id").notNull(),
  type: text("type", { enum: ["post_review", "voluntary"] }).notNull(),
  usabilityRating: integer("usability_rating").notNull(),
  easeOfUseRating: integer("ease_of_use_rating").notNull(),
  helpfulnessRating: integer("helpfulness_rating").notNull(),
  message: text("message"),
  willingToReview: boolean("willing_to_review").notNull().default(false),
  willingToPay: boolean("willing_to_pay").notNull().default(false),
  createdAt,
  updatedAt,
});
