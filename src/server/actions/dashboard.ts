import { db } from "@/server/db/index";
import { eq } from "drizzle-orm";
import {
  ReviewTable,
  TaskTable,
  ReviewAnswerTable,
  QuestionTable,
  CategoryTable,
} from "@/server/drizzle/schema";

export async function getDashboardStats(companyId: string) {
  try {
    // Get all reviews
    const reviews = await db
      .select()
      .from(ReviewTable)
      .where(eq(ReviewTable.companyId, companyId));

    // Get all tasks
    const tasks = await db
      .select()
      .from(TaskTable)
      .where(eq(TaskTable.companyId, companyId));

    // Calculate review stats
    const reviewStats = {
      total: reviews.length,
      drafts: reviews.filter((r) => r.status === "draft").length,
      completed: reviews.filter((r) => r.status === "completed").length,
      lastReviewDate:
        reviews.length > 0
          ? new Date(Math.max(...reviews.map((r) => r.createdAt.getTime())))
          : null,
    };

    // Calculate task stats
    const taskStats = {
      toDo: tasks.filter((t) => t.status === "To Do").length,
      inProgress: tasks.filter((t) => t.status === "In Progress").length,
      completed: tasks.filter((t) => t.status === "Completed").length,
      archived: tasks.filter((t) => t.status === "Archived").length,
    };

    // Get latest assessment data by category
    const categoryStats = await Promise.all(
      (await db.select().from(CategoryTable)).map(async (category) => {
        const questionCount = await db
          .select()
          .from(QuestionTable)
          .where(eq(QuestionTable.categoryId, category.id));

        const answerCount = await db
          .select()
          .from(ReviewAnswerTable)
          .innerJoin(
            QuestionTable,
            eq(ReviewAnswerTable.questionId, QuestionTable.id),
          )
          .where(eq(QuestionTable.categoryId, category.id));

        return {
          categoryName: category.name,
          total: questionCount.length,
          answered: answerCount.length,
        };
      }),
    );

    return {
      success: true,
      data: {
        reviews: reviewStats,
        tasks: taskStats,
        categories: categoryStats,
      },
    };
  } catch (error) {
    console.error("Error fetching dashboard stats:", error);
    return { success: false, error: "Failed to fetch dashboard stats" };
  }
}

export async function getLatestTasks(companyId: string, limit = 5) {
  try {
    const tasks = await db
      .select({
        id: TaskTable.id,
        title: TaskTable.title,
        status: TaskTable.status,
        priority: TaskTable.priority,
        dueDate: TaskTable.dueDate,
        category: TaskTable.category,
        assignedToUserId: TaskTable.assignedToUserId,
      })
      .from(TaskTable)
      .where(eq(TaskTable.companyId, companyId))
      .orderBy(TaskTable.createdAt)
      .limit(limit);

    return { success: true, data: tasks };
  } catch (error) {
    console.error("Error fetching latest tasks:", error);
    return { success: false, error: "Failed to fetch latest tasks" };
  }
}
