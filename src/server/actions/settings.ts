"use server";

import { db } from "@/server/db/index";
import { companies, companyDetails } from "@/server/drizzle/schema";
import { eq } from "drizzle-orm";
import { revalidatePath } from "next/cache";
import { z } from "zod";
import {
  type TeamSize,
  type Industry,
  type LocationCount,
  type City,
  type Country,
} from "@/server/actions/onboarding";

// Create a schema that matches the exact types
const companyDetailsSchema = z.object({
  companyName: z.string().min(1),
  companyDomain: z.string().min(1),
  teamSize: z.enum(["1-10", "11-50", "50+"]).optional(),
  industry: z
    .enum([
      "technology",
      "financial",
      "healthcare",
      "education",
      "retail",
      "manufacturing",
      "professional_services",
      "hospitality",
      "construction",
      "media",
      "transportation",
      "energy",
      "agriculture",
      "nonprofit",
      "government",
    ])
    .optional(),
  locationCount: z.enum(["1-5", "11-50", "50+"]).optional(),
  headquarterCity: z
    .enum([
      "johannesburg",
      "cape_town",
      "durban",
      "pretoria",
      "lagos",
      "cairo",
      "nairobi",
      "london",
      "paris",
      "berlin",
      "amsterdam",
      "new_york",
      "san_francisco",
      "toronto",
      "tokyo",
      "singapore",
      "dubai",
    ])
    .optional(),
  headquarterCountry: z
    .enum([
      "GB",
      "DE",
      "FR",
      "NL",
      "ES",
      "ZA",
      "NG",
      "KE",
      "EG",
      "GH",
      "US",
      "CA",
      "MX",
      "JP",
      "SG",
      "AE",
      "IN",
      "CN",
      "AU",
      "NZ",
    ])
    .optional(),
});

export type CompanyDetailsFormData = z.infer<typeof companyDetailsSchema>;

export async function updateCompanyDetails(
  companyId: string,
  data: CompanyDetailsFormData,
) {
  try {
    console.log("Updating company details with:", data);
    const validated = companyDetailsSchema.parse(data);

    // First, update the company name and domain
    await db
      .update(companies)
      .set({
        companyName: validated.companyName,
        companyDomain: validated.companyDomain,
        updatedAt: new Date(),
      })
      .where(eq(companies.id, companyId));

    // Check if company details exist
    const existingDetails = await db.query.companyDetails.findFirst({
      where: eq(companyDetails.companyId, companyId),
    });

    const detailsData = {
      teamSize: validated.teamSize as TeamSize,
      industry: validated.industry as Industry,
      locationCount: validated.locationCount as LocationCount,
      headquarterCity: validated.headquarterCity as City,
      headquarterCountry: validated.headquarterCountry as Country,
      updatedAt: new Date(),
    };

    if (existingDetails) {
      // Update existing details
      await db
        .update(companyDetails)
        .set(detailsData)
        .where(eq(companyDetails.companyId, companyId));
    } else {
      // Insert new details
      await db.insert(companyDetails).values({
        companyId,
        ...detailsData,
        createdAt: new Date(),
      });
    }

    console.log("Update completed successfully");
    revalidatePath("/settings");
    return { success: true };
  } catch (error) {
    console.error("Error updating company details:", error);
    return {
      success: false,
      error:
        error instanceof Error
          ? error.message
          : "Failed to update company details",
    };
  }
}

export async function getCompanyDetails(companyId: string) {
  try {
    const result = await db.query.companyDetails.findFirst({
      where: eq(companyDetails.companyId, companyId),
      columns: {
        teamSize: true,
        industry: true,
        locationCount: true,
        headquarterCity: true,
        headquarterCountry: true,
      },
    });

    return { success: true, data: result };
  } catch (error) {
    console.error("Error fetching company details:", error);
    return { success: false, error: "Failed to fetch company details" };
  }
}
