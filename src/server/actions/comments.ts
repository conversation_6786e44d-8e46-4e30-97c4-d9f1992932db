"use server";

import { db } from "@/server/db/index";
import { eq, and, desc, inArray } from "drizzle-orm";
import {
  CommentTable,
  NotificationTable,
  users,
  ReviewCommentTable,
} from "@/server/drizzle/schema";
import { revalidatePath } from "next/cache";
import { type Comment, type ApiResponse, type Notification } from "@/types";

interface CommentInput {
  reviewId: string;
  questionId: string;
  userId: string;
  text: string;
  parentId?: string;
}

export async function addComment({
  reviewId,
  questionId,
  userId,
  text,
  parentId,
}: CommentInput): Promise<ApiResponse<Comment>> {
  try {
    // Get user name for the comment
    const [user] = await db
      .select({
        firstName: users.firstName,
      })
      .from(users)
      .where(eq(users.clerkUserId, userId))
      .limit(1);

    if (!user) {
      return { success: false, error: "User not found" };
    }

    const [comment] = await db
      .insert(CommentTable)
      .values({
        reviewId,
        questionId,
        userId,
        text,
        parentId: parentId ?? null,
        edited: false,
      })
      .returning();

    const commentWithUser: Comment = {
      id: comment.id,
      reviewId: comment.reviewId,
      questionId: comment.questionId,
      userId: comment.userId,
      userName: user.firstName,
      text: comment.text,
      edited: comment.edited,
      createdAt: comment.createdAt,
      ...(comment.parentId && { parentId: comment.parentId }),
    };

    // Create notifications for mentions (@username)
    const mentions = text.match(/@(\w+)/g);
    if (mentions?.length) {
      const mentionedUsers = await db
        .select()
        .from(users)
        .where(
          inArray(
            users.firstName,
            mentions.map((m) => m.substring(1)),
          ),
        );

      await Promise.all(
        mentionedUsers.map((user) =>
          db.insert(NotificationTable).values({
            userId: user.clerkUserId,
            reviewId,
            questionId,
            commentId: comment.id,
            type: "mention",
            read: false,
          }),
        ),
      );
    }

    // Create notification for parent comment author if this is a reply
    if (parentId) {
      const [parentComment] = await db
        .select()
        .from(CommentTable)
        .where(eq(CommentTable.id, parentId))
        .limit(1);

      if (parentComment) {
        await db.insert(NotificationTable).values({
          userId: parentComment.userId,
          reviewId,
          questionId,
          commentId: comment.id,
          type: "reply",
          read: false,
        });
      }
    }

    revalidatePath(`/reviews/${reviewId}`);
    return { success: true, data: commentWithUser };
  } catch (error) {
    console.error("Error adding comment:", error);
    return { success: false, error: "Failed to add comment" };
  }
}

export async function updateComment({
  commentId,
  text,
}: {
  commentId: string;
  text: string;
}): Promise<ApiResponse<Comment>> {
  try {
    const [updated] = await db
      .update(CommentTable)
      .set({
        text,
        edited: true,
        updatedAt: new Date(),
      })
      .where(eq(CommentTable.id, commentId))
      .returning();

    // Get user name
    const [user] = await db
      .select({
        firstName: users.firstName,
      })
      .from(users)
      .where(eq(users.clerkUserId, updated.userId))
      .limit(1);

    if (!user) {
      return { success: false, error: "User not found" };
    }

    const commentWithUser: Comment = {
      id: updated.id,
      reviewId: updated.reviewId,
      questionId: updated.questionId,
      userId: updated.userId,
      userName: user.firstName,
      text: updated.text,
      edited: updated.edited,
      createdAt: updated.createdAt,
      parentId: updated.parentId || undefined,
    };

    revalidatePath(`/reviews/${updated.reviewId}`);
    return { success: true, data: commentWithUser };
  } catch (error) {
    console.error("Error updating comment:", error);
    return { success: false, error: "Failed to update comment" };
  }
}

export async function deleteComment(
  commentId: string,
): Promise<ApiResponse<void>> {
  try {
    const [deleted] = await db
      .delete(CommentTable)
      .where(eq(CommentTable.id, commentId))
      .returning();

    revalidatePath(`/reviews/${deleted.reviewId}`);
    return { success: true, data: undefined };
  } catch (error) {
    console.error("Error deleting comment:", error);
    return { success: false, error: "Failed to delete comment" };
  }
}

export async function getComments(
  reviewId: string,
  questionId: string,
): Promise<ApiResponse<Comment[]>> {
  try {
    // Get comments from both tables
    const [assessmentComments, reviewComments] = await Promise.all([
      // Get comments from CommentTable (assessment comments)
      db
        .select({
          id: CommentTable.id,
          reviewId: CommentTable.reviewId,
          questionId: CommentTable.questionId,
          userId: CommentTable.userId,
          userName: users.firstName,
          text: CommentTable.text,
          edited: CommentTable.edited,
          parentId: CommentTable.parentId,
          createdAt: CommentTable.createdAt,
        })
        .from(CommentTable)
        .innerJoin(users, eq(CommentTable.userId, users.clerkUserId))
        .where(
          and(
            eq(CommentTable.reviewId, reviewId),
            eq(CommentTable.questionId, questionId),
          ),
        )
        .orderBy(desc(CommentTable.createdAt)),

      // Get comments from ReviewCommentTable (feedback comments)
      db
        .select()
        .from(ReviewCommentTable)
        .where(
          and(
            eq(ReviewCommentTable.reviewId, reviewId),
            eq(ReviewCommentTable.questionId, questionId),
          ),
        )
        .orderBy(desc(ReviewCommentTable.createdAt)),
    ]);

    // Merge and format comments from both tables
    const formattedComments: Comment[] = [
      ...assessmentComments.map((c) => ({
        ...c,
        parentId: c.parentId || undefined,
      })),
      ...reviewComments.map((c) => ({
        id: c.id,
        reviewId: c.reviewId,
        questionId: c.questionId,
        userId: c.userId,
        userName: `${c.firstName} ${c.lastName}`,
        text: c.text,
        edited: c.edited ?? false,
        createdAt: c.createdAt,
      })),
    ].sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime());

    return { success: true, data: formattedComments };
  } catch (error) {
    console.error("Error fetching comments:", error);
    return { success: false, error: "Failed to fetch comments" };
  }
}

export async function getNotifications(
  userId: string,
): Promise<ApiResponse<Notification[]>> {
  try {
    const notifications = await db
      .select()
      .from(NotificationTable)
      .where(eq(NotificationTable.userId, userId))
      .orderBy(desc(NotificationTable.createdAt));

    return { success: true, data: notifications };
  } catch (error) {
    console.error("Error fetching notifications:", error);
    return { success: false, error: "Failed to fetch notifications" };
  }
}

export async function markNotificationAsRead(
  notificationId: string,
): Promise<ApiResponse<void>> {
  try {
    await db
      .update(NotificationTable)
      .set({ read: true })
      .where(eq(NotificationTable.id, notificationId));

    return { success: true, data: undefined };
  } catch (error) {
    console.error("Error marking notification as read:", error);
    return { success: false, error: "Failed to mark notification as read" };
  }
}
