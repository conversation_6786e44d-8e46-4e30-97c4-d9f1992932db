import { db } from "@/server/db";
import { CategoryTable } from "@/server/drizzle/schema";

export async function getCategories() {
  try {
    const categories = await db
      .select({
        id: CategoryTable.id,
        name: CategoryTable.name,
      })
      .from(CategoryTable)
      .orderBy(CategoryTable.name);

    return categories;
  } catch (error) {
    console.error("Error fetching categories:", error);
    return [];
  }
}
