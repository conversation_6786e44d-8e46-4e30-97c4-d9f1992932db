"use server";

import { db } from "@/server/db/index";
import { userCompanies } from "@/server/drizzle/schema";
import { eq } from "drizzle-orm";

export async function checkUserOnboardingStatus(userId: string) {
  try {
    const userCompany = await db
      .select()
      .from(userCompanies)
      .where(eq(userCompanies.userId, userId))
      .limit(1);

    return {
      isMember: userCompany.length > 0 && userCompany[0].role === "member",
      hasCompany: userCompany.length > 0,
      role: userCompany[0]?.role,
    };
  } catch (error) {
    console.error("Error checking user onboarding status:", error);
    throw new Error("Failed to check user onboarding status");
  }
}
