"use server";

import { db } from "@/server/db/index";
import { eq } from "drizzle-orm";
import {
  companies,
  userCompanies,
  companyDetails,
} from "@/server/drizzle/schema";
import { auth } from "@clerk/nextjs/server";
import { redirect } from "next/navigation";
import { type ApiResponse } from "@/types";
interface CompanyData {
  teamSize: string | undefined;
  industry: string | undefined;
  locationCount: string | undefined;
  headquarterCity: string | undefined;
  headquarterCountry: string | undefined;
  createdAt: Date | undefined;
  updatedAt: Date | undefined;
  id: string;
  companyName: string;
  companyDomain: string;
}

export async function checkUserCompany(
  email: string,
): Promise<
  | { success: true; requiresCompanyCreation: true; companyDomain: string }
  | { success: false; error: string }
> {
  try {
    const { userId } = auth();
    if (!userId) throw new Error("Unauthorized");

    // Extract domain from email
    const domain = email.split("@")[1];

    // First check if user already has a company association
    const existingUserCompany = await db
      .select()
      .from(userCompanies)
      .where(eq(userCompanies.clerkUserId, userId))
      .limit(1);

    if (existingUserCompany.length > 0) {
      // User already has a company association - redirect to dashboard
      redirect("/dashboard/home");
    }

    // Check if company exists with this domain
    const existingCompany = await db
      .select()
      .from(companies)
      .where(eq(companies.companyDomain, domain))
      .limit(1);

    // Company exists, associate user as member
    if (existingCompany.length > 0) {
      // Company exists, create user-company association
      await db.insert(userCompanies).values({
        userId: userId,
        clerkUserId: userId,
        companyId: existingCompany[0].id,
        role: "member",
      });

      // Redirect to dashboard as member
      redirect("/dashboard/home");
    }

    // No company found with this domain
    return {
      success: true,
      requiresCompanyCreation: true,
      companyDomain: domain,
    };
  } catch (error) {
    if (error instanceof Error && error.message === "NEXT_REDIRECT") {
      throw error; // Let Next.js handle the redirect
    }
    console.error("Error checking company:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Failed to check company",
    };
  }
}

export async function createCompany(data: {
  companyName: string;
  companyDomain: string;
}): Promise<ApiResponse<void>> {
  try {
    const { userId } = auth();
    if (!userId) throw new Error("Unauthorized");

    // Check if user already has a company association
    const existingUserCompany = await db
      .select()
      .from(userCompanies)
      .where(eq(userCompanies.clerkUserId, userId))
      .limit(1);

    if (existingUserCompany.length > 0) {
      throw new Error("User already has a company association");
    }

    // Create company
    const [company] = await db
      .insert(companies)
      .values({
        companyName: data.companyName,
        companyDomain: data.companyDomain,
      })
      .returning();

    // Create company details with proper type checking
    await db.insert(companyDetails).values({
      companyId: company.id,
      createdAt: new Date(),
      updatedAt: new Date(),
    });

    // Create user-company association
    await db.insert(userCompanies).values({
      userId,
      clerkUserId: userId,
      companyId: company.id,
      role: "admin", // First user is admin
    });

    return { success: true, data: undefined };
  } catch (error) {
    console.error("Error creating company:", error);
    return {
      success: false,
      error:
        error instanceof Error ? error.message : "Failed to create company",
    };
  }
}
export async function getUserCompany(
  userId: string,
): Promise<ApiResponse<CompanyData>> {
  try {
    const userCompany = await db
      .select({
        id: companies.id,
        companyName: companies.companyName,
        companyDomain: companies.companyDomain,
        headquarterCity: companyDetails.headquarterCity,
      })
      .from(companies)
      .innerJoin(userCompanies, eq(userCompanies.companyId, companies.id))
      .leftJoin(companyDetails, eq(companyDetails.companyId, companies.id))
      .where(eq(userCompanies.clerkUserId, userId))
      .limit(1);

    if (!userCompany.length) {
      return { success: false, error: "No company found for user" };
    }

    return { success: true, data: userCompany[0] as CompanyData };
  } catch (error) {
    console.error("Error fetching user company:", error);
    return { success: false, error: "Failed to fetch company" };
  }
}
