"use server";

import { db } from "@/server/db/index";
import {
  ReviewAnswerTable,
  ReviewTable,
  QuestionTable,
  CategoryTable,
  RecommendationTable,
  TaskTable,
  type ReviewWithProgress,
  type Answer,
  ReviewCollaboratorTable,
  ReviewHistoryTable,
  ReviewCommentTable,
} from "@/server/drizzle/schema";
import { eq, and, desc, sql, or, inArray, count } from "drizzle-orm";
import { revalidatePath } from "next/cache";
import { cache } from "react";

// Update return type for getReviewQuestions
export type QuestionWithCategory = {
  id: string;
  text: string;
  description: string;
  guideImage: string | null;
  category: string;
  questionGuide?: {
    description: string;
    image: string;
  };
  answer?: "yes" | "no" | "partially" | "na";
  comment?: string;
};

export async function getReviewQuestions(): Promise<{
  success: boolean;
  data?: QuestionWithCategory[];
  error?: string;
}> {
  try {
    const questions = await db
      .select({
        id: QuestionTable.id,
        text: QuestionTable.text,
        description: QuestionTable.description,
        guideImage: QuestionTable.guideImage,
        category: CategoryTable.name,
      })
      .from(QuestionTable)
      .leftJoin(CategoryTable, eq(QuestionTable.categoryId, CategoryTable.id))
      .where(eq(QuestionTable.isArchived, false)); // Filter for non-archived questions

    // Transform the data to match the expected format
    const formattedQuestions = questions.map((q) => ({
      ...q,
      category: q.category || "Uncategorized",
      questionGuide: {
        description: q.description,
        image: q.guideImage || "",
      },
    }));

    return { success: true, data: formattedQuestions };
  } catch (error) {
    console.error("Error fetching questions:", error);
    return { success: false, error: "Failed to fetch questions" };
  }
}

// Update getReviewProgress to handle null categories
export async function getReviewProgress(reviewId: string) {
  try {
    const answers = await db
      .select()
      .from(ReviewAnswerTable)
      .where(eq(ReviewAnswerTable.reviewId, reviewId));

    const questionsResult = await getReviewQuestions();

    if (!questionsResult.success || !questionsResult.data) {
      throw new Error("Failed to fetch questions");
    }

    const progress = questionsResult.data.reduce(
      (acc, question) => {
        const category = question.category || "Uncategorized";
        if (!acc[category]) {
          acc[category] = { total: 0, answered: 0 };
        }
        acc[category].total++;
        if (answers.find((a) => a.questionId === question.id)) {
          acc[category].answered++;
        }
        return acc;
      },
      {} as Record<string, { total: number; answered: number }>,
    );

    return { success: true, data: progress };
  } catch (error) {
    console.error("Error getting review progress:", error);
    return { success: false, error: "Failed to get review progress" };
  }
}

// Keep other functions but update their imports to use the new db import

// Get all reviews for a company
export async function getCompanyReviews(companyId: string) {
  try {
    const reviews = await db
      .select({
        id: ReviewTable.id,
        title: ReviewTable.title,
        type: ReviewTable.type,
        status: ReviewTable.status,
        createdAt: ReviewTable.createdAt,
        isArchived: ReviewTable.isArchived,
      })
      .from(ReviewTable)
      .where(eq(ReviewTable.companyId, companyId))
      .orderBy(desc(ReviewTable.createdAt));

    // Get progress for each review
    const reviewsWithProgress = await Promise.all(
      reviews.map(async (review) => {
        const progress = await getReviewProgress(review.id);
        return {
          ...review,
          progress: progress.success ? progress.data : {},
        };
      }),
    );

    return { success: true, data: reviewsWithProgress };
  } catch (error) {
    console.error("Error fetching reviews:", error);
    return { success: false, error: "Failed to fetch reviews" };
  }
}

// Save answer with optimistic update support
export async function saveAnswer({
  reviewId,
  questionId,
  answer,
  comment,
  userId,
}: {
  reviewId: string;
  questionId: string;
  answer: "yes" | "no" | "partially" | "na";
  comment?: string;
  userId: string;
}) {
  try {
    // First verify the review exists
    const review = await db
      .select()
      .from(ReviewTable)
      .where(eq(ReviewTable.id, reviewId))
      .limit(1);

    if (!review.length) {
      return {
        success: false,
        error: "Review not found",
      };
    }

    // Check if answer exists
    const existingAnswer = await db
      .select()
      .from(ReviewAnswerTable)
      .where(
        and(
          eq(ReviewAnswerTable.reviewId, reviewId),
          eq(ReviewAnswerTable.questionId, questionId),
        ),
      );

    let savedAnswer;

    if (existingAnswer.length > 0) {
      // Update existing answer
      [savedAnswer] = await db
        .update(ReviewAnswerTable)
        .set({
          answer,
          comment,
          updatedAt: new Date(),
        })
        .where(eq(ReviewAnswerTable.id, existingAnswer[0].id))
        .returning();
    } else {
      // Create new answer
      [savedAnswer] = await db
        .insert(ReviewAnswerTable)
        .values({
          reviewId,
          questionId,
          answer,
          comment,
          answeredByUserId: userId,
        })
        .returning();
    }

    // If answer is "no" or "partially", create tasks from recommendations
    if (answer === "no" || answer === "partially") {
      await createTasksFromRecommendations(reviewId, questionId);
    }

    revalidatePath(`/reviews/${reviewId}`);
    return { success: true, data: savedAnswer };
  } catch (error) {
    console.error("Error saving answer:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Failed to save answer",
    };
  }
}

// Get review with answers
export async function getReviewWithAnswers(reviewId: string) {
  try {
    const review = await db
      .select()
      .from(ReviewTable)
      .where(eq(ReviewTable.id, reviewId))
      .limit(1);

    if (!review.length) {
      return { success: false, error: "Review not found" };
    }

    const answers = await db
      .select()
      .from(ReviewAnswerTable)
      .where(eq(ReviewAnswerTable.reviewId, reviewId));

    const questions = await getReviewQuestions();

    if (!questions.success || !questions.data) {
      return { success: false, error: "Failed to fetch questions" };
    }

    // Merge questions with answers
    const questionsWithAnswers = questions.data.map((question) => {
      const answer = answers.find((a) => a.questionId === question.id);
      return {
        ...question,
        answer: answer?.answer,
        comment: answer?.comment,
      };
    });

    return {
      success: true,
      data: {
        review: review[0],
        questions: questionsWithAnswers,
      },
    };
  } catch (error) {
    console.error("Error fetching review:", error);
    return { success: false, error: "Failed to fetch review" };
  }
}

// Create tasks from recommendations when answer is "no" or "partially"
async function createTasksFromRecommendations(
  reviewId: string,
  questionId: string,
) {
  try {
    // Get the answer for this question first
    const answer = await db
      .select()
      .from(ReviewAnswerTable)
      .where(
        and(
          eq(ReviewAnswerTable.reviewId, reviewId),
          eq(ReviewAnswerTable.questionId, questionId),
        ),
      )
      .limit(1);

    // Only proceed if answer is "no" or "partially"
    if (answer[0]?.answer === "no" || answer[0]?.answer === "partially") {
      // Get recommendations first
      const recommendations = await db
        .select({
          id: RecommendationTable.id,
          text: RecommendationTable.text,
          priority: RecommendationTable.priority,
          category: CategoryTable.name,
        })
        .from(RecommendationTable)
        .leftJoin(
          QuestionTable,
          eq(RecommendationTable.questionId, QuestionTable.id),
        )
        .leftJoin(CategoryTable, eq(QuestionTable.categoryId, CategoryTable.id))
        .where(eq(RecommendationTable.questionId, questionId));

      // Check for existing tasks
      const existingTasks = await db
        .select()
        .from(TaskTable)
        .where(
          and(
            eq(TaskTable.reviewId, reviewId),
            eq(TaskTable.questionId, questionId),
          ),
        );

      if (existingTasks.length > 0) {
        console.log(`Tasks already exist for question ${questionId}`);
        return;
      }

      const review = await db
        .select()
        .from(ReviewTable)
        .where(eq(ReviewTable.id, reviewId))
        .limit(1);

      if (!review.length) return;

      // Create draft tasks
      const tasksToCreate = recommendations.map((rec) => ({
        recommendationId: rec.id,
        reviewId,
        questionId,
        reviewAnswerId: answer[0].id,
        companyId: review[0].companyId,
        title: rec.text,
        description: `Task created from review ${review[0].title}`,
        status: "Draft" as const,
        priority: (answer[0]?.answer === "no" ? rec.priority : "medium") as
          | "high"
          | "medium"
          | "low",
        startDate: new Date(),
        category: rec.category ?? "Uncategorized",
        assignedToUserId: review[0].createdByUserId,
      }));

      if (tasksToCreate.length > 0) {
        await db.insert(TaskTable).values(tasksToCreate);
      }
    }
  } catch (error) {
    console.error("Error creating draft tasks:", error);
  }
}

// Update the getReviewDetails function
export async function getReviewDetails(reviewId: string) {
  try {
    const [review] = await db
      .select()
      .from(ReviewTable)
      .where(eq(ReviewTable.id, reviewId))
      .limit(1);

    if (!review) {
      return { success: false, error: "Review not found" };
    }

    // Get questions with their categories
    const questions = await db
      .select({
        id: QuestionTable.id,
        text: QuestionTable.text,
        description: QuestionTable.description,
        guideImage: QuestionTable.guideImage,
        category: CategoryTable.name,
      })
      .from(QuestionTable)
      .leftJoin(CategoryTable, eq(QuestionTable.categoryId, CategoryTable.id))
      .where(eq(QuestionTable.isArchived, false)); // Filter for non-archived questions

    // Get existing answers for this review
    const answers = await db
      .select()
      .from(ReviewAnswerTable)
      .where(eq(ReviewAnswerTable.reviewId, reviewId));

    // Format questions with answers
    const questionsWithAnswers = questions.map((q) => {
      const answer = answers.find((a) => a.questionId === q.id);
      return {
        id: q.id,
        text: q.text,
        description: q.description,
        guideImage: q.guideImage,
        category: q.category || "Uncategorized",
        answer: answer?.answer,
        comment: answer?.comment,
      };
    });

    return {
      success: true,
      data: {
        review,
        questions: questionsWithAnswers,
      },
    };
  } catch (error) {
    console.error("Error fetching review details:", error);
    return { success: false, error: "Failed to fetch review details" };
  }
}

// Update review status
export async function updateReviewStatus(
  reviewId: string,
  status: "draft" | "in_progress" | "completed",
) {
  try {
    const [updated] = await db
      .update(ReviewTable)
      .set({
        status,
        updatedAt: new Date(),
      })
      .where(eq(ReviewTable.id, reviewId))
      .returning();

    revalidatePath(`/reviews/${reviewId}`);
    return { success: true, data: updated };
  } catch (error) {
    console.error("Error updating review status:", error);
    return { success: false, error: "Failed to update review status" };
  }
}

// Add these exports
export async function getReviewCollaborators(reviewId: string) {
  try {
    const collaborators = await db
      .select()
      .from(ReviewCollaboratorTable)
      .where(eq(ReviewCollaboratorTable.reviewId, reviewId));

    return { success: true, data: collaborators };
  } catch (error) {
    console.error("Error fetching collaborators:", error);
    return { success: false, error: "Failed to fetch collaborators" };
  }
}

export async function inviteCollaborator(data: {
  reviewId: string;
  companyId: string;
  email: string;
  role: "reviewer" | "collaborator";
  inviterName: string;
  reviewTitle: string;
}) {
  // Implementation from invitations.ts
}

export async function removeCollaborator(collaboratorId: string) {
  try {
    await db
      .delete(ReviewCollaboratorTable)
      .where(eq(ReviewCollaboratorTable.id, collaboratorId));

    return { success: true };
  } catch (error) {
    console.error("Error removing collaborator:", error);
    return { success: false, error: "Failed to remove collaborator" };
  }
}

export async function updateCollaboratorRole(
  collaboratorId: string,
  newRole: "reviewer" | "collaborator",
) {
  try {
    const [updated] = await db
      .update(ReviewCollaboratorTable)
      .set({ role: newRole })
      .where(eq(ReviewCollaboratorTable.id, collaboratorId))
      .returning();

    return { success: true, data: updated };
  } catch (error) {
    console.error("Error updating collaborator role:", error);
    return { success: false, error: "Failed to update collaborator role" };
  }
}

// Add getReviewHistory to exports
export async function getReviewHistory(reviewId: string) {
  try {
    const changes = await db
      .select({
        id: ReviewHistoryTable.id,
        changes: ReviewHistoryTable.changes,
        createdAt: ReviewHistoryTable.createdAt,
      })
      .from(ReviewHistoryTable)
      .where(eq(ReviewHistoryTable.reviewId, reviewId))
      .orderBy(desc(ReviewHistoryTable.createdAt));

    return { success: true, data: changes };
  } catch (error) {
    console.error("Error fetching review history:", error);
    return { success: false, error: "Failed to fetch review history" };
  }
}

export async function createReview({
  title,
  type,
  companyId,
  createdByUserId,
}: {
  title: string;
  type: "basic" | "comprehensive";
  companyId: string;
  createdByUserId: string;
}) {
  try {
    const [review] = await db
      .insert(ReviewTable)
      .values({
        title,
        type,
        companyId,
        createdByUserId,
        status: "draft",
      })
      .returning();

    return { success: true, data: review };
  } catch (error) {
    console.error("Error creating review:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Failed to create review",
    };
  }
}

// Update the saveComment function to handle first and last names
export async function saveComment({
  reviewId,
  questionId,
  text,
  userId,
  firstName,
  lastName,
}: {
  reviewId: string;
  questionId: string;
  text: string;
  userId: string;
  firstName: string;
  lastName: string;
}) {
  try {
    const [comment] = await db
      .insert(ReviewCommentTable)
      .values({
        reviewId,
        questionId,
        text,
        userId,
        firstName,
        lastName,
        createdAt: new Date(),
        edited: false,
      })
      .returning();

    return { success: true, data: comment };
  } catch (error) {
    console.error("Error saving comment:", error);
    return { success: false, error: "Failed to save comment" };
  }
}

// Add this function to get comments
export async function getQuestionComments(
  reviewId: string,
  questionId: string,
) {
  try {
    const comments = await db
      .select()
      .from(ReviewCommentTable)
      .where(
        and(
          eq(ReviewCommentTable.reviewId, reviewId),
          eq(ReviewCommentTable.questionId, questionId),
        ),
      )
      .orderBy(desc(ReviewCommentTable.createdAt));

    return { success: true, data: comments };
  } catch (error) {
    console.error("Error fetching comments:", error);
    return { success: false, error: "Failed to fetch comments" };
  }
}

// Add this function to update a comment
export async function updateComment({
  commentId,
  text,
}: {
  commentId: string;
  text: string;
}) {
  try {
    const [updated] = await db
      .update(ReviewCommentTable)
      .set({
        text,
        edited: true,
        updatedAt: new Date(),
      })
      .where(eq(ReviewCommentTable.id, commentId))
      .returning();

    return { success: true, data: updated };
  } catch (error) {
    console.error("Error updating comment:", error);
    return { success: false, error: "Failed to update comment" };
  }
}

export async function getLatestReview(companyId: string) {
  try {
    // Fetch the most recent review
    const [review] = await db
      .select({
        id: ReviewTable.id,
        title: ReviewTable.title,
        type: ReviewTable.type,
        status: ReviewTable.status,
        createdAt: ReviewTable.createdAt,
        updatedAt: ReviewTable.updatedAt,
        createdByUserId: ReviewTable.createdByUserId,
        companyId: ReviewTable.companyId,
      })
      .from(ReviewTable)
      .where(eq(ReviewTable.companyId, companyId))
      .orderBy(desc(ReviewTable.createdAt))
      .limit(1);

    if (!review) {
      return { success: true, data: null };
    }

    // Get answers grouped by category
    const categoryProgress = await db
      .select({
        categoryName: CategoryTable.name,
        totalQuestions: sql<number>`cast(count(${QuestionTable.id}) as integer)`,
        answeredQuestions: sql<number>`cast(count(${ReviewAnswerTable.id}) as integer)`,
      })
      .from(QuestionTable)
      .leftJoin(CategoryTable, eq(QuestionTable.categoryId, CategoryTable.id))
      .leftJoin(
        ReviewAnswerTable,
        and(
          eq(ReviewAnswerTable.questionId, QuestionTable.id),
          eq(ReviewAnswerTable.reviewId, review.id),
        ),
      )
      .where(eq(QuestionTable.isArchived, false)) // Add filter for non-archived questions
      .groupBy(CategoryTable.name);

    // Format the progress data to match the UI structure
    const progressData = categoryProgress.map((cat) => ({
      title: cat.categoryName,
      value: cat.answeredQuestions,
      total: cat.totalQuestions,
    }));

    // Get collaborators count
    const [collaboratorsCount] = await db
      .select({
        count: sql<number>`cast(count(${ReviewCollaboratorTable.id}) as integer)`,
      })
      .from(ReviewCollaboratorTable)
      .where(eq(ReviewCollaboratorTable.reviewId, review.id));

    // Format the data to match what the component expects
    const formattedReview = {
      ...review,
      progressData,
      collaboratorsCount: collaboratorsCount?.count || 0,
    };

    return { success: true, data: formattedReview };
  } catch (error) {
    console.error("Error fetching latest review:", error);
    return { success: false, error: "Failed to fetch latest review" };
  }
}

export async function getReviewComments(reviewId: string) {
  try {
    const comments = await db
      .select({
        id: ReviewCommentTable.id,
        username: sql`${ReviewCommentTable.firstName} || ' ' || ${ReviewCommentTable.lastName}`,
        timestamp: ReviewCommentTable.createdAt,
        content: ReviewCommentTable.text,
        category: CategoryTable.name,
        questionNumber: QuestionTable.id,
      })
      .from(ReviewCommentTable)
      .leftJoin(
        QuestionTable,
        eq(ReviewCommentTable.questionId, QuestionTable.id),
      )
      .leftJoin(CategoryTable, eq(QuestionTable.categoryId, CategoryTable.id))
      .where(eq(ReviewCommentTable.reviewId, reviewId))
      .orderBy(desc(ReviewCommentTable.createdAt))
      .limit(5);

    return { success: true, data: comments };
  } catch (error) {
    console.error("Error fetching review comments:", error);
    return { success: false, error: "Failed to fetch review comments" };
  }
}

export async function deleteReview(reviewId: string) {
  try {
    const deletedReview = await db
      .delete(ReviewTable)
      .where(eq(ReviewTable.id, reviewId))
      .returning();

    return {
      success: true,
      data: deletedReview[0],
    };
  } catch (error) {
    console.error("Error deleting review:", error);
    return {
      success: false,
      error: "Failed to delete review",
    };
  }
}

export async function getReviewType(reviewId: string) {
  try {
    const review = await db
      .select({ type: ReviewTable.type })
      .from(ReviewTable)
      .where(eq(ReviewTable.id, reviewId))
      .limit(1);

    return {
      success: true,
      data: review[0]?.type ?? "basic",
    };
  } catch (error) {
    console.error("Error getting review type:", error);
    return { success: false, error: "Failed to get review type" };
  }
}

// Add this new server action
export async function updateReviewTitle(reviewId: string, title: string) {
  try {
    await db
      .update(ReviewTable)
      .set({ title })
      .where(eq(ReviewTable.id, reviewId));

    return {
      success: true,
      data: title,
    };
  } catch (error) {
    console.error("Error updating review title:", error);
    return {
      success: false,
      error: "Failed to update review title",
    };
  }
}

export async function getReviewTitle(reviewId: string) {
  try {
    const review = await db
      .select({ title: ReviewTable.title })
      .from(ReviewTable)
      .where(eq(ReviewTable.id, reviewId))
      .limit(1);

    return {
      success: true,
      data: review[0]?.title,
    };
  } catch (error) {
    console.error("Error getting review title:", error);
    return { success: false, error: "Failed to get review title" };
  }
}

export async function getReviewTaskStats(reviewId: string) {
  try {
    const tasks = await db
      .select({
        category: TaskTable.category,
        status: TaskTable.status,
        count: sql<number>`count(*)::int`,
      })
      .from(TaskTable)
      .where(eq(TaskTable.reviewId, reviewId))
      .groupBy(TaskTable.category, TaskTable.status);

    return {
      success: true,
      data: tasks,
    };
  } catch (error) {
    console.error("Error getting task stats:", error);
    return { success: false, error: "Failed to get task stats" };
  }
}

export async function updateReviewAnswerAndTasks({
  reviewId,
  questionId,
  answer,
  comment,
  userId,
}: {
  reviewId: string;
  questionId: string;
  answer: "yes" | "no" | "partially" | "na";
  comment?: string;
  userId: string;
}) {
  try {
    // Update the answer
    await db
      .update(ReviewAnswerTable)
      .set({
        answer,
        comment,
        updatedAt: new Date(),
      })
      .where(
        and(
          eq(ReviewAnswerTable.reviewId, reviewId),
          eq(ReviewAnswerTable.questionId, questionId),
        ),
      );

    // If answer changed to "yes" or "na", delete related tasks
    if (answer === "yes" || answer === "na") {
      await db
        .delete(TaskTable)
        .where(
          and(
            eq(TaskTable.reviewId, reviewId),
            eq(TaskTable.questionId, questionId),
          ),
        );
    }

    // If answer is "no" or "partially", create/update tasks
    if (answer === "no" || answer === "partially") {
      await createTasksFromRecommendations(reviewId, questionId);
    }

    // Add revalidation
    revalidatePath("/tasks");
    revalidatePath(`/tasks?reviewId=${reviewId}`);
    revalidatePath(`/reviews/${reviewId}/feedback`);
    revalidatePath("/dashboard/home");
    revalidatePath("/reviews/manage");
    revalidatePath(`/reviews/${reviewId}/report`);

    return { success: true };
  } catch (error) {
    console.error("Error updating answer and tasks:", error);
    return { success: false, error: "Failed to update answer and tasks" };
  }
}

// Add this shared query function
export const getReviewTasksAndRecommendations = cache(
  async (reviewId: string) => {
    try {
      const questions = await db
        .select({
          id: QuestionTable.id,
          text: QuestionTable.text,
          answer: ReviewAnswerTable.answer,
          category: CategoryTable.name,
        })
        .from(QuestionTable)
        .leftJoin(
          ReviewAnswerTable,
          and(
            eq(ReviewAnswerTable.questionId, QuestionTable.id),
            eq(ReviewAnswerTable.reviewId, reviewId),
          ),
        )
        .leftJoin(CategoryTable, eq(QuestionTable.categoryId, CategoryTable.id))
        .where(
          and(
            // Combine archived check with existing conditions
            eq(QuestionTable.isArchived, false),
            or(
              eq(ReviewAnswerTable.answer, "no"),
              eq(ReviewAnswerTable.answer, "partially"),
            ),
          ),
        );

      // Get recommendations for these questions
      const recommendations = await db
        .select({
          id: RecommendationTable.id,
          text: RecommendationTable.text,
          priority: RecommendationTable.priority,
          questionId: RecommendationTable.questionId,
          category: CategoryTable.name,
          questionText: QuestionTable.text,
        })
        .from(RecommendationTable)
        .leftJoin(
          QuestionTable,
          eq(RecommendationTable.questionId, QuestionTable.id),
        )
        .leftJoin(CategoryTable, eq(QuestionTable.categoryId, CategoryTable.id))
        .where(
          and(
            // Add archived check here too
            eq(QuestionTable.isArchived, false),
            inArray(
              RecommendationTable.questionId,
              questions.map((q) => q.id),
            ),
          ),
        );

      return { success: true, data: { questions, recommendations } };
    } catch (error) {
      console.error("Error getting review tasks:", error);
      return { success: false, error: "Failed to get review tasks" };
    }
  },
);

// Helper functions for task finalization
function areSimilar(text1: string, text2: string) {
  // Simple similarity check using word overlap
  const words1 = text1.toLowerCase().split(" ");
  const words2 = text2.toLowerCase().split(" ");
  const intersection = words1.filter((x) => words2.includes(x));
  const union = Array.from(new Set([...words1, ...words2]));
  return intersection.length / union.length > 0.5;
}

function findSimilarTasks(tasks: any[]) {
  const groups: any[][] = [];
  const used = new Set();

  tasks.forEach((task, i) => {
    if (used.has(i)) return;

    const group = [task];
    used.add(i);

    tasks.forEach((otherTask, j) => {
      if (i !== j && !used.has(j) && areSimilar(task.title, otherTask.title)) {
        group.push(otherTask);
        used.add(j);
      }
    });

    groups.push(group);
  });

  return groups;
}

function createMergedTitle(tasks: any[]) {
  if (tasks.length === 1) return tasks[0].title;
  // Find the most comprehensive title from the group
  return tasks.reduce(
    (longest, task) =>
      task.title.length > longest.length ? task.title : longest,
    tasks[0].title,
  );
}

function createMergedDescription(tasks: any[]) {
  return tasks
    .map((t, i) => `${i + 1}. ${t.description || t.title}`)
    .join("\n\nRelated task: ");
}

function calculateFinalPriority(tasks: any[], answers: any[]) {
  // Count how many "no" answers are associated with these tasks
  const noCount = tasks.reduce((count, task) => {
    const answer = answers.find((a) => a.questionId === task.questionId);
    return count + (answer?.answer === "no" ? 1 : 0);
  }, 0);

  // If more than half are "no", increase priority
  if (noCount > tasks.length / 2) {
    return "high";
  }
  return tasks[0].priority;
}

async function finalizeReviewTasks(reviewId: string) {
  try {
    // Get all draft tasks for this review
    const draftTasks = await db
      .select()
      .from(TaskTable)
      .where(
        and(eq(TaskTable.reviewId, reviewId), eq(TaskTable.status, "Draft")),
      );

    // Get all answers for this review
    const answers = await db
      .select()
      .from(ReviewAnswerTable)
      .where(eq(ReviewAnswerTable.reviewId, reviewId));

    // Group tasks by category for potential merging
    const tasksByCategory = draftTasks.reduce(
      (acc, task) => {
        if (!acc[task.category]) {
          acc[task.category] = [];
        }
        acc[task.category].push(task);
        return acc;
      },
      {} as Record<string, typeof draftTasks>,
    );

    // Process each category
    for (const [category, tasks] of Object.entries(tasksByCategory)) {
      // Find similar tasks that could be merged
      const similarTasks = findSimilarTasks(tasks);

      // Create merged tasks with updated priorities
      const mergedTasks = similarTasks.map((group) => ({
        ...group[0],
        title: createMergedTitle(group),
        description: createMergedDescription(group),
        priority: calculateFinalPriority(group, answers),
        status: "To Do" as const,
        updatedAt: new Date(),
      }));

      // Delete the original tasks first
      await db.delete(TaskTable).where(
        inArray(
          TaskTable.id,
          tasks.map((t) => t.id),
        ),
      );

      // Then insert the merged tasks
      if (mergedTasks.length > 0) {
        await db.insert(TaskTable).values(mergedTasks);
      }
    }

    return true;
  } catch (error) {
    console.error("Error finalizing tasks:", error);
    return false;
  }
}

// Update completeReview to include task finalization
export async function completeReview(reviewId: string) {
  try {
    // First finalize all tasks
    await finalizeReviewTasks(reviewId);

    // Then proceed with existing completion logic
    const [review] = await db
      .update(ReviewTable)
      .set({
        status: "completed" as const,
        updatedAt: new Date(),
      })
      .where(eq(ReviewTable.id, reviewId))
      .returning();

    if (!review) {
      throw new Error("Review not found");
    }

    // Use the same getCompanyReviews function as the dashboard
    const reviewsResult = await getCompanyReviews(review.companyId);
    const completedCount =
      reviewsResult.success && reviewsResult.data
        ? reviewsResult.data.filter((r) => r.status === "completed").length
        : 0;

    revalidatePath("/reviews/manage");

    return {
      success: true,
      data: {
        review,
        isFirstReview: completedCount === 16,
      },
    };
  } catch (error) {
    console.error("Error completing review:", error);
    return { success: false, error };
  }
}

export async function getReview(reviewId: string) {
  try {
    const [review] = await db
      .select()
      .from(ReviewTable)
      .where(eq(ReviewTable.id, reviewId));

    return { success: true, data: review };
  } catch (error) {
    console.error("Error getting review:", error);
    return { success: false, error };
  }
}

// Add this new function to reviews.ts
export async function archiveReview(reviewId: string) {
  try {
    const [updated] = await db
      .update(ReviewTable)
      .set({
        isArchived: true,
        updatedAt: new Date(),
      })
      .where(eq(ReviewTable.id, reviewId))
      .returning();

    revalidatePath("/reviews/manage");
    return { success: true, data: updated };
  } catch (error) {
    console.error("Error archiving review:", error);
    return { success: false, error: "Failed to archive review" };
  }
}

export async function restoreReview(reviewId: string) {
  try {
    const [updated] = await db
      .update(ReviewTable)
      .set({
        isArchived: false,
        updatedAt: new Date(),
      })
      .where(eq(ReviewTable.id, reviewId))
      .returning();

    revalidatePath("/reviews/manage");
    return { success: true, data: updated };
  } catch (error) {
    console.error("Error restoring review:", error);
    return { success: false, error: "Failed to restore review" };
  }
}
