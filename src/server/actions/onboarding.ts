"use server";

import { db } from "@/server/db/index";
import { eq } from "drizzle-orm";
import { users, userCompanies, companyDetails } from "@/server/drizzle/schema";
import { onboardingStepSchemas } from "@/server/zod-schemas/onboarding";
import { revalidatePath } from "next/cache";

// Export the types
export type UserRole =
  | "hr"
  | "other"
  | "executive"
  | "director"
  | "manager"
  | "team_lead"
  | "developer"
  | "designer"
  | "product_manager"
  | "project_manager"
  | "analyst"
  | "consultant"
  | "marketing"
  | "sales"
  | "support";

export type TeamSize = "1-10" | "11-50" | "50+";

export type Industry =
  | "technology"
  | "financial"
  | "healthcare"
  | "education"
  | "retail"
  | "manufacturing"
  | "professional_services"
  | "hospitality"
  | "construction"
  | "media"
  | "transportation"
  | "energy"
  | "agriculture"
  | "nonprofit"
  | "government";

export type LocationCount = "1-5" | "11-50" | "50+";

export type City =
  | "johannesburg"
  | "cape_town"
  | "durban"
  | "pretoria"
  | "lagos"
  | "cairo"
  | "nairobi"
  | "london"
  | "paris"
  | "berlin"
  | "amsterdam"
  | "new_york"
  | "san_francisco"
  | "toronto"
  | "tokyo"
  | "singapore"
  | "dubai";

export type Country =
  | "GB"
  | "DE"
  | "FR"
  | "NL"
  | "ES"
  | "ZA"
  | "NG"
  | "KE"
  | "EG"
  | "GH"
  | "US"
  | "CA"
  | "MX"
  | "JP"
  | "SG"
  | "AE"
  | "IN"
  | "CN"
  | "AU"
  | "NZ";

interface OnboardingData {
  role?: UserRole;
  teamSize?: TeamSize;
  industry?: Industry;
  locationCount?: LocationCount;
  headquarterCity?: City;
  headquarterCountry?: Country;
  invites?: string[];
}

export async function updateOnboardingStep(
  step: number,
  data: OnboardingData,
  clerkUserId: string,
) {
  try {
    // Validate data based on step
    const stepSchema = getStepSchema(step);
    if (stepSchema) {
      stepSchema.parse(data);
    }

    // Get user and company info
    const [userCompany] = await db
      .select()
      .from(userCompanies)
      .where(eq(userCompanies.clerkUserId, clerkUserId));

    if (!userCompany) {
      throw new Error("User company association not found");
    }

    // Ensure companyId is not null before updates
    if (!userCompany.companyId) {
      throw new Error("Invalid company ID");
    }

    // Update based on step
    switch (step) {
      case 3: // Position
        await db
          .update(users)
          .set({
            position: data.role,
            updatedAt: new Date(),
          })
          .where(eq(users.clerkUserId, clerkUserId));
        break;

      case 4: // Company Size
        await db
          .update(companyDetails)
          .set({
            teamSize: data.teamSize,
            updatedAt: new Date(),
          })
          .where(eq(companyDetails.companyId, userCompany.companyId));
        break;

      case 5: // Industry
        await db
          .update(companyDetails)
          .set({
            industry: data.industry,
            updatedAt: new Date(),
          })
          .where(eq(companyDetails.companyId, userCompany.companyId));
        break;

      case 6: // Locations
        await db
          .update(companyDetails)
          .set({
            locationCount: data.locationCount,
            updatedAt: new Date(),
          })
          .where(eq(companyDetails.companyId, userCompany.companyId));
        break;

      case 7: // Headquarters
        await db
          .update(companyDetails)
          .set({
            headquarterCity: data.headquarterCity,
            headquarterCountry: data.headquarterCountry,
            updatedAt: new Date(),
          })
          .where(eq(companyDetails.companyId, userCompany.companyId));
        break;

      case 8: // Team Invites
        if (data.invites?.length) {
          await processTeamInvites(data.invites, userCompany.companyId);
        }
        break;
    }

    revalidatePath("/onboarding");
    return { success: true };
  } catch (error) {
    console.error("Error updating onboarding step:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Failed to update",
    };
  }
}

// Add the team invites handling function
async function processTeamInvites(invites: string[], companyId: string) {
  // Implement your team invite logic here
  // For example:
  for (const email of invites) {
    // Add to invites table or send emails
    console.log(`Processing invite for ${email} to company ${companyId}`);
  }
}
function getStepSchema(step: number) {
  switch (step) {
    case 3:
      return onboardingStepSchemas.userPosition;
    case 4:
      return onboardingStepSchemas.companySize;
    case 5:
      return onboardingStepSchemas.companyIndustry;
    case 6:
      return onboardingStepSchemas.companyLocations;
    case 7:
      return onboardingStepSchemas.headquarters;
    case 8:
      return onboardingStepSchemas.teamInvites;
    default:
      return null;
  }
}
