import { users } from "@/server/drizzle/schema";
import { db } from "@/server/db/index";
import { eq } from "drizzle-orm";

export async function createNewUser({
  data,
}: {
  data: typeof users.$inferInsert;
}) {
  return db.insert(users).values(data).onConflictDoNothing({
    target: users.clerkUserId,
  });
}

export async function deleteUser(userId: string) {
  return db.delete(users).where(eq(users.id, userId));
}
