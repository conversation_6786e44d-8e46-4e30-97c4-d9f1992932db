"use server";

import { db } from "@/server/db/index";
import { eq } from "drizzle-orm";
import { userCompanies, users } from "@/server/drizzle/schema";
import { revalidatePath } from "next/cache";

export type TeamMember = {
  id: string;
  firstName: string;
  lastName: string;
  email: string;
  role: "admin" | "member";
  position: string | null;
  clerkUserId: string;
};

export async function getTeamMembers(companyId: string) {
  try {
    const members = await db
      .select({
        id: users.id,
        firstName: users.firstName,
        lastName: users.lastName,
        email: users.email,
        role: userCompanies.role,
        position: users.position,
        clerkUserId: users.clerkUserId,
      })
      .from(users)
      .innerJoin(
        userCompanies,
        eq(users.clerkUserId, userCompanies.clerkUserId),
      )
      .where(eq(userCompanies.companyId, companyId));

    return { success: true, data: members };
  } catch (error) {
    console.error("Error fetching team members:", error);
    return { success: false, error: "Failed to fetch team members" };
  }
}

export async function updateMemberRole(
  userId: string,
  role: "admin" | "member",
) {
  try {
    const [updated] = await db
      .update(userCompanies)
      .set({ role, updatedAt: new Date() })
      .where(eq(userCompanies.clerkUserId, userId))
      .returning();

    revalidatePath("/team");
    return { success: true, data: updated };
  } catch (error) {
    console.error("Error updating member role:", error);
    return { success: false, error: "Failed to update member role" };
  }
}

export async function removeMember(userId: string) {
  try {
    await db.delete(userCompanies).where(eq(userCompanies.clerkUserId, userId));

    revalidatePath("/team");
    return { success: true };
  } catch (error) {
    console.error("Error removing member:", error);
    return { success: false, error: "Failed to remove member" };
  }
}
