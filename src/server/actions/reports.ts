"use server";

import { db } from "@/server/db/index";
import { eq, and, desc, inArray } from "drizzle-orm";
import {
  ReportTable,
  ReviewAnswerTable,
  QuestionTable,
  CategoryTable,
  RecommendationTable,
  TaskTable,
  ReviewTable,
  type Task,
} from "@/server/drizzle/schema";
import {
  type RAGScore,
  type CategoryScore,
  type ApiResponse,
  type Report,
} from "@/types";

interface Answer {
  answer: string;
  question: string;
  category: string;
  questionId: string;
}

interface Recommendation {
  id: string;
  questionId: string;
  text: string;
  priority: string;
  estimatedEffort?: string | null;
  category: string;
  question: string;
  description: string;
}

interface ReportData {
  id: string;
  ragScores: RAGScore;
  categoryScores: CategoryScore;
  recommendations: Array<{
    id: string;
    questionId: string;
    text: string;
    priority: string;
    estimatedEffort?: string | null;
    category: string;
    question: string;
    description?: string | null;
  }>;
}

export async function generateReport(
  reviewId: string,
  userId?: string,
): Promise<ApiResponse<ReportData>> {
  try {
    // First get the review to access companyId
    const [review] = await db
      .select()
      .from(ReviewTable)
      .where(eq(ReviewTable.id, reviewId))
      .limit(1);

    if (!review) {
      return { success: false, error: "Review not found" };
    }

    // Get all answers for the review
    const answers = await db
      .select({
        answer: ReviewAnswerTable.answer,
        question: QuestionTable.text,
        category: CategoryTable.name,
        questionId: QuestionTable.id,
      })
      .from(ReviewAnswerTable)
      .innerJoin(
        QuestionTable,
        eq(ReviewAnswerTable.questionId, QuestionTable.id),
      )
      .innerJoin(CategoryTable, eq(QuestionTable.categoryId, CategoryTable.id))
      .where(eq(ReviewAnswerTable.reviewId, reviewId));

    // Calculate overall RAG scores
    const ragScores: RAGScore = answers.reduce(
      (acc, curr) => {
        acc.total++;
        switch (curr.answer) {
          case "no":
            acc.red++;
            break;
          case "partially":
            acc.amber++;
            break;
          case "yes":
            acc.green++;
            break;
          case "na":
            acc.na++;
            break;
        }
        return acc;
      },
      { red: 0, amber: 0, green: 0, na: 0, total: 0 },
    );

    // Calculate category scores
    const categoryScores = answers.reduce(
      (acc, curr) => {
        if (!acc[curr.category]) {
          acc[curr.category] = { red: 0, amber: 0, green: 0, na: 0, total: 0 };
        }

        acc[curr.category].total++;
        switch (curr.answer) {
          case "no":
            acc[curr.category].red++;
            break;
          case "partially":
            acc[curr.category].amber++;
            break;
          case "yes":
            acc[curr.category].green++;
            break;
          case "na":
            acc[curr.category].na++;
            break;
        }
        return acc;
      },
      {} as Record<
        string,
        { red: number; amber: number; green: 0; na: number; total: number }
      >,
    );

    // Get recommendations for "no" and "partially" answers
    const answersNeedingRecommendations = answers.filter(
      (a) => a.answer === "no" || a.answer === "partially",
    );

    // Get recommendations
    const recommendations = await Promise.all(
      answersNeedingRecommendations.map(async (answer) => {
        const recs = await db
          .select({
            id: RecommendationTable.id,
            text: RecommendationTable.text,
            priority: RecommendationTable.priority,
            estimatedEffort: RecommendationTable.estimatedEffort,
            questionId: RecommendationTable.questionId,
            description: RecommendationTable.description,
          })
          .from(RecommendationTable)
          .where(eq(RecommendationTable.questionId, answer.questionId));

        return recs.map((rec) => ({
          id: rec.id,
          questionId: rec.questionId,
          text: rec.text,
          priority: rec.priority,
          estimatedEffort: rec.estimatedEffort,
          category: answer.category,
          question: answer.question,
          description: rec.description,
        }));
      }),
    ).then((arrays) => arrays.flat());

    // Save report
    const [report] = await db
      .insert(ReportTable)
      .values({
        reviewId,
        companyId: review.companyId,
        ragScores,
        categoryScores,
        generatedBy: userId ?? "admin",
      })
      .returning();

    return {
      success: true,
      data: {
        id: report.id,
        ragScores,
        categoryScores,
        recommendations,
      },
    };
  } catch (error) {
    console.error("Error generating report:", error);
    return { success: false, error: "Failed to generate report" };
  }
}

export async function getReport(
  reportId: string,
): Promise<ApiResponse<Report>> {
  try {
    const [report] = await db
      .select()
      .from(ReportTable)
      .where(eq(ReportTable.id, reportId))
      .limit(1);

    if (!report) {
      return { success: false, error: "Report not found" };
    }

    return { success: true, data: report as Report };
  } catch (error) {
    console.error("Error fetching report:", error);
    return { success: false, error: "Failed to fetch report" };
  }
}

export async function getReportHistory(
  reviewId: string,
): Promise<ApiResponse<Report[]>> {
  try {
    const reports = await db
      .select()
      .from(ReportTable)
      .where(eq(ReportTable.reviewId, reviewId))
      .orderBy(desc(ReportTable.createdAt));

    return { success: true, data: reports as Report[] };
  } catch (error) {
    console.error("Error fetching report history:", error);
    return { success: false, error: "Failed to fetch report history" };
  }
}

export async function compareReports(
  reportIds: [string, string],
): Promise<ApiResponse<[Report, Report]>> {
  try {
    const reports = await db
      .select()
      .from(ReportTable)
      .where(inArray(ReportTable.id, reportIds));

    if (reports.length !== 2) {
      return { success: false, error: "Could not find both reports" };
    }

    // Ensure we have exactly two reports in the correct order
    const orderedReports: [Report, Report] = [
      reports.find((r) => r.id === reportIds[0]),
      reports.find((r) => r.id === reportIds[1]),
    ] as [Report, Report];

    if (!orderedReports[0] || !orderedReports[1]) {
      return { success: false, error: "Could not find both reports" };
    }

    return { success: true, data: orderedReports };
  } catch (error) {
    console.error("Error comparing reports:", error);
    return { success: false, error: "Failed to compare reports" };
  }
}
