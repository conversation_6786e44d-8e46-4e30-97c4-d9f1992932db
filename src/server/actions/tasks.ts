"use server";

import { db } from "@/server/db/index";
import { eq, and, desc, asc, sql, or, inArray, not, exists } from "drizzle-orm";
import {
  TaskTable,
  RecommendationTable,
  QuestionTable,
  CategoryTable,
  type TaskStatusEnum,
  type TaskPriorityEnum,
  ReviewAnswerTable,
  ReviewTable,
} from "@/server/drizzle/schema";
import { revalidatePath } from "next/cache";
import { type ApiResponse } from "@/types";
import { type TaskStats } from "@/types/task";
import { getReviewTasksAndRecommendations } from "./reviews";

// Add and export the TaskWithDetails interface
export interface TaskWithDetails {
  id: string;
  title: string;
  description: string;
  status: (typeof TaskStatusEnum.enumValues)[number];
  priority: (typeof TaskPriorityEnum.enumValues)[number];
  startDate: Date | null;
  dueDate: Date | null;
  category: string;
  assignedToUserId: string | null;
  reviewId: string;
  recommendationDetails?: {
    text: string;
    questionText: string;
    questionCategory: string;
  };
}

// Add these helper functions at the top of the file
function transformToPieChartData(tasks: any[]) {
  const statusCounts = {
    "To Do": 0,
    "In Progress": 0,
    Completed: 0,
    Archived: 0,
  };

  tasks.forEach((task) => {
    if (task.status in statusCounts) {
      statusCounts[task.status as keyof typeof statusCounts]++;
    }
  });

  return Object.entries(statusCounts).map(([browser, visitors]) => ({
    browser,
    visitors,
  }));
}

function transformToBarChartData(tasks: any[]) {
  const categoryCounts: { [key: string]: { value: number; total: number } } =
    {};

  tasks.forEach((task) => {
    if (!categoryCounts[task.category]) {
      categoryCounts[task.category] = { value: 0, total: 0 };
    }
    categoryCounts[task.category].total++;
    if (task.status === "Completed") {
      categoryCounts[task.category].value++;
    }
  });

  return Object.entries(categoryCounts).map(([title, counts]) => ({
    title,
    value: counts.value,
    total: counts.total,
  }));
}

export async function getCompanyTasks(
  companyId: string,
  filters: {
    status?: (typeof TaskStatusEnum.enumValues)[number];
    category?: string;
    priority?: (typeof TaskPriorityEnum.enumValues)[number];
    reviewId?: string;
  },
): Promise<ApiResponse<TaskWithDetails[]>> {
  try {
    // Get recommendations if reviewId is provided
    let recommendationIds: string[] | undefined;
    if (filters.reviewId) {
      const result = await getReviewTasksAndRecommendations(filters.reviewId);
      if (!result.success || !result.data) {
        throw new Error("Failed to get tasks");
      }
      recommendationIds = result.data.recommendations.map((r) => r.id);
    }

    const tasks = await db
      .select({
        id: TaskTable.id,
        title: TaskTable.title,
        status: TaskTable.status,
        category: TaskTable.category,
        reviewId: TaskTable.reviewId,
        createdAt: TaskTable.createdAt,
        updatedAt: TaskTable.updatedAt,
        companyId: TaskTable.companyId,
        description: TaskTable.description,
        priority: TaskTable.priority,
        recommendationId: TaskTable.recommendationId,
        assignedToUserId: TaskTable.assignedToUserId,
        startDate: TaskTable.startDate,
        dueDate: TaskTable.dueDate,
      })
      .from(TaskTable)
      .leftJoin(ReviewTable, eq(TaskTable.reviewId, ReviewTable.id))
      .where(
        and(
          eq(TaskTable.companyId, companyId),
          eq(ReviewTable.isArchived, false),
          filters.status ? eq(TaskTable.status, filters.status) : undefined,
          filters.category
            ? eq(TaskTable.category, filters.category)
            : undefined,
          filters.priority
            ? eq(TaskTable.priority, filters.priority)
            : undefined,
          filters.reviewId
            ? eq(TaskTable.reviewId, filters.reviewId)
            : undefined,
          recommendationIds
            ? inArray(TaskTable.recommendationId, recommendationIds)
            : undefined,
        ),
      )
      .orderBy(desc(TaskTable.createdAt));

    return { success: true, data: tasks as TaskWithDetails[] };
  } catch (error) {
    console.error("Error fetching tasks:", error);
    return { success: false, error: "Failed to fetch tasks" };
  }
}

export async function updateTaskStatus(
  taskId: string,
  status: (typeof TaskStatusEnum.enumValues)[number],
): Promise<ApiResponse<TaskWithDetails>> {
  try {
    const [updated] = await db
      .update(TaskTable)
      .set({
        status,
        updatedAt: new Date(),
      })
      .where(eq(TaskTable.id, taskId))
      .returning();

    if (!updated) {
      throw new Error("Task not found");
    }

    // Add revalidation
    revalidatePath("/tasks");
    revalidatePath(`/tasks?reviewId=${updated.reviewId}`);
    revalidatePath(`/reviews/${updated.reviewId}/feedback`);

    return { success: true, data: updated as TaskWithDetails };
  } catch (error) {
    console.error("Error updating task status:", error);
    return {
      success: false,
      error:
        error instanceof Error ? error.message : "Failed to update task status",
    };
  }
}

export async function assignTask(
  taskId: string,
  userId: string,
): Promise<ApiResponse<TaskWithDetails>> {
  try {
    const [updated] = await db
      .update(TaskTable)
      .set({
        assignedToUserId: userId,
        updatedAt: new Date(),
      })
      .where(eq(TaskTable.id, taskId))
      .returning();

    // Add revalidation for all relevant paths
    revalidatePath("/tasks");
    revalidatePath(`/tasks?reviewId=${updated.reviewId}`);
    revalidatePath("/dashboard/home");
    revalidatePath("/reviews/manage");
    revalidatePath(`/reviews/${updated.reviewId}/report`);
    revalidatePath(`/reviews/${updated.reviewId}/feedback`);

    return { success: true, data: updated as TaskWithDetails };
  } catch (error) {
    console.error("Error assigning task:", error);
    return { success: false, error: "Failed to assign task" };
  }
}

export async function getTaskStats(companyId: string, reviewId?: string) {
  try {
    if (reviewId) {
      // Use the shared query to get accurate recommendations
      const result = await getReviewTasksAndRecommendations(reviewId);
      if (!result.success || !result.data) {
        throw new Error("Failed to get tasks");
      }

      // Get status counts for these recommendations
      const tasks = await db
        .select({
          status: TaskTable.status,
          count: sql<number>`cast(count(${TaskTable.id}) as integer)`,
        })
        .from(TaskTable)
        .leftJoin(ReviewTable, eq(TaskTable.reviewId, ReviewTable.id))
        .where(
          and(
            eq(TaskTable.companyId, companyId),
            eq(TaskTable.reviewId, reviewId),
            inArray(
              TaskTable.recommendationId,
              result.data.recommendations.map((r) => r.id),
            ),
            eq(ReviewTable.isArchived, false),
          ),
        )
        .groupBy(TaskTable.status);

      const taskStats: TaskStats = {
        total: 0, // Initialize to 0
        toDo: 0,
        inProgress: 0,
        completed: 0,
        archived: 0,
        draft: 0,
      };

      // Calculate totals from actual tasks
      tasks.forEach((stat) => {
        taskStats.total += stat.count; // Add to total for each status
        switch (stat.status) {
          case "To Do":
            taskStats.toDo = stat.count;
            break;
          case "In Progress":
            taskStats.inProgress = stat.count;
            break;
          case "Completed":
            taskStats.completed = stat.count;
            break;
          case "Archived":
            taskStats.archived = stat.count;
            break;
          case "Draft":
            taskStats.draft = stat.count;
            break;
        }
      });

      return { success: true, data: taskStats };
    }

    // Handle case when no reviewId
    return {
      success: true,
      data: {
        total: 0,
        toDo: 0,
        inProgress: 0,
        completed: 0,
        archived: 0,
        draft: 0,
      },
    };
  } catch (error) {
    console.error("Error getting task stats:", error);
    return { success: false, error: "Failed to get task stats" };
  }
}

export async function getRemediationTasks(
  companyId: string,
  reviewId?: string,
) {
  try {
    const tasks = await db
      .select({
        id: TaskTable.id,
        title: TaskTable.title,
        status: TaskTable.status,
        category: TaskTable.category,
        reviewId: TaskTable.reviewId,
        createdAt: TaskTable.createdAt,
        updatedAt: TaskTable.updatedAt,
        companyId: TaskTable.companyId,
        description: TaskTable.description,
        priority: TaskTable.priority,
        recommendationId: TaskTable.recommendationId,
        assignedToUserId: TaskTable.assignedToUserId,
        startDate: TaskTable.startDate,
        dueDate: TaskTable.dueDate,
      })
      .from(TaskTable)
      .leftJoin(
        RecommendationTable,
        eq(TaskTable.recommendationId, RecommendationTable.id),
      )
      .leftJoin(
        QuestionTable,
        eq(RecommendationTable.questionId, QuestionTable.id),
      )
      .leftJoin(
        ReviewAnswerTable,
        eq(QuestionTable.id, ReviewAnswerTable.questionId),
      )
      .leftJoin(ReviewTable, eq(TaskTable.reviewId, ReviewTable.id))
      .where(
        and(
          eq(TaskTable.companyId, companyId),
          reviewId ? eq(TaskTable.reviewId, reviewId) : undefined,
          // Only include tasks for "no" and "partially" answers
          or(
            eq(ReviewAnswerTable.answer, "no"),
            eq(ReviewAnswerTable.answer, "partially"),
          ),
          eq(ReviewTable.isArchived, false),
        ),
      )
      .orderBy(desc(TaskTable.createdAt));

    const pieChartData = transformToPieChartData(tasks);
    const barChartData = transformToBarChartData(tasks);

    return {
      success: true,
      data: {
        tasks,
        pieChartData,
        barChartData,
        totalTasks: tasks.length,
      },
    };
  } catch (error) {
    console.error("Error getting remediation tasks:", error);
    return { success: false, error: "Failed to get remediation tasks" };
  }
}

export async function getDashboardRemediationTasks(
  companyId: string,
  reviewId?: string,
) {
  try {
    // Get recommendations if reviewId is provided
    let recommendationIds: string[] | undefined;
    if (reviewId) {
      const result = await getReviewTasksAndRecommendations(reviewId);
      if (!result.success || !result.data) {
        throw new Error("Failed to get tasks");
      }
      recommendationIds = result.data.recommendations.map((r) => r.id);
    }

    const tasks = await db
      .select({
        id: TaskTable.id,
        title: TaskTable.title,
        status: TaskTable.status,
        category: TaskTable.category,
        reviewId: TaskTable.reviewId,
        description: TaskTable.description,
        createdAt: TaskTable.createdAt,
        updatedAt: TaskTable.updatedAt,
        priority: TaskTable.priority,
        recommendationId: TaskTable.recommendationId,
        companyId: TaskTable.companyId,
        assignedToUserId: TaskTable.assignedToUserId,
      })
      .from(TaskTable)
      .leftJoin(ReviewTable, eq(TaskTable.reviewId, ReviewTable.id))
      .where(
        reviewId
          ? and(
              eq(TaskTable.companyId, companyId),
              eq(TaskTable.reviewId, reviewId),
              eq(ReviewTable.isArchived, false),
              recommendationIds
                ? inArray(TaskTable.recommendationId, recommendationIds)
                : undefined,
            )
          : and(
              eq(TaskTable.companyId, companyId),
              eq(ReviewTable.isArchived, false),
              // For non-review-specific queries, join with recommendations to filter
              exists(
                db
                  .select()
                  .from(RecommendationTable)
                  .leftJoin(
                    QuestionTable,
                    eq(RecommendationTable.questionId, QuestionTable.id),
                  )
                  .leftJoin(
                    ReviewAnswerTable,
                    eq(QuestionTable.id, ReviewAnswerTable.questionId),
                  )
                  .where(
                    and(
                      eq(RecommendationTable.id, TaskTable.recommendationId),
                      or(
                        eq(ReviewAnswerTable.answer, "no"),
                        eq(ReviewAnswerTable.answer, "partially"),
                      ),
                    ),
                  ),
              ),
            ),
      )
      .orderBy(desc(TaskTable.createdAt));

    const pieChartData = transformToPieChartData(tasks);
    const barChartData = transformToBarChartData(tasks);

    return {
      success: true,
      data: {
        tasks,
        pieChartData,
        barChartData,
        totalTasks: tasks.length,
      },
    };
  } catch (error) {
    console.error("Error getting dashboard remediation tasks:", error);
    return {
      success: false,
      error: "Failed to get dashboard remediation tasks",
    };
  }
}

export async function getOverallTaskStats(companyId: string) {
  try {
    // Get all active reviews first
    const reviews = await db
      .select({ id: ReviewTable.id })
      .from(ReviewTable)
      .where(
        and(
          eq(ReviewTable.companyId, companyId),
          eq(ReviewTable.isArchived, false),
        ),
      );

    // Instead of getting recommendations, let's sum up the stats from each review
    const reviewStatsPromises = reviews.map((review) =>
      getTaskStats(companyId, review.id),
    );
    const reviewStatsResults = await Promise.all(reviewStatsPromises);

    // Initialize overall stats
    const taskStats: TaskStats = {
      total: 0,
      toDo: 0,
      inProgress: 0,
      completed: 0,
      archived: 0,
      draft: 0,
    };

    // Sum up stats from each review
    reviewStatsResults.forEach((result) => {
      if (result.success && result.data) {
        taskStats.total += result.data.total;
        taskStats.toDo += result.data.toDo;
        taskStats.inProgress += result.data.inProgress;
        taskStats.completed += result.data.completed;
        taskStats.archived += result.data.archived;
        taskStats.draft += result.data.draft;
      }
    });

    return { success: true, data: taskStats };
  } catch (error) {
    console.error("Error getting overall task stats:", error);
    return { success: false, error: "Failed to get task stats" };
  }
}
