"use server";

import { db } from "@/server/db";
import { feedbacks } from "@/server/drizzle/schema";
import { auth } from "@clerk/nextjs/server";
import { revalidatePath } from "next/cache";

interface FeedbackData {
  usabilityRating: number;
  easeOfUseRating: number;
  helpfulnessRating: number;
  message: string;
  willingToReview: boolean;
  willingToPay: boolean;
  type: "post_review" | "voluntary";
}

export async function submitFeedback(data: FeedbackData) {
  try {
    const { userId } = auth();
    if (!userId) throw new Error("Not authenticated");

    console.log("Submitting feedback:", { userId, data });

    await db.insert(feedbacks).values({
      userId,
      type: data.type,
      usabilityRating: data.usabilityRating,
      easeOfUseRating: data.easeOfUseRating,
      helpfulnessRating: data.helpfulnessRating,
      message: data.message,
      willingToReview: data.willingToReview,
      willingToPay: data.willingToPay,
      createdAt: new Date(),
      updatedAt: new Date(),
    });

    revalidatePath("/admin");
    return { success: true };
  } catch (error) {
    console.error("Error submitting feedback:", error);
    return { success: false, error };
  }
}
