import { db } from "@/server/db/index";
import { InvitationTable } from "@/server/drizzle/schema";
import { eq } from "drizzle-orm";
import { nanoid } from "nanoid";

export async function createInvitation(
  reviewId: string,
  companyId: string,
  email: string,
  role: "owner" | "reviewer" | "collaborator",
) {
  try {
    const token = nanoid();

    const [invitation] = await db
      .insert(InvitationTable)
      .values({
        reviewId,
        companyId,
        email,
        role,
        token,
        status: "pending",
      })
      .returning();

    return { success: true, data: invitation };
  } catch (error) {
    console.error("Error creating invitation:", error);
    return { success: false, error: "Failed to create invitation" };
  }
}

export async function acceptInvitation(token: string, userId: string) {
  try {
    const [invitation] = await db
      .select()
      .from(InvitationTable)
      .where(eq(InvitationTable.token, token))
      .limit(1);

    if (!invitation) {
      return { success: false, error: "Invalid invitation" };
    }

    if (invitation.status !== "pending") {
      return { success: false, error: "Invitation has already been used" };
    }

    // Update invitation status
    await db
      .update(InvitationTable)
      .set({ status: "accepted" })
      .where(eq(InvitationTable.token, token));

    return {
      success: true,
      data: {
        reviewId: invitation.reviewId,
        role: invitation.role,
      },
    };
  } catch (error) {
    console.error("Error accepting invitation:", error);
    return { success: false, error: "Failed to accept invitation" };
  }
}
