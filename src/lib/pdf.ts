import PDFDocument from "pdfkit";
import { type ReportData } from "@/types";

export async function generateReportPDF(
  reportData: ReportData,
): Promise<Buffer> {
  return new Promise((resolve) => {
    const doc = new PDFDocument({
      size: "A4",
      margins: {
        top: 50,
        bottom: 50,
        left: 50,
        right: 50,
      },
      bufferPages: true,
      autoFirstPage: true,
      font: "Helvetica",
    });

    const chunks: Buffer[] = [];
    doc.on("data", (chunk) => chunks.push(chunk));
    doc.on("end", () => resolve(Buffer.concat(chunks)));

    // Header
    doc
      .fontSize(24)
      .font("Helvetica-Bold")
      .text("Assessment Report", { align: "center" });
    doc.moveDown();

    // Summary Section
    doc.fontSize(16).font("Helvetica-Bold").text("Executive Summary");
    doc.moveDown(0.5);
    doc
      .fontSize(12)
      .font("Helvetica")
      .text(`Total Questions Reviewed: ${reportData.ragScores.total}`, {
        continued: true,
      })
      .text(`  |  Green: ${reportData.ragScores.green}`, { continued: true })
      .text(`  |  Amber: ${reportData.ragScores.amber}`, { continued: true })
      .text(`  |  Red: ${reportData.ragScores.red}`);
    doc.moveDown(2);

    // Category Scores
    doc.fontSize(16).font("Helvetica-Bold").text("Category Analysis");
    doc.moveDown(0.5);

    Object.entries(reportData.categoryScores).forEach(([category, scores]) => {
      doc.fontSize(14).font("Helvetica-Bold").text(category);
      doc
        .fontSize(12)
        .font("Helvetica")
        .text(
          `Total: ${scores.total} | Green: ${scores.green} | Amber: ${scores.amber} | Red: ${scores.red}`,
        );
      doc.moveDown();
    });
    doc.moveDown();

    // Recommendations
    if (reportData.recommendations.length > 0) {
      doc.fontSize(16).font("Helvetica-Bold").text("Recommendations");
      doc.moveDown(0.5);

      reportData.recommendations.forEach((rec, index) => {
        doc
          .fontSize(12)
          .font("Helvetica-Bold")
          .text(`${index + 1}. ${rec.category}`, { continued: true })
          .font("Helvetica")
          .text(` - ${rec.priority} priority`);
        doc.fontSize(12).text(rec.recommendation);
        doc.moveDown();
      });
    }

    // Footer with page numbers
    const pages = doc.bufferedPageRange();
    for (let i = 0; i < pages.count; i++) {
      doc.switchToPage(i);
      doc
        .fontSize(10)
        .text(`Page ${i + 1} of ${pages.count}`, 50, doc.page.height - 50, {
          align: "center",
        });
    }

    doc.end();
  });
}
