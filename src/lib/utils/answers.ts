/**
 * Utility functions for handling answer values and their display
 */

export type AnswerValue = "yes" | "no" | "partially" | "na";

/**
 * Capitalizes answer values for display purposes
 * @param answer - The lowercase answer value from the database
 * @returns The capitalized display text
 */
export function capitalizeAnswer(answer: string): string {
  switch (answer) {
    case "yes":
      return "Yes";
    case "no":
      return "No";
    case "partially":
      return "Partially";
    case "na":
      return "Not Applicable";
    default:
      return answer;
  }
}

/**
 * Gets the lowercase value from a capitalized answer
 * @param displayText - The capitalized display text
 * @returns The lowercase database value
 */
export function getAnswerValue(displayText: string): AnswerValue {
  switch (displayText) {
    case "Yes":
      return "yes";
    case "No":
      return "no";
    case "Partially":
      return "partially";
    case "Not Applicable":
      return "na";
    default:
      return displayText.toLowerCase() as AnswerValue;
  }
}

/**
 * Answer mapping for consistent display across the application
 */
export const ANSWER_DISPLAY_MAP = {
  yes: "Yes",
  no: "No", 
  partially: "Partially",
  na: "Not Applicable",
} as const;

/**
 * Reverse mapping for getting values from display text
 */
export const DISPLAY_TO_VALUE_MAP = {
  "Yes": "yes",
  "No": "no",
  "Partially": "partially", 
  "Not Applicable": "na",
} as const;
