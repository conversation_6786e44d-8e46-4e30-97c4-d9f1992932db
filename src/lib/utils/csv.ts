export function downloadCSV(data: any[], filename: string) {
  // Convert feedback data to CSV format
  const headers = [
    "User Name",
    "Email",
    "Type",
    "Usability Rating",
    "Ease of Use Rating",
    "Helpfulness Rating",
    "Message",
    "Willing to Review",
    "Willing to Pay",
    "Date",
    "Time",
  ];

  const rows = data.map((feedback) => {
    const date = new Date(feedback.createdAt);
    return [
      `${feedback.user.firstName} ${feedback.user.lastName}`,
      feedback.user.email,
      feedback.type,
      feedback.usabilityRating,
      feedback.easeOfUseRating,
      feedback.helpfulnessRating,
      feedback.message || "",
      feedback.willingToReview ? "Yes" : "No",
      feedback.willingToPay ? "Yes" : "No",
      date.toLocaleDateString("en-GB", {
        day: "2-digit",
        month: "2-digit",
        year: "numeric",
      }),
      date.toLocaleTimeString(),
    ];
  });

  const csvContent = [
    headers.join(","),
    ...rows.map((row) => row.map((cell) => `"${cell}"`).join(",")),
  ].join("\n");

  // Create and trigger download
  const blob = new Blob([csvContent], { type: "text/csv;charset=utf-8;" });
  const link = document.createElement("a");
  if (link.download !== undefined) {
    const url = URL.createObjectURL(blob);
    link.setAttribute("href", url);
    link.setAttribute("download", filename);
    link.style.visibility = "hidden";
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  }
}
