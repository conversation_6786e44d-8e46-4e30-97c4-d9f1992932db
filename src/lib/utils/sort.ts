import { type QuestionWithCategory } from "@/types";
import { CATEGORY_ORDER } from "./categories";

export function sortQuestionsByCategory<
  T extends { category: string | null; text: string },
>(questions: T[]) {
  return [...questions].sort((a, b) => {
    // First sort by category using the standard order
    const categoryA = a.category ?? "Uncategorized";
    const categoryB = b.category ?? "Uncategorized";

    const indexA = CATEGORY_ORDER.indexOf(categoryA);
    const indexB = CATEGORY_ORDER.indexOf(categoryB);

    // If both categories are in the standard order, use that order
    if (indexA !== -1 && indexB !== -1) {
      return indexA - indexB;
    }

    // If only one category is in the standard order, prioritize it
    if (indexA !== -1) return -1;
    if (indexB !== -1) return 1;

    // Otherwise, fall back to alphabetical sorting
    const categoryCompare = categoryA.localeCompare(categoryB);
    if (categoryCompare !== 0) return categoryCompare;

    // Then sort by question text within each category
    return a.text.localeCompare(b.text);
  });
}
