/**
 * Defines the standard order for categories across the application
 */
export const CATEGORY_ORDER = [
  "Building & Infrastructure",
  "Equipment & Ergonomics",
  "Work culture & experience",
  "Job, Role & Time Structure",
];

/**
 * Sorts an array of categories according to the standard order
 */
export function sortCategories(categories: string[]): string[] {
  return [...categories].sort((a, b) => {
    const aIndex = CATEGORY_ORDER.indexOf(a);
    const bIndex = CATEGORY_ORDER.indexOf(b);
    return aIndex === -1 ? 1 : bIndex === -1 ? -1 : aIndex - bIndex;
  });
}

/**
 * Sorts an array of objects with a category property according to the standard order
 */
export function sortByCategory<T extends { category: string }>(items: T[]): T[] {
  return [...items].sort((a, b) => {
    const aIndex = CATEGORY_ORDER.indexOf(a.category);
    const bIndex = CATEGORY_ORDER.indexOf(b.category);
    return aIndex === -1 ? 1 : bIndex === -1 ? -1 : aIndex - bIndex;
  });
}

/**
 * Sorts entries from Object.entries() by category key according to the standard order
 */
export function sortCategoryEntries<T>(entries: [string, T][]): [string, T][] {
  return [...entries].sort(([a], [b]) => {
    const aIndex = CATEGORY_ORDER.indexOf(a);
    const bIndex = CATEGORY_ORDER.indexOf(b);
    return aIndex === -1 ? 1 : bIndex === -1 ? -1 : aIndex - bIndex;
  });
}
