"use client";
import Image from "next/image";
import Link from "next/link";
import { ChevronRight, Phone, Mail, Linkedin, Menu, X } from "lucide-react";
import { useState } from "react";
import { motion } from "framer-motion";

interface TeamMemberProps {
  name: string;
  role: string;
  description: string;
  imageUrl: string;
}

function TeamMember({ name, role, description, imageUrl }: TeamMemberProps) {
  return (
    <div className="rounded-xl bg-[#FFF8F5] p-6 shadow-md">
      <Image
        src={imageUrl || "/placeholder.svg"}
        alt={name}
        width={151}
        height={151}
        className="mx-auto mb-4 rounded-full"
      />
      <h3 className="mb-2 text-center text-xl font-bold">{name}</h3>
      <p className="mb-2 text-center font-medium text-[#e65e30]">{role}</p>
      <p className="text-center text-sm text-gray-600">{description}</p>
    </div>
  );
}

export default function WakariLanding() {
  const fadeInUp = {
    initial: { opacity: 0, y: 20 },
    animate: { opacity: 1, y: 0 },
    transition: { duration: 0.5 },
  };

  const staggerContainer = {
    animate: {
      transition: {
        staggerChildren: 0.1,
      },
    },
  };

  const scaleIn = {
    initial: { opacity: 0, scale: 0.8 },
    animate: { opacity: 1, scale: 1 },
    transition: { duration: 0.5 },
  };

  const slideInLeft = {
    initial: { opacity: 0, x: -20 },
    animate: { opacity: 1, x: 0 },
    transition: { duration: 0.5 },
  };

  const slideInRight = {
    initial: { opacity: 0, x: 20 },
    animate: { opacity: 1, x: 0 },
    transition: { duration: 0.5 },
  };

  const rotateIn = {
    initial: { opacity: 0, rotate: -10 },
    animate: { opacity: 1, rotate: 0 },
    transition: { duration: 0.5 },
  };

  const bounceIn = {
    initial: { opacity: 0, scale: 0.3 },
    animate: { opacity: 1, scale: 1 },
    transition: { type: "spring", stiffness: 300, damping: 20 },
  };

  return (
    <div className="flex min-h-screen flex-col bg-gradient-to-b from-[#FFF8F5] to-white">
      {/* Hero Section */}
      <section className="container mx-auto px-4 pt-8 md:pt-20">
        <motion.div
          initial="initial"
          animate="animate"
          variants={fadeInUp}
          className="relative mx-auto mb-8 max-w-4xl text-center md:mb-16"
        >
          <div className="absolute inset-0 -z-10 rounded-3xl bg-gradient-to-b from-[#FFF1EA] via-[#FFF8F5] to-white"></div>
          <div className="mb-4 inline-flex items-center rounded-full bg-white px-3 py-1 text-xs">
            <div className="mr-2 h-2 w-2 rounded-full bg-[#e65e30]"></div>
            <span className="font-medium">Accessibility Assessment</span>
          </div>
          <h1 className="mb-4 text-3xl font-bold md:text-4xl lg:text-5xl">
            Technology to assess and improve
            <span className="text-[#e65e30]"> accessibility maturity</span> and
            disability inclusion in the workplace
          </h1>
          <p className="mx-auto mb-8 max-w-xl text-base text-gray-600 md:text-lg">
            Transform your workplace with our digital accessibility assessment
            tool and expert training.
          </p>
          <div className="flex flex-col items-center justify-center space-y-4 sm:flex-row sm:space-x-4 sm:space-y-0">
            <Link
              href="#demo"
              className="flex w-full items-center justify-center rounded-full bg-black px-6 py-3 text-white transition-colors hover:bg-gray-900 sm:w-auto"
            >
              <svg
                className="mr-2 h-5 w-5"
                viewBox="0 0 20 20"
                fill="currentColor"
              >
                <path d="M10 12a2 2 0 100-4 2 2 0 000 4z" />
                <path
                  fillRule="evenodd"
                  d="M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z"
                  clipRule="evenodd"
                />
              </svg>
              Book a Demo
            </Link>
            <Link
              href="#contact"
              className="flex w-full items-center justify-center rounded-full border border-black px-6 py-3 transition-colors hover:bg-gray-50 sm:w-auto"
            >
              <svg
                className="mr-2 h-5 w-5"
                viewBox="0 0 20 20"
                fill="currentColor"
              >
                <path
                  fillRule="evenodd"
                  d="M14.243 5.757a6 6 0 10-.986 9.284 1 1 0 111.087 1.678A8 8 0 1118 10a3 3 0 01-4.8 2.401A4 4 0 1114 10a1 1 0 102 0c0-1.537-.586-3.07-1.757-4.243zM12 10a2 2 0 10-4 0 2 2 0 004 0z"
                  clipRule="evenodd"
                />
              </svg>
              Contact Us
            </Link>
          </div>
        </motion.div>

        {/* App Preview */}
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.3, duration: 0.5 }}
          className="relative mx-auto max-w-5xl px-4"
        >
          <div className="absolute inset-0 rounded-[40px] bg-gradient-to-b from-[#FFF1EA] via-[#FFF8F5] to-transparent"></div>
          <div className="relative">
            <div className="flex justify-center">
              <motion.div
                className="relative w-[280px] md:w-[320px]"
                whileHover={{ scale: 1.02 }}
                transition={{ type: "spring", stiffness: 300 }}
              >
                <Image
                  src="/placeholder.svg"
                  alt="Wakari App Interface"
                  width={320}
                  height={640}
                  className="rounded-[32px] shadow-xl"
                />
                {/* Floating Cards */}
                <motion.div
                  variants={rotateIn}
                  initial="initial"
                  whileInView="animate"
                  viewport={{ once: true }}
                  className="absolute -bottom-16 -left-16 md:-left-32"
                >
                  <div className="-rotate-12 transform rounded-xl bg-white p-4 shadow-lg">
                    <Image
                      src="/placeholder.svg"
                      alt="Accessibility Feature"
                      width={100}
                      height={100}
                      className="rounded-lg md:h-[120px] md:w-[120px]"
                    />
                  </div>
                </motion.div>
                <motion.div
                  variants={rotateIn}
                  initial="initial"
                  whileInView="animate"
                  viewport={{ once: true }}
                  className="absolute -bottom-8 -right-16 md:-right-32"
                >
                  <div className="rotate-12 transform rounded-xl bg-white p-4 shadow-lg">
                    <Image
                      src="/placeholder.svg"
                      alt="Accessibility Feature"
                      width={100}
                      height={100}
                      className="rounded-lg md:h-[120px] md:w-[120px]"
                    />
                  </div>
                </motion.div>
              </motion.div>
            </div>
          </div>
        </motion.div>
      </section>
      {/* Key Points Section */}
      <motion.section
        initial="initial"
        whileInView="animate"
        viewport={{ once: true }}
        variants={staggerContainer}
        className="container mx-auto px-4 pb-8 pt-16 md:pb-16 md:pt-32"
      >
        <div className="grid grid-cols-1 gap-8 sm:grid-cols-2 md:grid-cols-4">
          <motion.div
            variants={scaleIn}
            className="flex flex-col items-center rounded-xl bg-gradient-to-br from-white to-[#FFF8F5] p-6 text-center shadow-lg transition-all hover:shadow-xl"
          >
            <div className="mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-[#e65e30] text-white">
              <svg
                className="h-6 w-6"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth="2"
                  d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
                />
              </svg>
            </div>
            <h3 className="mb-2 text-lg font-bold md:text-xl">AI Assessment</h3>
            <p className="text-sm text-gray-600 md:text-base">
              Technology-driven accessibility evaluation
            </p>
          </motion.div>

          <motion.div
            variants={scaleIn}
            className="flex flex-col items-center rounded-xl bg-gradient-to-br from-white to-[#FFF8F5] p-6 text-center shadow-lg transition-all hover:shadow-xl"
          >
            <div className="mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-[#e65e30] text-white">
              <svg
                className="h-6 w-6"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth="2"
                  d="M13 10V3L4 14h7v7l9-11h-7z"
                />
              </svg>
            </div>
            <h3 className="mb-2 text-lg font-bold md:text-xl">
              Expert Training
            </h3>
            <p className="text-sm text-gray-600 md:text-base">
              Comprehensive accessibility education
            </p>
          </motion.div>

          <motion.div
            variants={scaleIn}
            className="flex flex-col items-center rounded-xl bg-gradient-to-br from-white to-[#FFF8F5] p-6 text-center shadow-lg transition-all hover:shadow-xl"
          >
            <div className="mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-[#e65e30] text-white">
              <svg
                className="h-6 w-6"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth="2"
                  d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"
                />
              </svg>
            </div>
            <h3 className="mb-2 text-lg font-bold md:text-xl">
              Progress Tracking
            </h3>
            <p className="text-sm text-gray-600 md:text-base">
              Monitor your accessibility journey
            </p>
          </motion.div>

          <motion.div
            variants={scaleIn}
            className="flex flex-col items-center rounded-xl bg-gradient-to-br from-white to-[#FFF8F5] p-6 text-center shadow-lg transition-all hover:shadow-xl"
          >
            <div className="mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-[#e65e30] text-white">
              <svg
                className="h-6 w-6"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth="2"
                  d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"
                />
              </svg>
            </div>
            <h3 className="mb-2 text-lg font-bold md:text-xl">
              Instant Results
            </h3>
            <p className="text-sm text-gray-600 md:text-base">
              Quick actionable insights
            </p>
          </motion.div>
        </div>
      </motion.section>
      {/* Features Section */}
      {/* <section className="container mx-auto px-4 py-16 md:py-32">
        <div className="grid grid-cols-1 gap-8 md:grid-cols-2 md:gap-16">
          <div>
            <div className="mb-6 flex items-start">
              <div className="mr-3 mt-1 flex h-6 w-6 items-center justify-center rounded-full bg-[#e65e30] text-xs text-white">
                1
              </div>
              <div>
                <h2 className="mb-4 text-xl font-bold md:text-2xl">
                  <span className="text-[#e65e30]">Technology</span> that
                  assesses your accessibility
                </h2>
                <p className="text-sm text-gray-600 md:text-base">
                  Our AI-driven tool helps evaluate and improve workplace
                  accessibility for everyone.
                </p>
              </div>
            </div>
            <div className="relative mt-8">
              <Image
                src="/placeholder.svg?height=300&width=400"
                alt="Technology Preview"
                width={400}
                height={300}
                className="w-full rounded-2xl"
              />
              <div className="absolute -bottom-6 -right-6 rounded-xl bg-white p-4 shadow-lg">
                <div className="flex items-center">
                  <div className="mr-3 h-8 w-8 rounded-full bg-gray-200"></div>
                  <span className="text-sm font-medium">AI Assessment</span>
                </div>
              </div>
            </div>
          </div>

          <div className="space-y-8 md:space-y-12">
            <div className="flex flex-col md:flex-row md:items-start">
              <div className="mb-4 mr-0 flex h-10 w-10 shrink-0 items-center justify-center rounded-full bg-[#e65e30] text-xs text-white md:mb-0 md:mr-4">
                2
              </div>
              <div>
                <h3 className="mb-2 text-lg font-bold md:text-xl">
                  Comprehensive Evaluation
                </h3>
                <p className="mb-2 text-sm text-gray-600 md:text-base">
                  Review and measure your organization's disability inclusion
                  and workplace accessibility practices.
                </p>
                <Link
                  href="#learn-more"
                  className="flex items-center text-sm font-medium text-[#e65e30]"
                >
                  Learn more <ChevronRight className="ml-1 h-4 w-4" />
                </Link>
              </div>
            </div>

            <div className="flex flex-col md:flex-row md:items-start">
              <div className="mb-4 mr-0 flex h-10 w-10 shrink-0 items-center justify-center rounded-full bg-[#e65e30] text-xs text-white md:mb-0 md:mr-4">
                3
              </div>
              <div>
                <h3 className="mb-2 text-lg font-bold md:text-xl">
                  Expert Training
                </h3>
                <p className="mb-2 text-sm text-gray-600 md:text-base">
                  Get accessibility awareness training for your teams and
                  leadership.
                </p>
                <Link
                  href="#learn-more"
                  className="flex items-center text-sm font-medium text-[#e65e30]"
                >
                  Learn more <ChevronRight className="ml-1 h-4 w-4" />
                </Link>
              </div>
            </div>

            <div className="flex flex-col md:flex-row md:items-start">
              <div className="mb-4 mr-0 flex h-10 w-10 shrink-0 items-center justify-center rounded-full bg-[#e65e30] text-xs text-white md:mb-0 md:mr-4">
                4
              </div>
              <div>
                <h3 className="mb-2 text-lg font-bold md:text-xl">
                  Actionable Insights
                </h3>
                <p className="mb-2 text-sm text-gray-600 md:text-base">
                  Receive practical recommendations based on research and
                  lived experiences.
                </p>
                <Link
                  href="#learn-more"
                  className="flex items-center text-sm font-medium text-[#e65e30]"
                >
                  Learn more <ChevronRight className="ml-1 h-4 w-4" />
                </Link>
              </div>
            </div>
          </div>
        </div>
      </section> */}
      {/* App Showcase */}
      <section className="container mx-auto px-4 py-16">
        <div className="grid grid-cols-1 items-center gap-8 md:grid-cols-2 md:gap-12">
          <div className="relative rounded-3xl bg-gradient-to-br from-[#FFF1EA] to-white p-8">
            <div className="mb-6 inline-flex items-center rounded-full bg-white px-3 py-1 text-xs">
              <div className="mr-2 h-2 w-2 rounded-full bg-[#e65e30]"></div>
              <span className="font-medium">Our Story</span>
            </div>
            <h2 className="mb-6 text-2xl font-bold md:text-3xl">
              Transforming workplace accessibility through technology
            </h2>
            <div className="space-y-4">
              <p className="text-sm text-gray-600 md:text-base">
                Wakari is a technology-based Accessibility Maturity review tool
                that helps organisations evaluate their workplace accessibility.
                Using our platform, governments and businesses can review and
                measure their disability inclusion and workplace accessibility
                practices.
              </p>
              <p className="text-sm text-gray-600 md:text-base">
                By leveraging our digital capabilities, organizations receive
                practical, actionable recommendations based on research and
                lived experiences of disabled people.
              </p>
              <p className="text-sm text-gray-600 md:text-base">
                Our mission is to improve overall workplace wellbeing,
                supporting staff retention, productivity, and profitability,
                while enabling organisations to attract new and disabled talent
                that would otherwise not be accessible to them.
              </p>
            </div>
          </div>
          <div className="relative">
            <h3 className="mb-6 text-xl font-bold text-[#e65e30] md:text-2xl">
              Unveil your workplace potential
            </h3>
            <div className="flex justify-center space-x-4 md:justify-start">
              <Image
                src="/placeholder.svg?height=500&width=250"
                alt="App Interface"
                width={250}
                height={500}
                className="w-[200px] rounded-2xl md:w-[250px]"
              />
              <Image
                src="/placeholder.svg?height=400&width=200"
                alt="App Interface"
                width={200}
                height={400}
                className="mt-16 w-[160px] rounded-2xl md:w-[200px]"
              />
            </div>
          </div>
        </div>
      </section>
      {/* Style Revolution */}
      <section className="container mx-auto px-4 py-16">
        <motion.div
          initial="initial"
          whileInView="animate"
          viewport={{ once: true }}
          className="mb-12 flex flex-col items-center justify-between md:flex-row"
        >
          <motion.div
            variants={slideInLeft}
            className="relative rounded-3xl bg-gradient-to-br from-[#FFF1EA] to-white p-8"
          >
            <motion.div
              variants={bounceIn}
              className="mb-4 inline-flex items-center rounded-full bg-white px-3 py-1 text-xs"
            >
              <div className="mr-2 h-2 w-2 rounded-full bg-[#e65e30]"></div>
              <span className="font-medium">Discover Features</span>
            </motion.div>
            <h2 className="text-3xl font-bold">
              Revolutionizing <span className="text-[#e65e30]">workplace</span>{" "}
              accessibility
            </h2>
          </motion.div>
          {/* <motion.div variants={slideInRight} className="mt-4 md:mt-0">
            <Link
              href="#get-started"
              className="rounded-full bg-[#e65e30] px-6 py-3 font-medium text-white transition-colors hover:bg-[#FF4C18]"
            >
              Get Started
            </Link>
          </motion.div> */}
        </motion.div>

        <motion.div
          initial="initial"
          whileInView="animate"
          viewport={{ once: true }}
          variants={staggerContainer}
          className="grid grid-cols-1 gap-6 md:grid-cols-3"
        >
          <motion.div
            variants={fadeInUp}
            whileHover={{ scale: 1.02 }}
            className="overflow-hidden rounded-2xl"
          >
            <Image
              src="/placeholder.svg?height=500&width=300"
              alt="Feature"
              width={300}
              height={500}
              className="h-full w-full object-cover"
            />
          </motion.div>
          <motion.div
            variants={scaleIn}
            whileHover={{ scale: 1.02 }}
            className="overflow-hidden rounded-2xl bg-gradient-to-br from-[#FF7E28] to-[#e65e30] p-8 text-white"
          >
            <motion.div
              variants={bounceIn}
              className="mb-6 flex h-10 w-10 items-center justify-center rounded-full bg-white text-xs text-[#e65e30]"
            >
              1
            </motion.div>
            <h3 className="mb-4 text-xl font-bold">AI-Powered Assessment</h3>
            <p>
              Get comprehensive insights and recommendations for improving
              workplace accessibility.
            </p>
          </motion.div>
          <motion.div
            variants={fadeInUp}
            whileHover={{ scale: 1.02 }}
            className="relative overflow-hidden rounded-2xl"
          >
            <Image
              src="/placeholder.svg?height=500&width=300"
              alt="Feature"
              width={300}
              height={500}
              className="h-full w-full object-cover"
            />
            <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black to-transparent p-6">
              <h3 className="mb-2 text-lg font-bold text-white">
                Expert Training
              </h3>
              <p className="text-sm text-white">
                Empower your team with accessibility knowledge
              </p>
            </div>
          </motion.div>
        </motion.div>
      </section>
      {/* Reviews Section */}
      <section className="container mx-auto px-4 py-16">
        <motion.h2
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.5 }}
          className="mb-4 text-center text-3xl font-bold"
        >
          Reviews that speak for themselves
        </motion.h2>
        <motion.div
          initial="initial"
          whileInView="animate"
          viewport={{ once: true }}
          variants={staggerContainer}
          className="mx-auto grid max-w-4xl grid-cols-1 gap-6 md:grid-cols-2"
        >
          <motion.div
            variants={fadeInUp}
            whileHover={{ scale: 1.02 }}
            className="rounded-xl bg-gradient-to-br from-white to-[#FFF8F5] p-6 shadow-sm transition-all hover:shadow-lg"
          >
            <div className="mb-4 flex items-center">
              <span className="mr-2 text-2xl font-bold">⭐⭐⭐⭐⭐</span>
              <span className="text-sm text-gray-600">(5.0)</span>
            </div>
            <p className="mb-4 text-gray-700">
              "Wakari has transformed how we approach workplace accessibility.
              The AI-driven insights have been invaluable."
            </p>
            <div className="flex items-center">
              <div className="mr-3 h-10 w-10 rounded-full bg-gray-200"></div>
              <span className="font-medium">HR Director</span>
            </div>
          </motion.div>

          <motion.div
            variants={fadeInUp}
            whileHover={{ scale: 1.02 }}
            className="rounded-xl bg-gradient-to-br from-white to-[#FFF8F5] p-6 shadow-sm transition-all hover:shadow-lg"
          >
            <div className="mb-4 flex items-center">
              <span className="mr-2 text-2xl font-bold">⭐⭐⭐⭐⭐</span>
              <span className="text-sm text-gray-600">(5.0)</span>
            </div>
            <p className="mb-4 text-gray-700">
              "The training provided by Wakari has helped us create a more
              inclusive workplace for all our employees."
            </p>
            <div className="flex items-center">
              <div className="mr-3 h-10 w-10 rounded-full bg-gray-200"></div>
              <span className="font-medium">Diversity Lead</span>
            </div>
          </motion.div>
        </motion.div>
      </section>
      {/* Newsletter */}
      <motion.section
        initial={{ opacity: 0, y: 20 }}
        whileInView={{ opacity: 1, y: 0 }}
        viewport={{ once: true }}
        transition={{ duration: 0.5 }}
        className="container mx-auto mb-12 px-4 py-8"
      >
        <div className="rounded-2xl bg-gradient-to-r from-black to-[#1A1A1A] p-8">
          <div className="mx-auto flex max-w-4xl flex-col items-center justify-between md:flex-row">
            <div className="mb-4 md:mb-0">
              <h3 className="text-xl font-bold text-white">
                Join our newsletter
              </h3>
              <p className="text-sm text-gray-400">
                Stay updated with our latest accessibility insights
              </p>
            </div>
            <div className="flex w-full md:w-auto">
              <input
                type="email"
                placeholder="Enter your email"
                className="flex-grow rounded-l-full px-6 py-3 focus:outline-none focus:ring-2 focus:ring-[#e65e30] md:w-64"
              />
              <button className="rounded-r-full bg-[#e65e30] px-8 py-3 font-medium text-white transition-colors hover:bg-[#FF4C18]">
                Subscribe
              </button>
            </div>
          </div>
        </div>
      </motion.section>

      {/* Team Section */}
      <section className="container mx-auto bg-white px-4 py-16">
        <motion.h2
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          className="mb-12 text-center text-3xl font-bold"
        >
          Our Team
        </motion.h2>
        <motion.div
          initial="initial"
          whileInView="animate"
          viewport={{ once: true }}
          variants={staggerContainer}
          className="grid grid-cols-1 gap-8 md:grid-cols-2 lg:grid-cols-3"
        >
          <motion.div variants={fadeInUp} whileHover={{ scale: 1.03 }}>
            <TeamMember
              name="Ofentse Lekwane"
              role="Founder"
              description="PM, Tech, Youth employment consulting. I am that employee and journey partner"
              imageUrl="/placeholder.svg"
            />
          </motion.div>
          {/* <motion.div variants={fadeInUp} whileHover={{ scale: 1.03 }}>
            <TeamMember
              name="Accessibility Consultant"
              role="Sales and BD"
              description="Consulting, Framework refinement. Someone to hold our partner's hand"
              imageUrl="/placeholder.svg"
            />
          </motion.div>
          <motion.div variants={fadeInUp} whileHover={{ scale: 1.03 }}>
            <TeamMember
              name="Marketing Specialist"
              role="Lead Generation"
              description="Campaigns, Social Media strategy. Someone to speak our partners' language"
              imageUrl="/placeholder.svg"
            />
          </motion.div> */}
          <motion.div variants={fadeInUp} whileHover={{ scale: 1.03 }}>
            <TeamMember
              name="Hlogi Mafolo"
              role="Product Owner"
              description="UI/UX Design, S/ware Development, Business Strategy. He speaks in code"
              imageUrl="/placeholder.svg"
            />
          </motion.div>
          <motion.div variants={fadeInUp} whileHover={{ scale: 1.03 }}>
            <TeamMember
              name="OB Mahlaba"
              role="CTO"
              description="Cloud Architecture, S/ware Development, Business strategy. He lives in the cloud"
              imageUrl="/placeholder.svg"
            />
          </motion.div>
        </motion.div>
      </section>
      {/* Founder's Story Section */}
      <section className="container mx-auto bg-[#FFF8F5] px-4 py-16">
        <h2 className="mb-12 text-center text-3xl font-bold">
          Founder's Story
        </h2>
        <div className="mx-auto max-w-4xl rounded-2xl bg-white p-8 shadow-lg">
          <div className="mb-8 flex flex-col items-center md:flex-row">
            <Image
              src="/placeholder.svg?height=200&width=200"
              alt="Ofentse Lekwane"
              width={200}
              height={200}
              className="mb-4 rounded-full md:mb-0 md:mr-8"
            />
            <div>
              <h3 className="mb-2 text-2xl font-bold">Ofentse Lekwane</h3>
              <p className="text-gray-600">Founder of Wakari</p>
            </div>
          </div>
          <div className="space-y-4 text-gray-700">
            <p>
              I have many stories of how, if life had its way, I would be living
              at the periphery of society.
            </p>
            <p>
              At eight years old, my eye doctor recommended that I be sent to a
              school for blind children, believing my limited sight would
              prevent me from thriving in mainstream education. If you know
              South Africa, you'll understand what that would have meant for my
              future—minimal education, little chance of a senior certificate,
              and an unlikely path to university. My father refused. He believed
              I deserved access to the same opportunities as anyone else. And he
              was right. I attended a mainstream school, and while I may have
              been the blindest student in my grade, I was also one of the most
              accomplished.
            </p>
            <p>
              When I graduated from university, I was nearly denied a position
              at a prestigious consulting firm—not because of my abilities, but
              because company policy required consultants to drive. My legal
              blindness made this impossible, and despite my stellar academic
              record, my competence was overshadowed by a lack of a car. It took
              an HR leader willing to break the rules to get me through the
              door. For five years, I proved that a car had nothing to do with
              my ability to deliver sharp, strategic work.
            </p>
            <p>
              These experiences drive me. They are the reason I built Wakari. No
              one should have to battle for the right to work effectively. Our
              platform exists to make sure no one else has to.
            </p>
            <p>
              With Wakari, we are starting small—for now, we want to help
              organizations become aware, understand what they're doing well and
              also where they can improve. But this is just the beginning. Our
              utopia is a complete culture shift—one where the 20% of the
              population with disabilities are no longer overlooked simply
              because of perceived limitations, but are let into work and set up
              to flourish despite and because of their disabilities. Just
              imagine it.
            </p>
          </div>
        </div>
      </section>

      {/* Get in Touch */}
      <motion.section
        initial={{ opacity: 0, y: 20 }}
        whileInView={{ opacity: 1, y: 0 }}
        viewport={{ once: true }}
        transition={{ duration: 0.5 }}
        className="container mx-auto mb-16 rounded-3xl bg-gradient-to-br from-[#111111] to-black px-4 py-16"
      >
        <div className="mx-auto max-w-4xl text-center">
          <h2 className="mb-6 text-3xl font-bold text-white">Get in Touch</h2>
          <p className="mb-8 text-gray-300">
            Have questions about our accessibility solutions? We're here to
            help.
          </p>
          <div className="mb-12 grid grid-cols-1 gap-8 md:grid-cols-3">
            <div className="rounded-xl bg-gradient-to-br from-white/15 to-white/5 p-6 backdrop-blur-sm">
              <Mail className="mx-auto mb-4 h-8 w-8 text-[#e65e30]" />
              <h3 className="mb-2 font-medium text-white">Email Us</h3>
              <p className="text-sm text-gray-300"><EMAIL></p>
            </div>
            <div className="rounded-xl bg-gradient-to-br from-white/15 to-white/5 p-6 backdrop-blur-sm">
              <Phone className="mx-auto mb-4 h-8 w-8 text-[#e65e30]" />
              <h3 className="mb-2 font-medium text-white">Call Us</h3>
              <p className="text-sm text-gray-300">+44 (0) ************</p>
            </div>
            <div className="rounded-xl bg-gradient-to-br from-white/15 to-white/5 p-6 backdrop-blur-sm">
              <Linkedin className="mx-auto mb-4 h-8 w-8 text-[#e65e30]" />
              <h3 className="mb-2 font-medium text-white">Follow Us</h3>
              <p className="text-sm text-gray-300">Connect on LinkedIn</p>
            </div>
          </div>
          <form className="mx-auto max-w-md">
            <div className="flex flex-col space-y-4">
              <input
                type="text"
                placeholder="Your name"
                className="rounded-full bg-gradient-to-br from-white/15 to-white/5 px-6 py-3 text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-[#e65e30]"
              />
              <input
                type="email"
                placeholder="Your email"
                className="rounded-full bg-gradient-to-br from-white/15 to-white/5 px-6 py-3 text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-[#e65e30]"
              />
              <textarea
                placeholder="Your message"
                rows={4}
                className="rounded-2xl bg-gradient-to-br from-white/15 to-white/5 px-6 py-4 text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-[#e65e30]"
              ></textarea>
              <button className="rounded-full bg-[#e65e30] px-8 py-3 font-medium text-white transition-colors hover:bg-[#FF4C18]">
                Send Message
              </button>
            </div>
          </form>
        </div>
      </motion.section>
    </div>
  );
}
