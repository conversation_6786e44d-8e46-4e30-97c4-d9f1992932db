"use client";

import { useState, useEffect } from "react";
import { <PERSON>, CardContent, CardHeader } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Progress } from "@/components/ui/progress";
import { Collapsible } from "@/components/ui/collapsible";
import {
  CheckCircle,
  XCircle,
  ChevronLeft,
  ChevronRight,
  MinusCircle,
  HelpCircle,
} from "lucide-react";
import Link from "next/link";
import AssessmentReviewCard from "@/components/AssessmentReviewCard";
import { ViewReportButton } from "@/components/ViewReportButton";
import { type QuestionWithCategory } from "@/types";

interface ReviewFeedbackContentProps {
  reviewId: string;
  reviewDate: Date;
  categoryScores: Record<
    string,
    { total: number; yes: number; no: number; partially: number; na: number }
  >;
  answeredQuestions: QuestionWithCategory[];
  userId: string;
  firstName: string;
  lastName: string;
}

export function ReviewFeedbackContent({
  reviewId,
  reviewDate,
  categoryScores,
  answeredQuestions,
  userId,
  firstName,
  lastName,
}: ReviewFeedbackContentProps) {
  const [selectedQuestionId, setSelectedQuestionId] = useState<string>("");
  const [currentCategoryIndex, setCurrentCategoryIndex] = useState(0);
  const [isDesktop, setIsDesktop] = useState(false);

  useEffect(() => {
    const handleResize = () => {
      setIsDesktop(window.innerWidth >= 1024);
    };

    handleResize();
    window.addEventListener("resize", handleResize);

    return () => {
      window.removeEventListener("resize", handleResize);
    };
  }, []);

  const categories = Object.keys(categoryScores);
  const currentCategory = categories[currentCategoryIndex];

  const goToNextCategory = () => {
    if (currentCategoryIndex < categories.length - 1) {
      setCurrentCategoryIndex(currentCategoryIndex + 1);
    }
  };

  const goToPreviousCategory = () => {
    if (currentCategoryIndex > 0) {
      setCurrentCategoryIndex(currentCategoryIndex - 1);
    }
  };

  const handleAnswerUpdate = async (
    questionId: string,
    answer: "yes" | "no" | "partially" | "na",
    comment?: string,
  ) => {
    // Implementation similar to ReviewFeedbackSection
  };

  const renderProgressBars = (scores: (typeof categoryScores)[string]) => (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <div className="flex w-3/4 items-center gap-2">
          <CheckCircle className="h-4 w-4 text-green-600" />
          <Progress
            value={(scores.yes / scores.total) * 100}
            className="h-2"
            indicatorClassName="bg-green-600"
          />
        </div>
        <span className="text-sm font-bold text-green-600">
          {scores.yes}/{scores.total}
        </span>
      </div>

      <div className="flex items-center justify-between">
        <div className="flex w-3/4 items-center gap-2">
          <XCircle className="h-4 w-4 text-red-600" />
          <Progress
            value={(scores.no / scores.total) * 100}
            className="h-2"
            indicatorClassName="bg-red-600"
          />
        </div>
        <span className="text-sm font-bold text-red-600">
          {scores.no}/{scores.total}
        </span>
      </div>

      <div className="flex items-center justify-between">
        <div className="flex w-3/4 items-center gap-2">
          <MinusCircle className="h-4 w-4 text-yellow-600" />
          <Progress
            value={(scores.partially / scores.total) * 100}
            className="h-2"
            indicatorClassName="bg-yellow-600"
          />
        </div>
        <span className="text-sm font-bold text-yellow-600">
          {scores.partially}/{scores.total}
        </span>
      </div>

      <div className="flex items-center justify-between">
        <div className="flex w-3/4 items-center gap-2">
          <HelpCircle className="h-4 w-4 text-gray-600" />
          <Progress
            value={(scores.na / scores.total) * 100}
            className="h-2"
            indicatorClassName="bg-gray-600"
          />
        </div>
        <span className="text-sm font-bold text-gray-600">
          {scores.na}/{scores.total}
        </span>
      </div>
    </div>
  );

  return (
    <div>
      <div className="mb-8 flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Link href="/reviews">
            <Button variant="ghost" size="icon" className="h-9 w-9">
              <ChevronLeft className="h-6 w-6" />
            </Button>
          </Link>
          <div>
            <h1 className="text-2xl font-bold">Review Feedback</h1>
            <p className="text-muted-foreground">
              Review completed on{" "}
              {new Date(reviewDate).toLocaleDateString("en-GB", {
                day: "2-digit",
                month: "2-digit",
                year: "numeric",
              })}
            </p>
          </div>
        </div>
        <ViewReportButton reviewId={reviewId} />
      </div>

      {/* Progress Overview - Desktop */}
      <div className="mb-8 hidden gap-2 md:grid md:grid-cols-2 md:gap-4 lg:grid-cols-4">
        {Object.entries(categoryScores).map(([category, scores]) => (
          <Card key={category} className="w-full border-none shadow-none">
            <CardHeader className="flex flex-row items-center space-x-2 rounded-lg border p-2 shadow-none">
              <div className="rounded-md bg-[#f7e0d8] p-2">
                {scores.yes > scores.no ? (
                  <CheckCircle className="h-6 w-6 text-primary" />
                ) : (
                  <XCircle className="h-6 w-6 text-primary" />
                )}
              </div>
              <div>
                <h2 className="text-sm font-bold">{category}</h2>
                <p className="text-sm text-gray-500">
                  {scores.total} questions
                </p>
              </div>
            </CardHeader>
            <CardContent className="px-0 py-1">
              {renderProgressBars(scores)}
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Progress Overview - Mobile */}
      <div className="block md:hidden">
        <div className="relative mb-4 flex items-start justify-between gap-2">
          <div className="absolute left-0 top-4 z-10">
            <Button
              onClick={goToPreviousCategory}
              disabled={currentCategoryIndex === 0}
              variant="outline"
              size="icon"
              className="h-10 w-10 shrink-0 rounded-full bg-gray-50"
            >
              <ChevronLeft className="h-5 w-5" />
            </Button>
          </div>

          {currentCategory && (
            <div className="mx-12 flex w-full items-center">
              <Card className="w-full border-none shadow-none">
                <CardHeader className="flex flex-row items-center space-x-2 rounded-lg border p-2 shadow-none">
                  <div className="rounded-md bg-[#f7e0d8] p-2">
                    {categoryScores[currentCategory].yes >
                    categoryScores[currentCategory].no ? (
                      <CheckCircle className="h-6 w-6 text-primary" />
                    ) : (
                      <XCircle className="h-6 w-6 text-primary" />
                    )}
                  </div>
                  <div>
                    <h2 className="text-sm font-bold">{currentCategory}</h2>
                    <p className="text-sm text-gray-500">
                      {categoryScores[currentCategory].total} questions
                    </p>
                  </div>
                </CardHeader>
                <CardContent className="px-0 py-1">
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <div className="flex w-3/4 items-center gap-2">
                        <CheckCircle className="h-4 w-4 text-green-600" />
                        <Progress
                          value={
                            (categoryScores[currentCategory].yes /
                              categoryScores[currentCategory].total) *
                            100
                          }
                          className="h-2"
                          indicatorClassName="bg-green-600"
                        />
                      </div>
                      <span className="text-sm font-bold text-green-600">
                        {categoryScores[currentCategory].yes}/
                        {categoryScores[currentCategory].total}
                      </span>
                    </div>

                    <div className="flex items-center justify-between">
                      <div className="flex w-3/4 items-center gap-2">
                        <XCircle className="h-4 w-4 text-red-600" />
                        <Progress
                          value={
                            (categoryScores[currentCategory].no /
                              categoryScores[currentCategory].total) *
                            100
                          }
                          className="h-2"
                          indicatorClassName="bg-red-600"
                        />
                      </div>
                      <span className="text-sm font-bold text-red-600">
                        {categoryScores[currentCategory].no}/
                        {categoryScores[currentCategory].total}
                      </span>
                    </div>

                    <div className="flex items-center justify-between">
                      <div className="flex w-3/4 items-center gap-2">
                        <MinusCircle className="h-4 w-4 text-yellow-600" />
                        <Progress
                          value={
                            (categoryScores[currentCategory].partially /
                              categoryScores[currentCategory].total) *
                            100
                          }
                          className="h-2"
                          indicatorClassName="bg-yellow-600"
                        />
                      </div>
                      <span className="text-sm font-bold text-yellow-600">
                        {categoryScores[currentCategory].partially}/
                        {categoryScores[currentCategory].total}
                      </span>
                    </div>

                    <div className="flex items-center justify-between">
                      <div className="flex w-3/4 items-center gap-2">
                        <HelpCircle className="h-4 w-4 text-gray-600" />
                        <Progress
                          value={
                            (categoryScores[currentCategory].na /
                              categoryScores[currentCategory].total) *
                            100
                          }
                          className="h-2"
                          indicatorClassName="bg-gray-600"
                        />
                      </div>
                      <span className="text-sm font-bold text-gray-600">
                        {categoryScores[currentCategory].na}/
                        {categoryScores[currentCategory].total}
                      </span>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          )}

          <div className="absolute right-0 top-4 z-10">
            <Button
              onClick={goToNextCategory}
              disabled={currentCategoryIndex === categories.length - 1}
              variant="outline"
              size="icon"
              className="h-10 w-10 shrink-0 rounded-full bg-gray-50"
            >
              <ChevronRight className="h-5 w-5" />
            </Button>
          </div>
        </div>
      </div>

      {/* Detailed Answers */}
      <div className="mt-8">
        <h2 className="mb-4 text-xl font-bold">Detailed Answers</h2>
        <div className="space-y-4">
          {answeredQuestions.map((question, index) => (
            <Collapsible key={question.id}>
              <AssessmentReviewCard
                key={question.id}
                question={question}
                reviewId={reviewId}
                userId={userId}
                firstName={firstName}
                lastName={lastName}
                isSelected={selectedQuestionId === question.id}
                questionNumber={index + 1}
                onAnswerUpdate={handleAnswerUpdate}
                onGuideChange={() => {}}
              />
            </Collapsible>
          ))}
        </div>
      </div>
    </div>
  );
}
