"use client";

import { useState } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Progress } from "@/components/ui/progress";
import { Collapsible } from "@/components/ui/collapsible";
import {
  ArrowLeft,
  CheckCircle,
  XCircle,
  MinusCircle,
  HelpCircle,
  FileText,
  ChevronLeft,
} from "lucide-react";
import Link from "next/link";
import AssessmentReviewCard from "@/components/AssessmentReviewCard";
import QuestionGuideCard from "@/components/QuestionGuideCard";
import { type QuestionWithCategory } from "@/types";
import { ViewReportButton } from "@/components/ViewReportButton";

interface ReviewFeedbackPageProps {
  reviewId: string;
  reviewDate: Date;
  categoryScores: Record<
    string,
    { total: number; yes: number; no: number; partially: number; na: number }
  >;
  answeredQuestions: QuestionWithCategory[];
  userId: string;
  firstName: string;
  lastName: string;
}

export function ReviewFeedbackPage({
  reviewId,
  reviewDate,
  categoryScores,
  answeredQuestions,
  userId,
  firstName,
  lastName,
}: ReviewFeedbackPageProps) {
  const [selectedQuestionId, setSelectedQuestionId] = useState<string>("");
  const [showQuestionGuide, setShowQuestionGuide] = useState(true);
  const currentQuestion = answeredQuestions.find(
    (q) => q.id === selectedQuestionId,
  );

  const handleCardSelect = (
    questionId: string,
    guide: { description: string; image: string },
  ) => {
    if (selectedQuestionId === questionId) {
      setSelectedQuestionId("");
      setShowQuestionGuide(false);
    } else {
      setSelectedQuestionId(questionId);
      setShowQuestionGuide(true);
    }
  };

  const handleGetHelpClick = () => {
    setShowQuestionGuide(true);
  };

  const handleAnswerUpdate = async (
    questionId: string,
    answer: "yes" | "no" | "partially" | "na",
    comment?: string,
  ) => {
    // Implementation similar to ReviewFeedbackSection
  };

  return (
    // <div className="container mx-auto py-6">
    <div className="mb-8 flex items-center justify-between">
      <div className="flex items-center gap-4">
        <Link href="/reviews">
          <Button variant="ghost" size="icon" className="h-9 w-9">
            <ChevronLeft className="h-6 w-6" />
          </Button>
        </Link>
        <div>
          <h1 className="text-2xl font-bold">Review Feedback</h1>
          <p className="text-muted-foreground">
            Review completed on {new Date(reviewDate).toLocaleDateString()}
          </p>
        </div>
      </div>
      <ViewReportButton reviewId={reviewId} />
      {/* </div> */}

      {/* Progress Overview */}
      <div className="mb-8 grid gap-2 md:grid-cols-2 md:gap-4 lg:grid-cols-4">
        {Object.entries(categoryScores).map(([category, scores]) => (
          <Card key={category} className="w-full border-none shadow-none">
            <CardHeader className="flex flex-row items-center space-x-2 rounded-lg border p-2 shadow-none">
              <div className="rounded-md bg-[#f7e0d8] p-2">
                {scores.yes > scores.no ? (
                  <CheckCircle className="h-6 w-6 text-primary" />
                ) : (
                  <XCircle className="h-6 w-6 text-primary" />
                )}
              </div>
              <div>
                <h2 className="text-sm font-bold">{category}</h2>
                <p className="text-sm text-gray-500">
                  {scores.total} questions
                </p>
              </div>
            </CardHeader>
            <CardContent className="px-0 py-1">
              <div className="space-y-4">
                {/* Progress bars */}
                <div className="flex items-center justify-between">
                  <div className="flex w-3/4 items-center gap-2">
                    <CheckCircle className="h-4 w-4 text-green-600" />
                    <Progress
                      value={(scores.yes / scores.total) * 100}
                      className="h-2"
                      indicatorClassName="bg-green-600"
                    />
                  </div>
                  <span className="text-sm font-bold text-green-600">
                    {scores.yes}/{scores.total}
                  </span>
                </div>
                {/* Add similar blocks for no, partially, and n/a */}
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Detailed Review */}
      <div className="grid gap-6 lg:grid-cols-3">
        <div className="lg:col-span-2">
          <div className="space-y-4">
            {answeredQuestions.map((question, index) => (
              <AssessmentReviewCard
                key={question.id}
                question={question}
                reviewId={reviewId}
                userId={userId}
                firstName={firstName}
                lastName={lastName}
                isSelected={selectedQuestionId === question.id}
                onGuideChange={(guide) => handleCardSelect(question.id, guide)}
                questionNumber={index + 1}
                onAnswerUpdate={handleAnswerUpdate}
              />
            ))}
          </div>
        </div>
        {showQuestionGuide && currentQuestion && (
          <div>
            <QuestionGuideCard questionGuide={currentQuestion.questionGuide} />
          </div>
        )}
      </div>
    </div>
  );
}
