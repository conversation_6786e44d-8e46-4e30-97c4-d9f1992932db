import { type Task } from "@/server/drizzle/schema";
import { type LucideIcon } from "lucide-react";
import {
  type TaskStatusEnum,
  type TaskPriorityEnum,
} from "@/server/drizzle/schema";

export interface Review {
  id: string;
  title: string;
  type: "basic" | "comprehensive";
  status: "draft" | "in_progress" | "completed";
  companyId: string;
  updatedAt: Date;
  createdAt: Date;
  progress?: Record<string, { total: number; answered: number }>;
}

export interface TaskStats {
  total: number;
  completed: number;
  inProgress: number;
  toDo: number;
  archived: number;
}

export interface Report {
  id: string;
  createdAt: Date;
  updatedAt: Date;
  companyId: string;
  reviewId: string;
  ragScores: RAGScore;
  categoryScores: CategoryScore;
  generatedBy: string;
  status: "generated" | "archived";
}

export interface Comment {
  id: string;
  reviewId: string;
  questionId: string;
  userId: string;
  userName: string;
  text: string;
  edited: boolean;
  createdAt: Date;
  parentId?: string;
}

export interface Notification {
  id: string;
  userId: string;
  reviewId: string;
  questionId: string;
  commentId: string;
  type: "comment" | "mention" | "reply";
  read: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export interface TaskPieChartData {
  browser: string;
  visitors: number;
}

export interface TaskBarChartData {
  title: string;
  value: number;
  total: number;
}

export interface RemediationData {
  tasks: Task[];
  pieChartData: TaskPieChartData[];
  barChartData: TaskBarChartData[];
  totalTasks: number;
}

export interface ApiResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
}

export interface RAGScore {
  red: number;
  amber: number;
  green: number;
  na?: number;
  total: number;
}

export interface CategoryScore {
  [category: string]: RAGScore;
}

export interface ReportData {
  id: string;
  ragScores: RAGScore;
  categoryScores: CategoryScore;
  recommendations: Array<{
    category: string;
    question: string;
    recommendation: string;
    priority: string;
  }>;
}

// Add a new type for review comments
export interface ReviewComment {
  id: string;
  reviewId: string;
  questionId: string;
  username: string;
  timestamp: string;
  content: string;
  category: string | null;
  questionNumber: string | null;
}

export interface SideBarProps {
  user: {
    $id: string;
    email: string;
    UserId: string;
    firstName: string;
    lastName: string;
    location?: string;
  };
}

export interface HeaderBoxProps {
  type?: "title" | "greeting";
  title: string;
  user?: string;
  subtext: string;
}

export interface FooterProps {
  className?: string;
}

export interface FAQItem {
  question: string;
  answer: string;
}

export interface FAQCardProps {
  title: string;
  description: string;
  faqs: FAQItem[];
}

export interface CommentSectionProps {
  reviewId: string;
  questionId: string;
  userId: string;
  comments: Comment[];
  onCommentAdded: (comment: Comment) => void;
}

export interface Collaborator {
  id: string;
  userId: string;
  name: string;
  email: string;
  role: "viewer" | "editor";
}

export interface CollaboratorManagementProps {
  reviewId: string;
  collaborators: Collaborator[];
  currentUserId: string;
  isOwner: boolean;
}

export interface QuestionWithCategory {
  id: string;
  text: string;
  answer?: "yes" | "no" | "partially" | "na";
  comment?: string;
  description: string;
  guideImage: string | null;
  questionGuide?: {
    description: string;
    image: string;
  };
  category: string;
  categoryId?: string;
  createdAt?: Date;
  updatedAt?: Date;
}

export interface AnswerButton {
  text: string;
  value: string;
  icon?: LucideIcon;
}

export interface AnswerButtonGroupProps {
  answers?: AnswerButton[];
  onAnswer: (value: string) => void;
  selectedAnswer?: string | null;
}

export interface Step {
  id: number;
  img?: string;
  tagline?: string;
  title: string;
  content: string;
  helptitle: string;
  helpcontent: string;
  selectOptions: Array<{ value: string; label: string }>;
  inputlabel: string;
  descr: string;
  additionalinputlabel: string;
  additionalselectOptions: Array<{ value: string; label: string }>;
  video: string;
  helpicon?: LucideIcon;
}

export interface AssessmentCardProps {
  title: string;
  description: string;
  complexity: string;
  suitability: string;
  link: string;
}

export interface Assessment {
  id: number;
  title: string;
  type: string;
  date: Date;
  tasksCompleted: number;
  totalTasks: number;
  status: string;
}

export interface TaskWithDetails {
  id: string;
  title: string;
  description: string;
  status: (typeof TaskStatusEnum.enumValues)[number];
  priority: (typeof TaskPriorityEnum.enumValues)[number];
  startDate: Date | null;
  dueDate: Date | null;
  category: string;
  assignedToUserId: string | null;
  reviewId: string;
  recommendationDetails?: {
    text: string;
    questionText: string;
    questionCategory: string;
  };
}
