// Core types
import { type TaskStats } from "./task";

export interface User {
  $id: string;
  firstName: string;
  lastName: string;
  email: string;
  UserId: string;
  location?: string;
}

// Create a separate type for SideBar
export interface SideBarUser {
  UserId: string;
  location?: string;
}

export interface Company {
  id: string;
  name: string;
  domain: string;
}

export interface CompanyDetails {
  id: string;
  companyId: string;
  teamSize?: string;
  industry?: string;
  locationCount?: string;
  headquarterCity?: string;
  headquarterCountry?: string;
  createdAt: Date;
  updatedAt: Date;
}

// Review related types
export type ReviewType = "basic" | "comprehensive";
export type ReviewStatus = "draft" | "in_progress" | "completed";
export type AnswerType = "yes" | "no" | "partially" | "na";
export type CollaboratorRole = "owner" | "reviewer" | "collaborator";

export interface Review {
  id: string;
  companyId: string;
  title: string;
  type: ReviewType;
  status: ReviewStatus;
  createdByUserId: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface Question {
  id: string;
  categoryId: string;
  text: string;
  description: string;
  guideImage: string | null;
  createdAt: Date;
  updatedAt: Date;
}

export interface QuestionWithCategory extends Question {
  category: string;
  questionGuide?: {
    description: string;
    image: string;
  };
  answer?: AnswerType;
  comment?: string;
}

export interface Answer {
  id: string;
  reviewId: string;
  questionId: string;
  answeredByUserId: string;
  answer: AnswerType;
  comment?: string;
  createdAt: Date;
  updatedAt: Date;
}

// Task related types
export interface Task {
  id: string;
  recommendationId: string;
  reviewId: string;
  companyId: string;
  title: string;
  description: string;
  status: string;
  priority: string;
  startDate: Date;
  dueDate?: Date;
  category: string;
  assignedToUserId: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface TaskWithDetails extends Task {
  companyId: string;
  recommendationDetails?: {
    text: string;
    questionText: string;
  };
}

export { type TaskStats } from "./task";

// Comment related types
export interface Comment {
  id: string;
  text: string;
  userId: string;
  userName: string;
  createdAt: Date;
  reviewId: string;
  questionId: string;
  edited: boolean;
}

// Report related types
export interface RAGScore {
  red: number;
  amber: number;
  green: number;
  na: number;
  total: number;
}

export interface CategoryScore {
  [category: string]: RAGScore;
}

export interface ReportData {
  id: string;
  ragScores: RAGScore;
  categoryScores: CategoryScore;
  recommendations: Array<{
    id: string;
    questionId: string;
    text: string;
    priority: string;
    estimatedEffort?: string | null;
    category: string;
    question: string;
    description?: string | null;
  }>;
}

// Team related types
export interface TeamMember {
  id: string;
  firstName: string;
  lastName: string;
  email: string;
  role: string;
  position?: string;
  clerkUserId: string;
}

// Component Props types
export interface StatsCardProps {
  stats: {
    initiated: number;
    inProgress: number;
    completed: number;
    lastReview: string;
  };
}

export interface TasksStatsProps {
  stats: TaskStats | null;
}

export interface CollaboratorManagementProps {
  reviewId: string;
  collaborators: Array<{
    id: string;
    email: string;
    role: CollaboratorRole;
    status: "pending" | "accepted";
    name?: string;
  }>;
  currentUserId: string;
  isOwner: boolean;
  onInvite: (email: string, role: CollaboratorRole) => Promise<void>;
  onRemove: (collaboratorId: string) => Promise<void>;
  onUpdateRole: (
    collaboratorId: string,
    newRole: CollaboratorRole,
  ) => Promise<void>;
}

// API Response types
export type ApiResponse<T> =
  | { success: true; data: T; error?: never }
  | { success: false; error: string; data?: never };

// Notification types
export interface Notification {
  id: string;
  type: "comment" | "mention" | "reply";
  reviewId: string;
  questionId: string;
  commentId: string;
  read: boolean;
  createdAt: Date;
  userName?: string;
}

// Add AssessmentReviewCardProps to the exports
export interface AssessmentReviewCardProps {
  question: QuestionWithCategory;
  reviewId: string;
  userId: string;
}

// Add Report type
export interface Report {
  id: string;
  reviewId: string;
  companyId: string;
  status: "generated" | "archived";
  ragScores: RAGScore;
  categoryScores: Record<string, RAGScore>;
  generatedBy: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface ReviewFeedbackPageProps {
  reviewId: string;
  reviewDate: Date;
  categoryScores: Record<
    string,
    { total: number; yes: number; no: number; partially: number; na: number }
  >;
  answeredQuestions: QuestionWithCategory[];
  userId: string;
}

export interface SideBarProps {
  user: {
    UserId: string;
    location?: string;
  };
}

// Add CompanyData type
export interface CompanyData {
  id: string;
  name: string;
  domain: string;
  companyId?: string;
  teamSize?: string;
  industry?: string;
  locationCount?: string;
  headquarterCity?: string;
  headquarterCountry?: string;
  createdAt?: Date;
  updatedAt?: Date;
}
