import { type QuestionWithCategory } from "@/server/actions/reviews";
import { type Comment } from "@/types/comment";

export interface AssessmentReviewCardProps {
  question: QuestionWithCategory;
  isSelected: boolean;
  onSelect: () => void;
  onQuestionUpdate: (question: QuestionWithCategory) => void;
  onGetHelpClick: () => void;
  onCommentsClick: (questionId: string) => void;
  openComments: string | null;
  comments: Comment[];
  onCommentChange: (comment: Comment) => void;
  reviewId: string;
  index: number;
  currentQuestionIndex: number;
  setCurrentQuestionIndex: (index: number) => void;
  questions: QuestionWithCategory[];
  currentAnswer: string;
  setCurrentAnswer: (answer: string) => void;
  setQuestions: (questions: QuestionWithCategory[]) => void;
  onAnswerChange: () => void;
}

export interface AssessmentProps {
  assessment: {
    id: string;
    title: string;
    type: string;
    date: string;
    status: string;
    tasksCompleted: number;
    totalTasks: number;
  };
}

export interface AssessmentProgressCardBoxProps {
  title: string;
  assignedTo: string;
  progress: number;
  total: number;
}
