export interface Report {
  id: string;
  reviewId: string;
  companyId: string;
  status: "generated" | "archived";
  ragScores: {
    red: number;
    amber: number;
    green: number;
    total: number;
  };
  categoryScores: Record<
    string,
    {
      red: number;
      amber: number;
      green: number;
      total: number;
    }
  >;
  generatedBy: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface ReportComparisonProps {
  reports: [Report, Report];
}

export interface ReportHistoryProps {
  reports: Report[];
  onCompare: (reportIds: string[]) => void;
}
