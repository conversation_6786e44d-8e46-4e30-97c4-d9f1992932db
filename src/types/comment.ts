export type Comment = {
  id: string;
  text: string;
  userId: string;
  userName: string;
  edited: boolean;
  parentId: string | null;
  createdAt: Date;
};

export type CommentResponse =
  | {
      success: true;
      data: ReviewComment;
      error?: never;
    }
  | {
      success: false;
      error: string;
      data?: never;
    };

export interface ReviewComment {
  id: string;
  reviewId: string;
  questionId: string;
  userId: string;
  firstName: string;
  lastName: string;
  text: string;
  edited: boolean;
  createdAt: Date;
  updatedAt: Date;
  source?: "comments";
}
