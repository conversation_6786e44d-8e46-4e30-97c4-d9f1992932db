import { <PERSON>ada<PERSON> } from "next";
import Link from "next/link";
import { CheckCircle } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";

export const metadata: Metadata = {
  title: "Form Submitted Successfully | <PERSON><PERSON><PERSON>",
  description: "Thank you for contacting <PERSON><PERSON><PERSON>. Your form has been submitted successfully.",
};

export default function SuccessPage() {
  return (
    <div className="container flex min-h-[calc(100vh-200px)] flex-col items-center justify-center py-20">
      <div className="mx-auto max-w-md text-center">
        <CheckCircle className="mx-auto mb-4 h-16 w-16 text-primary" />
        <h1 className="mb-2 text-3xl font-bold">Thank You!</h1>
        <p className="mb-8 text-lg text-muted-foreground">
          Your form has been submitted successfully. We'll be in touch with you shortly.
        </p>
        <div className="flex justify-center gap-4">
          <Button asChild>
            <Link href="/">Return to Home</Link>
          </Button>
        </div>
      </div>
    </div>
  );
}
