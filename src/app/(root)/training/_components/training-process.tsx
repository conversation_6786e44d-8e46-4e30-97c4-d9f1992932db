"use client";

import { motion } from "framer-motion";
import { FileSignature, Users, Clock, FileText } from "lucide-react";

interface ProcessStepProps {
  icon: React.ElementType;
  title: string;
  description: string;
  delay: number;
  stepNumber: number;
}

function ProcessStep({
  icon: Icon,
  title,
  description,
  delay,
  stepNumber,
}: ProcessStepProps) {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      whileInView={{ opacity: 1, y: 0 }}
      viewport={{ once: true }}
      transition={{ duration: 0.5, delay }}
      className="relative flex flex-col"
    >
      {/* Step number above card */}
      <div className="relative z-10 mx-auto -mb-7 flex h-14 w-14 items-center justify-center rounded-full bg-[#0a2245] text-xl font-bold text-white shadow-md">
        {stepNumber}
      </div>

      {/* Content card */}
      <div className="flex-1 rounded-xl border border-primary/10 bg-white p-6 pt-10 shadow-sm transition-all hover:shadow-md">
        <div className="mb-4 flex items-center gap-4">
          <div className="flex size-16 shrink-0 items-center justify-center rounded-[1rem] bg-[rgba(235,190,155,0.1)]">
            <Icon size={29} className="text-[#ebbe9b]" />
          </div>
          <div className="flex flex-col gap-2">
            <h3 className="font-bold">{title}</h3>
            <p className="text-balance text-muted-foreground">{description}</p>
          </div>
        </div>
      </div>
    </motion.div>
  );
}

export function TrainingProcess() {
  return (
    <section className="container flex flex-col items-center gap-4 overflow-hidden rounded-bl-[2.5rem] rounded-br-[2.5rem] rounded-tl-[1.5rem] rounded-tr-[1.5rem] bg-[#faefec] px-6 py-12 sm:gap-10 sm:rounded-bl-[5rem] sm:rounded-br-[5rem] sm:rounded-tl-[2.5rem] sm:rounded-tr-[2.5rem] sm:px-12 sm:py-24">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        whileInView={{ opacity: 1, y: 0 }}
        viewport={{ once: true }}
        transition={{ duration: 0.5 }}
        className="flex flex-col gap-2 sm:gap-3"
      >
        <span className="text-center font-bold uppercase text-primary">
          Process
        </span>
        <h2 className="text-balance text-center font-heading text-2xl font-semibold tracking-tight sm:text-3xl md:text-4xl">
          How Our Training Works
        </h2>
      </motion.div>

      <div className="relative mt-12 grid max-w-4xl gap-16 sm:mt-16">
        {/* Removed timeline line since we're using top numbers */}

        <ProcessStep
          icon={FileSignature}
          title="Signing of training contract"
          description="We begin with a formal agreement to ensure expectations are clear and both parties understand the scope of work, deliverables, and timeline."
          delay={0.1}
          stepNumber={1}
        />

        <ProcessStep
          icon={Users}
          title="Pre-engagement meeting with the lead client"
          description="A focused 1-hour requirements gathering session to understand your organization's specific needs, challenges, and goals for accessibility implementation."
          delay={0.2}
          stepNumber={2}
        />

        <ProcessStep
          icon={Clock}
          title="Training on A11Y for ERG or employee team"
          description="An engaging 90-minute interactive session designed to educate and empower your team, backed by 90 minutes of tailored preparation to ensure relevance to your industry and organization."
          delay={0.3}
          stepNumber={3}
        />

        <ProcessStep
          icon={FileText}
          title="Development of bespoke toolkit"
          description="Creation of customized resources and actionable strategies tailored to your organization's specific needs, providing a roadmap for embedding accessibility into your business operations."
          delay={0.4}
          stepNumber={4}
        />
      </div>
    </section>
  );
}
