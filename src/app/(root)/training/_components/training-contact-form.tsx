"use client";

import { useState } from "react";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Calendar, ArrowRight } from "lucide-react";

interface TrainingContactFormProps {
  className?: string;
  businessCategory?: "small" | "medium" | "enterprise";
}

export function TrainingContactForm({
  className,
  businessCategory = "small",
}: TrainingContactFormProps) {
  const [open, setOpen] = useState(false);
  const [showDateField, setShowDateField] = useState(false);

  const handleServiceDateChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    setShowDateField(e.target.value === "Provide a date");
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button
          size="lg"
          className={`group cursor-pointer gap-2 border bg-gradient-to-br from-[hsl(var(--secondary-landing))] via-[hsl(var(--primary-landing))] via-60% to-[hsl(var(--primary-landing))] transition-transform hover:scale-95 hover:opacity-90 sm:h-14 sm:px-10 sm:text-base ${className || ""}`}
        >
          Request Training
          <ArrowRight
            size={20}
            className="transition-transform group-hover:translate-x-1"
          />
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-[800px]">
        <DialogHeader>
          <DialogTitle className="text-2xl font-bold text-primary">
            Request Training
          </DialogTitle>
          <DialogDescription className="text-base text-muted-foreground">
            Fill out the form below to request accessibility training for your
            team.
          </DialogDescription>
        </DialogHeader>

        <form
          action="https://formsubmit.co/51d40e8858d4355a3b6a8048e2a389ae"
          method="POST"
          className="mt-3 space-y-3"
        >
          {/* Hidden fields for FormSubmit configuration */}
          <input
            type="hidden"
            name="_next"
            value="https://wearewakari.com/success"
          />
          <input type="hidden" name="_subject" value="New Training Request!" />
          <input
            type="hidden"
            name="_autoresponse"
            value="Thank you for your interest in Wakari's training services. We've received your request and will be in touch shortly."
          />
          <input type="hidden" name="_template" value="box" />
          <input type="hidden" name="Training Services Request" />
          <input
            type="hidden"
            name="businessCategory"
            value={businessCategory}
          />

          <div className="grid grid-cols-3 gap-3">
            <div className="space-y-2">
              <label htmlFor="firstName" className="text-sm font-medium">
                First Name
              </label>
              <input
                id="firstName"
                name="firstName"
                type="text"
                required
                className="w-full rounded-md border border-gray-300 px-3 py-1.5 text-sm"
              />
            </div>

            <div className="space-y-2">
              <label htmlFor="lastName" className="text-sm font-medium">
                Last Name
              </label>
              <input
                id="lastName"
                name="lastName"
                type="text"
                required
                className="w-full rounded-md border border-gray-300 px-3 py-1.5 text-sm"
              />
            </div>

            <div className="space-y-2">
              <label htmlFor="company" className="text-sm font-medium">
                Company
              </label>
              <input
                id="company"
                name="company"
                type="text"
                required
                className="w-full rounded-md border border-gray-300 px-3 py-1.5 text-sm"
              />
            </div>
          </div>

          <div className="grid grid-cols-3 gap-3">
            <div className="space-y-2">
              <label htmlFor="email" className="text-sm font-medium">
                Email
              </label>
              <input
                id="email"
                name="email"
                type="email"
                required
                className="w-full rounded-md border border-gray-300 px-3 py-1.5 text-sm"
              />
            </div>

            <div className="space-y-2">
              <label htmlFor="phone" className="text-sm font-medium">
                Phone
              </label>
              <input
                id="phone"
                name="phone"
                type="tel"
                required
                className="w-full rounded-md border border-gray-300 px-3 py-1.5 text-sm"
              />
            </div>

            <div className="space-y-2">
              <label htmlFor="teamSize" className="text-sm font-medium">
                Team Size
              </label>
              <input
                id="teamSize"
                name="teamSize"
                type="number"
                min="1"
                required
                className="w-full rounded-md border border-gray-300 px-3 py-1.5 text-sm"
              />
            </div>
          </div>

          <div>
            <div className="space-y-2">
              <label htmlFor="serviceDate" className="text-sm font-medium">
                Proposed Date of Training
              </label>
              <select
                id="serviceDate"
                name="serviceDate"
                required
                className="w-full rounded-md border border-gray-300 px-3 py-1.5 text-sm"
                onChange={handleServiceDateChange}
              >
                <option value="As soon as possible">As soon as possible</option>
                <option value="Not sure">Not sure</option>
                <option value="Provide a date">Provide a date</option>
              </select>
            </div>

            {showDateField && (
              <div className="mt-3 space-y-2">
                <label htmlFor="specificDate" className="text-sm font-medium">
                  Specific Date
                </label>
                <div className="relative">
                  <input
                    id="specificDate"
                    name="specificDate"
                    type="date"
                    required={showDateField}
                    className="w-full rounded-md border border-gray-300 px-3 py-1.5 text-sm"
                  />
                  <Calendar className="absolute right-3 top-2.5 h-4 w-4 text-gray-500" />
                </div>
              </div>
            )}
          </div>

          <div className="space-y-2">
            <label htmlFor="message" className="text-sm font-medium">
              Additional Information
            </label>
            <textarea
              id="message"
              name="message"
              rows={4}
              className="w-full rounded-md border border-gray-300 px-3 py-1.5 text-sm"
            ></textarea>
          </div>

          <Button
            type="submit"
            size="lg"
            className="group w-full cursor-pointer gap-2 border bg-gradient-to-br from-[hsl(var(--secondary-landing))] via-[hsl(var(--primary-landing))] via-60% to-[hsl(var(--primary-landing))] transition-transform hover:scale-95 hover:opacity-90 sm:h-14 sm:px-10 sm:text-base"
          >
            Submit Request
            <ArrowRight
              size={20}
              className="transition-transform group-hover:translate-x-1"
            />
          </Button>
        </form>
      </DialogContent>
    </Dialog>
  );
}

// Add client-side JavaScript to show/hide the date input based on selection
export const TrainingContactFormScript = () => {
  return (
    <script
      dangerouslySetInnerHTML={{
        __html: `
        document.addEventListener('DOMContentLoaded', function() {
          const serviceDate = document.getElementById('serviceDate');
          const dateInputContainer = document.getElementById('dateInputContainer');

          if (serviceDate && dateInputContainer) {
            serviceDate.addEventListener('change', function() {
              if (this.value === 'Provide a date') {
                dateInputContainer.classList.remove('hidden');
              } else {
                dateInputContainer.classList.add('hidden');
              }
            });
          }
        });
      `,
      }}
    />
  );
};
