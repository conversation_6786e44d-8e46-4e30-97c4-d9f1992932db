"use client";

import { motion } from "framer-motion";
import { Users, Building } from "lucide-react";
import Image from "next/image";

export function TrainingTarget() {
  return (
    <section className="container flex max-w-6xl flex-col gap-8 py-16 md:flex-row md:items-center md:gap-20 md:py-24">
      <div className="relative order-1 flex-1 overflow-hidden rounded-br-[2.5rem] rounded-tr-[1.5rem] bg-gradient-to-br from-secondary to-primary pl-4 pr-0 pt-8 md:order-none md:rounded-br-[5rem] md:rounded-tl-[2.5rem] md:rounded-tr-[2.5rem] md:pl-10 md:pt-16">
        <Image
          alt="Team Training Session"
          src="/images/a49c4727-9cb4-458d-9433-d20e11069b6a.jpeg"
          width={500}
          height={0}
          className="w-full max-w-full rounded-br-[2.5rem] object-contain"
        />
      </div>

      <div className="flex flex-1 flex-col items-start gap-4 md:gap-7">
        <div className="flex flex-col gap-2 md:gap-3">
          <motion.span
            initial={{ opacity: 0, x: -20 }}
            whileInView={{ opacity: 1, x: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.5 }}
            className="font-bold uppercase text-primary"
          >
            Target Market
          </motion.span>
          <motion.h2
            initial={{ opacity: 0, x: -20 }}
            whileInView={{ opacity: 1, x: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.5, delay: 0.1 }}
            className="text-balance text-left font-heading text-2xl font-bold tracking-tight text-[#0a2245] sm:text-3xl md:text-4xl"
          >
            Who Benefits Most
          </motion.h2>
        </div>

        <motion.div
          initial={{ opacity: 0, x: -20 }}
          whileInView={{ opacity: 1, x: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.5, delay: 0.2 }}
          className="space-y-6"
        >
          <div className="flex items-start gap-4">
            <div className="flex size-16 shrink-0 items-center justify-center rounded-[1rem] bg-[rgba(235,190,155,0.1)]">
              <Users size={29} className="text-[#ebbe9b]" />
            </div>
            <div className="flex flex-col gap-2">
              <h3 className="font-bold">Team Size</h3>
              <p className="text-balance text-muted-foreground">
                Ideal for teams of 5 or more members who want to improve
                workplace accessibility
              </p>
            </div>
          </div>

          <div className="flex items-start gap-4">
            <div className="flex size-16 shrink-0 items-center justify-center rounded-[1rem] bg-[rgba(235,190,155,0.1)]">
              <Building size={29} className="text-[#ebbe9b]" />
            </div>
            <div className="flex flex-col gap-2">
              <h3 className="font-bold">Industry</h3>
              <p className="text-balance text-muted-foreground">
                Industry agnostic - our training is adaptable to any sector
                committed to improving accessibility
              </p>
            </div>
          </div>
        </motion.div>
      </div>
    </section>
  );
}
