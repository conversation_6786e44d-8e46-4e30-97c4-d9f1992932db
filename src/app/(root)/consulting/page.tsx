import { Metada<PERSON> } from "next";
import { ConsultingHero } from "./_components/consulting-hero";
import { ConsultingDetails } from "./_components/consulting-details";
import { ConsultingProcess } from "./_components/consulting-process";
import { ConsultingTarget } from "./_components/consulting-target";
import { ConsultingPricing } from "./_components/consulting-pricing";

export const metadata: Metadata = {
  title: "Consulting Services | Wakari",
  description:
    "Basic workplace review delivered by Wakari consultant with written report and recommendations",
};

export default function ConsultingPage() {
  return (
    <>
      <ConsultingHero />
      <ConsultingDetails />
      <ConsultingProcess />
      <ConsultingTarget />
      <ConsultingPricing />
    </>
  );
}
