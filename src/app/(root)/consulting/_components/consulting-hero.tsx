"use client";

import { motion } from "framer-motion";
import {
  ConsultingContactForm,
  ConsultingContactFormScript,
} from "./consulting-contact-form";

export function ConsultingHero() {
  return (
    <section className="bg-landing-gradient pb-16 pt-12 md:pb-28 md:pt-20">
      <div className="container flex flex-col items-center gap-4 px-4 sm:gap-10 md:px-8">
        <motion.div
          animate={{ y: 0, opacity: 1 }}
          initial={{ y: 5, opacity: 0 }}
          transition={{ delay: 0.8, duration: 0.4 }}
          className="flex cursor-pointer items-center gap-1 rounded-full bg-[hsl(var(--secondary-landing)/0.15)] px-4 py-1 font-medium text-[hsl(var(--primary-landing))] hover:bg-[hsl(var(--secondary-landing)/0.25)]"
        >
          <span className="text-sm font-bold">Consulting Services</span>
        </motion.div>
        <motion.h1
          animate={{ y: 0, opacity: 1 }}
          initial={{ y: 10, opacity: 0 }}
          transition={{ delay: 0, duration: 0.4 }}
          className="text-balance text-center font-heading text-3xl font-bold tracking-tight sm:text-4xl md:text-5xl lg:text-6xl"
        >
          Workplace <br />
          <span className="bg-gradient-to-br from-[hsl(var(--secondary-landing))] to-[hsl(var(--primary-landing))] bg-clip-text text-transparent">
            Accessibility Review
          </span>
        </motion.h1>
        <motion.p
          animate={{ y: 0, opacity: 1 }}
          initial={{ y: 10, opacity: 0 }}
          transition={{ delay: 0.2, duration: 0.4 }}
          className="max-w-2xl px-4 text-center text-base text-muted-foreground sm:text-lg md:text-xl"
        >
          A workplace review delivered by Wakari consultants, providing you with
          a detailed written report and actionable recommendations to improve
          accessibility.
        </motion.p>
        <motion.div
          animate={{ y: 0.4, opacity: 1 }}
          initial={{ y: 10, opacity: 0 }}
          transition={{ delay: 0.4, duration: 0.4 }}
          className="flex"
        >
          <ConsultingContactForm />
        </motion.div>
        <ConsultingContactFormScript />
      </div>
    </section>
  );
}
