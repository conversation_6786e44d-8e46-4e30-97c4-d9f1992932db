"use client";

import { motion } from "framer-motion";
import {
  FileSignature,
  Users,
  Building,
  FileText,
  MessageSquare,
} from "lucide-react";

interface ProcessStepProps {
  icon: React.ElementType;
  title: string;
  description: string;
  delay: number;
  stepNumber: number;
}

function ProcessStep({
  icon: Icon,
  title,
  description,
  delay,
  stepNumber,
}: ProcessStepProps) {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      whileInView={{ opacity: 1, y: 0 }}
      viewport={{ once: true }}
      transition={{ duration: 0.5, delay }}
      className="relative flex flex-col"
    >
      {/* Step number above card */}
      <div className="relative z-10 mx-auto -mb-7 flex h-14 w-14 items-center justify-center rounded-full bg-[#0a2245] text-xl font-bold text-white shadow-md">
        {stepNumber}
      </div>

      {/* Content card */}
      <div className="flex-1 rounded-xl border border-primary/10 bg-white p-6 pt-10 shadow-sm transition-all hover:shadow-md">
        <div className="mb-4 flex items-center gap-4">
          <div className="flex size-16 shrink-0 items-center justify-center rounded-[1rem] bg-[rgba(235,190,155,0.1)]">
            <Icon size={29} className="text-[#ebbe9b]" />
          </div>
          <div className="flex flex-col gap-2">
            <h3 className="font-bold">{title}</h3>
            <p className="text-balance text-muted-foreground">{description}</p>
          </div>
        </div>
      </div>
    </motion.div>
  );
}

export function ConsultingProcess() {
  return (
    <section className="container flex flex-col items-center gap-4 overflow-hidden rounded-bl-[2.5rem] rounded-br-[2.5rem] rounded-tl-[1.5rem] rounded-tr-[1.5rem] bg-[#faefec] px-6 py-12 sm:gap-10 sm:rounded-bl-[5rem] sm:rounded-br-[5rem] sm:rounded-tl-[2.5rem] sm:rounded-tr-[2.5rem] sm:px-12 sm:py-24">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        whileInView={{ opacity: 1, y: 0 }}
        viewport={{ once: true }}
        transition={{ duration: 0.5 }}
        className="flex flex-col gap-2 sm:gap-3"
      >
        <span className="text-center font-bold uppercase text-primary">
          Process
        </span>
        <h2 className="text-balance text-center font-heading text-2xl font-semibold tracking-tight sm:text-3xl md:text-4xl">
          Our Consulting Approach
        </h2>
      </motion.div>

      <div className="relative mt-12 grid max-w-4xl gap-12 sm:mt-16 md:gap-16">
        {/* Removed timeline line since we're using top numbers */}

        <ProcessStep
          icon={FileSignature}
          title="Signing of letter of engagement"
          description="We begin with a formal agreement to ensure expectations are clear and both parties understand the scope of work, deliverables, and timeline."
          delay={0.1}
          stepNumber={1}
        />

        <ProcessStep
          icon={Users}
          title="Pre-engagement meeting"
          description="A focused requirements gathering session with the lead client to understand your organization's specific needs and objectives for the accessibility review."
          delay={0.2}
          stepNumber={2}
        />

        <ProcessStep
          icon={Building}
          title="Office walk-through review"
          description="Our consultant conducts a thorough one-day assessment of your workplace, with or without client accompaniment, to understand the layout and observe the full context."
          delay={0.3}
          stepNumber={3}
        />

        <ProcessStep
          icon={FileText}
          title="Report write-up"
          description="Our team spends one day creating a comprehensive written report with detailed findings and actionable recommendations for improving accessibility."
          delay={0.4}
          stepNumber={4}
        />

        <ProcessStep
          icon={MessageSquare}
          title="Feedback session and close out"
          description="A 60-minute session to review findings, answer questions, and ensure you have a clear understanding of the recommended next steps."
          delay={0.5}
          stepNumber={5}
        />
      </div>
    </section>
  );
}
