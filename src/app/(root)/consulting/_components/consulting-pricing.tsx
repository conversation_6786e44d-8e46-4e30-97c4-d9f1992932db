"use client";

import { useState } from "react";
import { motion } from "framer-motion";
import { Card, CardContent } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import { Check } from "lucide-react";
import { Tabs, Ta<PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import {
  ConsultingContactForm,
  ConsultingContactFormScript,
} from "./consulting-contact-form";

// Pricing data for different business sizes
const pricingData = {
  small: {
    consulting: 492,
  },
  medium: {
    consulting: 662,
  },
  enterprise: {
    consulting: 849,
  },
};

export function ConsultingPricing() {
  const [activeTab, setActiveTab] = useState("small");

  // Get current pricing based on active tab
  const currentPricing = pricingData[activeTab as keyof typeof pricingData];

  return (
    <section className="container flex flex-col items-center gap-4 px-4 pb-20 pt-12 sm:gap-6 sm:pb-32 sm:pt-24">
      <div className="flex flex-col items-center gap-2 sm:gap-3">
        <motion.span
          initial={{ opacity: 0, y: 10 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.5 }}
          className="text-center font-heading font-bold italic text-primary"
        >
          Pricing
        </motion.span>
        <motion.h2
          initial={{ opacity: 0, y: 10 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.5, delay: 0.1 }}
          className="text-balance text-center font-heading text-2xl font-bold tracking-tight sm:text-3xl md:text-4xl"
        >
          Consulting Service Package
        </motion.h2>
      </div>

      <motion.p
        initial={{ opacity: 0, y: 10 }}
        whileInView={{ opacity: 1, y: 0 }}
        viewport={{ once: true }}
        transition={{ duration: 0.5, delay: 0.2 }}
        className="max-w-lg text-balance text-center text-sm text-muted-foreground sm:text-base md:text-lg"
      >
        Invest in making your workplace more accessible and inclusive
      </motion.p>

      <motion.div
        initial={{ opacity: 0, y: 20 }}
        whileInView={{ opacity: 1, y: 0 }}
        viewport={{ once: true }}
        transition={{ duration: 0.5, delay: 0.3 }}
        className="mt-8 w-full max-w-3xl sm:mt-12"
      >
        <Tabs
          defaultValue="small"
          value={activeTab}
          onValueChange={setActiveTab}
          className="w-full"
        >
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="small">Small Business</TabsTrigger>
            <TabsTrigger value="medium">Medium Business</TabsTrigger>
            <TabsTrigger value="enterprise">Enterprise</TabsTrigger>
          </TabsList>

          <TabsContent value="small" className="mt-6">
            <Card className="h-full border-2 border-primary shadow-lg">
              <CardContent className="flex h-full flex-col items-start p-6 sm:p-8">
                <h4 className="font-heading text-2xl font-bold text-foreground sm:text-3xl">
                  Basic Review Package
                </h4>
                <div className="mt-3 sm:mt-5">
                  <span className="font-heading text-4xl font-semibold sm:text-5xl">
                    £{currentPricing.consulting}
                  </span>
                  <span className="text-sm"> /day</span>
                </div>
                <p className="mt-3 text-sm text-muted-foreground sm:mt-4 sm:text-base">
                  Comprehensive workplace accessibility review for small
                  businesses
                </p>
                <Separator orientation="horizontal" className="my-4 sm:my-6" />
                <ul className="min-h-[180px] w-full space-y-3">
                  <li className="flex items-center gap-3">
                    <div className="flex size-6 shrink-0 items-center justify-center rounded-full bg-primary/10">
                      <Check size={16} className="text-primary" />
                    </div>
                    <span className="font-semibold text-muted-foreground">
                      Full-day office walk-through assessment
                    </span>
                  </li>
                  <li className="flex items-center gap-3">
                    <div className="flex size-6 shrink-0 items-center justify-center rounded-full bg-primary/10">
                      <Check size={16} className="text-primary" />
                    </div>
                    <span className="font-semibold text-muted-foreground">
                      Comprehensive written report
                    </span>
                  </li>
                  <li className="flex items-center gap-3">
                    <div className="flex size-6 shrink-0 items-center justify-center rounded-full bg-primary/10">
                      <Check size={16} className="text-primary" />
                    </div>
                    <span className="font-semibold text-muted-foreground">
                      Practical accessibility recommendations
                    </span>
                  </li>
                  <li className="flex items-center gap-3">
                    <div className="flex size-6 shrink-0 items-center justify-center rounded-full bg-primary/10">
                      <Check size={16} className="text-primary" />
                    </div>
                    <span className="font-semibold text-muted-foreground">
                      60-minute feedback and Q&A session
                    </span>
                  </li>
                </ul>
                <ConsultingContactForm
                  className="mt-auto w-full"
                  businessCategory="small"
                />
                <ConsultingContactFormScript />
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="medium" className="mt-6">
            <Card className="h-full border-2 border-primary shadow-lg">
              <CardContent className="flex h-full flex-col items-start p-6 sm:p-8">
                <h4 className="font-heading text-2xl font-bold text-foreground sm:text-3xl">
                  Basic Review Package
                </h4>
                <div className="mt-3 sm:mt-5">
                  <span className="font-heading text-4xl font-semibold sm:text-5xl">
                    £{currentPricing.consulting}
                  </span>
                  <span className="text-sm"> /day</span>
                </div>
                <p className="mt-3 text-sm text-muted-foreground sm:mt-4 sm:text-base">
                  Comprehensive workplace accessibility review for medium-sized
                  businesses
                </p>
                <Separator orientation="horizontal" className="my-4 sm:my-6" />
                <ul className="min-h-[180px] w-full space-y-3">
                  <li className="flex items-center gap-3">
                    <div className="flex size-6 shrink-0 items-center justify-center rounded-full bg-primary/10">
                      <Check size={16} className="text-primary" />
                    </div>
                    <span className="font-semibold text-muted-foreground">
                      Full-day office walk-through assessment
                    </span>
                  </li>
                  <li className="flex items-center gap-3">
                    <div className="flex size-6 shrink-0 items-center justify-center rounded-full bg-primary/10">
                      <Check size={16} className="text-primary" />
                    </div>
                    <span className="font-semibold text-muted-foreground">
                      Comprehensive written report
                    </span>
                  </li>
                  <li className="flex items-center gap-3">
                    <div className="flex size-6 shrink-0 items-center justify-center rounded-full bg-primary/10">
                      <Check size={16} className="text-primary" />
                    </div>
                    <span className="font-semibold text-muted-foreground">
                      Practical accessibility recommendations
                    </span>
                  </li>
                  <li className="flex items-center gap-3">
                    <div className="flex size-6 shrink-0 items-center justify-center rounded-full bg-primary/10">
                      <Check size={16} className="text-primary" />
                    </div>
                    <span className="font-semibold text-muted-foreground">
                      60-minute feedback and Q&A session
                    </span>
                  </li>
                </ul>
                <ConsultingContactForm
                  className="mt-auto w-full"
                  businessCategory="medium"
                />
                <ConsultingContactFormScript />
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="enterprise" className="mt-6">
            <Card className="h-full border-2 border-primary shadow-lg">
              <CardContent className="flex h-full flex-col items-start p-6 sm:p-8">
                <h4 className="font-heading text-2xl font-bold text-foreground sm:text-3xl">
                  Basic Review Package
                </h4>
                <div className="mt-3 sm:mt-5">
                  <span className="font-heading text-4xl font-semibold sm:text-5xl">
                    £{currentPricing.consulting}
                  </span>
                  <span className="text-sm"> /day</span>
                </div>
                <p className="mt-3 text-sm text-muted-foreground sm:mt-4 sm:text-base">
                  Comprehensive workplace accessibility review for enterprise
                  businesses
                </p>
                <Separator orientation="horizontal" className="my-4 sm:my-6" />
                <ul className="min-h-[180px] w-full space-y-3">
                  <li className="flex items-center gap-3">
                    <div className="flex size-6 shrink-0 items-center justify-center rounded-full bg-primary/10">
                      <Check size={16} className="text-primary" />
                    </div>
                    <span className="font-semibold text-muted-foreground">
                      Full-day office walk-through assessment
                    </span>
                  </li>
                  <li className="flex items-center gap-3">
                    <div className="flex size-6 shrink-0 items-center justify-center rounded-full bg-primary/10">
                      <Check size={16} className="text-primary" />
                    </div>
                    <span className="font-semibold text-muted-foreground">
                      Comprehensive written report
                    </span>
                  </li>
                  <li className="flex items-center gap-3">
                    <div className="flex size-6 shrink-0 items-center justify-center rounded-full bg-primary/10">
                      <Check size={16} className="text-primary" />
                    </div>
                    <span className="font-semibold text-muted-foreground">
                      Practical accessibility recommendations
                    </span>
                  </li>
                  <li className="flex items-center gap-3">
                    <div className="flex size-6 shrink-0 items-center justify-center rounded-full bg-primary/10">
                      <Check size={16} className="text-primary" />
                    </div>
                    <span className="font-semibold text-muted-foreground">
                      60-minute feedback and Q&A session
                    </span>
                  </li>
                </ul>
                <ConsultingContactForm
                  className="mt-auto w-full"
                  businessCategory="enterprise"
                />
                <ConsultingContactFormScript />
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </motion.div>
    </section>
  );
}
