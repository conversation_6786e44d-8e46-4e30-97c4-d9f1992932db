"use client";

import { motion } from "framer-motion";
import { Clock, Users, FileText } from "lucide-react";

interface FeatureCardProps {
  icon: React.ElementType;
  title: string;
  description: string;
}

function FeatureCard({ icon: Icon, title, description }: FeatureCardProps) {
  return (
    <div className="flex h-full flex-col gap-3 rounded-xl border border-border bg-background p-6 transition-all hover:border-primary/20 hover:shadow-md">
      <div className="flex h-12 w-12 items-center justify-center rounded-full bg-primary/10">
        <Icon className="h-6 w-6 text-primary" />
      </div>
      <h3 className="font-heading text-xl font-semibold">{title}</h3>
      <p className="flex-grow text-muted-foreground">{description}</p>
    </div>
  );
}

export function ConsultingDetails() {
  return (
    <section className="container flex flex-col items-center gap-8 px-6 pb-16 pt-6 sm:gap-12 sm:pb-24 sm:pt-8">
      <div className="flex flex-col gap-2 sm:gap-3">
        <motion.span
          animate={{ y: 0, opacity: 1 }}
          initial={{ y: 5, opacity: 0 }}
          transition={{ delay: 0.2, duration: 0.4 }}
          className="text-center font-bold uppercase text-primary"
        >
          Service Overview
        </motion.span>
        <motion.h2
          animate={{ y: 0, opacity: 1 }}
          initial={{ y: 5, opacity: 0 }}
          transition={{ delay: 0.3, duration: 0.4 }}
          className="text-balance text-center font-heading text-2xl font-semibold tracking-tight sm:text-3xl md:text-4xl"
        >
          Basic Review with Limited Report
        </motion.h2>
      </div>

      <div className="grid w-full max-w-5xl grid-cols-1 gap-6 md:grid-cols-3">
        <motion.div
          animate={{ y: 0, opacity: 1 }}
          initial={{ y: 10, opacity: 0 }}
          transition={{ delay: 0.4, duration: 0.4 }}
        >
          <FeatureCard
            icon={Clock}
            title="Duration"
            description="Total preparation and delivery: 3 days. Direct engagement with client: 1 full day for office walk-through and assessment."
          />
        </motion.div>

        <motion.div
          animate={{ y: 0, opacity: 1 }}
          initial={{ y: 10, opacity: 0 }}
          transition={{ delay: 0.5, duration: 0.4 }}
        >
          <FeatureCard
            icon={Users}
            title="Approach"
            description="Non-bespoke review conducted by experienced Wakari consultants who understand accessibility requirements and best practices."
          />
        </motion.div>

        <motion.div
          animate={{ y: 0, opacity: 1 }}
          initial={{ y: 10, opacity: 0 }}
          transition={{ delay: 0.6, duration: 0.4 }}
        >
          <FeatureCard
            icon={FileText}
            title="Deliverables"
            description="Comprehensive written report with practical recommendations to improve workplace accessibility and inclusion."
          />
        </motion.div>
      </div>
    </section>
  );
}
