"use client";

import { motion } from "framer-motion";
import { Accordion } from "./ui/accordion";
import { FaqItem } from "./faq-item";

const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.2,
    },
  },
};

const itemVariants = {
  hidden: { y: 20, opacity: 0 },
  visible: {
    y: 0,
    opacity: 1,
    transition: { duration: 0.5 },
  },
};

export function Faq() {
  return (
    <section className="bg-[#faefec] pb-16 pt-12 md:pb-28 md:pt-20">
      <div className="container flex flex-col items-center gap-4 px-4 sm:gap-8">
        <motion.div
          className="flex flex-col items-center gap-2 sm:gap-3"
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true, margin: "-100px" }}
          variants={containerVariants}
        >
          <motion.span
            className="text-center font-heading font-bold italic text-[hsl(var(--primary-landing))]"
            variants={itemVariants}
          >
            FAQ
          </motion.span>
          <motion.h2
            className="text-balance text-center font-heading text-2xl font-bold tracking-tight sm:text-3xl md:text-4xl"
            variants={itemVariants}
          >
            Frequently Asked Questions
          </motion.h2>
        </motion.div>
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true, margin: "-100px" }}
          transition={{
            duration: 0.7,
            delay: 0.3,
            ease: [0.22, 1, 0.36, 1],
          }}
          className="w-full"
        >
          <Accordion
            type="single"
            collapsible
            className="mx-auto mt-4 flex w-full max-w-3xl flex-col gap-3 sm:mt-6 sm:gap-4"
          >
            <FaqItem
              answer={`Wakari offers 3 core services:

Accessibility maturity reviews - SaaS platform
Through our licensed platform, businesses get access to Wakari's workplace accessibility maturity review framework which they can use to review their workplace, and get recommendations, an action plan, a dashboard to manage actions.

Accessibility maturity reviews - In person
This service is the same as the above but instead of businesses opting to self-serve via the platform, a Wakari consultant will come conduct the accessibility review on a business' behalf and without having to purchase a platform license. The business will get access to all the above services except this time brokered by a consultant.

Accessibility awareness training and consulting
Wakari also offers DEIB training programs that help organisations become more inclusive. These come in the form of training and discussions on accessibility practices and in depth engagements on the value of DEIB practices in workplaces. It also includes development of a playbook or toolkit for the organisation to retain and refer to to embed accessibility considerations into their workplace. The trainings are offered to company leadership, employees and enterprise resource groups at a daily fee.`}
              question="What services does Wakari offer?"
            />
            <FaqItem
              answer="Our platform helps organizations identify accessibility gaps, improve employee experience, and create a more inclusive workplace culture."
              question="How can Wakari help my organization?"
            />
            <FaqItem
              answer="Yes, Wakari provides customized solutions based on your organization's size, industry, and specific accessibility needs."
              question="Do you offer customized solutions?"
            />
          </Accordion>
        </motion.div>
      </div>
    </section>
  );
}
