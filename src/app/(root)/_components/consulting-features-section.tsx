import { Clipboard<PERSON>heck, FileText, Building } from "lucide-react";
import Image from "next/image";

import { FeatureItem } from "./feature-item";

export function ConsultingFeaturesSection() {
  return (
    <section className="container flex max-w-6xl flex-col gap-6 p-16 md:flex-row md:items-center md:gap-[32px] md:pb-24 md:pt-48">
      <div className="flex flex-1 flex-col items-start gap-6 md:gap-10">
        <div className="flex flex-col gap-3">
          <h2 className="text-balance text-left font-heading text-2xl font-bold tracking-tight text-[#d6673e] sm:text-3xl md:text-4xl">
            Our Consulting Services
          </h2>
          <span className="text-left font-heading text-base font-bold italic text-[#002448] md:text-lg">
            &quot;We help you identify accessibility barriers in your workplace
            and provide actionable recommendations to create a more inclusive
            environment.&quot;
          </span>
        </div>
        <p className="hidden max-w-lg text-balance text-left text-lg text-muted-foreground">
          Our experienced consultants deliver comprehensive workplace reviews
          with detailed reports and practical recommendations.
        </p>
        <div className="flex flex-col gap-6 md:gap-8">
          <FeatureItem
            icon={ClipboardCheck}
            title="Basic Workplace Review"
            description="A thorough assessment of your workplace environment to identify accessibility barriers and opportunities for improvement."
            className="text-sm md:text-base"
          />
          <FeatureItem
            icon={FileText}
            title="Detailed Written Report"
            description="Receive a comprehensive report with specific findings and actionable recommendations tailored to your organization's needs."
            className="text-sm md:text-base"
          />
          <FeatureItem
            icon={Building}
            title="Office Walk-through"
            description="Our consultant conducts a detailed on-site assessment to understand your workplace layout and observe the full context."
            className="text-sm md:text-base"
          />
        </div>
      </div>
      <div className="relative mt-8 flex-1 rounded-br-[2.5rem] rounded-tr-[1.5rem] bg-gradient-to-br from-secondary to-primary pb-0 pl-4 pr-0 pt-8 md:mt-0 md:rounded-bl-[2.5rem] md:rounded-tl-[2.5rem] md:rounded-tr-[2.5rem] md:pl-10 md:pt-16">
        <Image
          alt="Workplace Accessibility Review"
          src="/images/a49c4727-9cb4-458d-9433-d20e11069b6a.jpeg"
          fill={false}
          width={500}
          height={500}
          className="w-full max-w-full rounded-br-[2.5rem] object-contain"
        />
      </div>
    </section>
  );
}
