"use client";

import Image from "next/image";
import { motion } from "framer-motion";

export function Section() {
  return (
    <section id="founder" className="bg-[#faefec] pb-28 pt-20">
      <motion.h2
        className="mb-12 text-balance text-center font-heading text-3xl font-bold tracking-tighter"
        initial={{ opacity: 0, y: 20 }}
        whileInView={{ opacity: 1, y: 0 }}
        viewport={{ once: true, margin: "-100px" }}
        transition={{ duration: 0.6 }}
      >
        Founder&apos;s Story
      </motion.h2>
      <motion.div
        className="mx-auto max-w-4xl rounded-3xl bg-white p-8 shadow-lg"
        initial={{ opacity: 0, y: 40 }}
        whileInView={{ opacity: 1, y: 0 }}
        viewport={{ once: true, margin: "-50px" }}
        transition={{
          duration: 0.8,
          delay: 0.2,
          ease: [0.22, 1, 0.36, 1],
        }}
      >
        <motion.div
          className="mb-8 flex flex-col items-center md:flex-row"
          initial={{ opacity: 0, scale: 0.95 }}
          whileInView={{ opacity: 1, scale: 1 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6, delay: 0.4 }}
        >
          <motion.div
            whileHover={{ scale: 1.05 }}
            transition={{ type: "spring", stiffness: 400, damping: 17 }}
          >
            <div className="overflow-hidden rounded-full">
              <Image
                alt="Ofentse Lekwane"
                src="/images/Ofentse-1.jpg"
                width={300}
                height={300}
                className="mb-2 md:mb-0 md:mr-8"
              />
            </div>
          </motion.div>
          <div>
            <motion.h3
              className="mb-2 text-2xl font-bold"
              initial={{ opacity: 0, x: -20 }}
              whileInView={{ opacity: 1, x: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.5, delay: 0.5 }}
            >
              Ofentse Lekwane
            </motion.h3>
            <motion.p
              className="text-gray-600"
              initial={{ opacity: 0, x: -20 }}
              whileInView={{ opacity: 1, x: 0 }}
              viewport={{ once: true }}
              transition={{ duration: 0.5, delay: 0.6 }}
            >
              Founder of Wakari
            </motion.p>
          </div>
        </motion.div>
        <motion.div
          className="space-y-4 text-gray-700"
          initial={{ opacity: 0 }}
          whileInView={{ opacity: 1 }}
          viewport={{ once: true }}
          transition={{ duration: 1, delay: 0.7 }}
        >
          <p>
            I have many stories of how, if life had its way, I would be living
            at the periphery of society.
          </p>
          <p>
            At eight years old, my eye doctor recommended that I be sent to a
            school for blind children, believing my limited sight would prevent
            me from thriving in mainstream education. If you know South Africa,
            you'll understand what that would have meant for my future—minimal
            education, little chance of a senior certificate, and an unlikely
            path to university. My father refused. He believed I deserved access
            to the same opportunities as anyone else. And he was right. I
            attended a mainstream school, and while I may have been the blindest
            student in my grade, I was also one of the most accomplished.
          </p>
          <p>
            When I graduated from university, I was nearly denied a position at
            a prestigious consulting firm—not because of my abilities, but
            because company policy required consultants to drive. My legal
            blindness made this impossible, and despite my stellar academic
            record, my competence was overshadowed by lack of a car. It took an
            HR leader willing to break the rules to get me through the door. For
            five years, I proved that a car had nothing to do with my ability to
            deliver sharp, strategic work.
          </p>
          <p>
            These experiences drive me. They are the reason I built Wakari. No
            one should have to battle for the right to work effectively. Our
            platform exists to make sure of this.
          </p>
          <p>
            With Wakari, we want to help organizations become accessibility
            aware, disability confident and to understand what they're doing
            well and where to improve. For us, utopia is a complete culture
            shift—where 20% of the population with disabilities are not
            overlooked because of perceived limitations; but are let into work
            and set up to flourish despite and because of their disabilities.
          </p>
          <p>Just imagine it.</p>
          <p className="font-semibold">#BeA11yReady</p>
        </motion.div>
      </motion.div>
    </section>
  );
}
