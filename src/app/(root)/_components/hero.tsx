"use client";

import { motion } from "framer-motion";
import Image from "next/image";

import { CtaButton } from "./cta-button";
import { InfiniteMovingCards } from "./InfiniteMovingCards";

export function Hero() {
  const logoItems = [
    { image: "/images/Screenshot-2025-03-21-at-14-50-38.png" },
    { image: "/images/harambee-logo-2.png" },
    { image: "/images/prospectus-removebg-preview.png" },
    { image: "/images/durham-logo.webp" },
    { image: "/images/Innovate-UK-logo.webp" },
    { image: "/images/LES.png" },
  ];

  return (
    <section
      id="home"
      className="bg-landing-gradient pb-16 pt-12 md:pb-28 md:pt-20"
    >
      <div className="container flex flex-col items-center gap-4 px-4 sm:gap-10 md:px-8">
        <motion.div
          animate={{ y: 0, opacity: 1 }}
          initial={{ y: 5, opacity: 0 }}
          transition={{ delay: 0.8, duration: 0.4 }}
          className="flex cursor-pointer items-center gap-1 rounded-full bg-[hsl(var(--secondary-landing)/0.15)] px-4 py-1 font-medium text-[hsl(var(--primary-landing))] hover:bg-[hsl(var(--secondary-landing)/0.25)]"
        >
          <span className="text-sm font-bold">Introducing Wakari</span>
        </motion.div>
        <motion.h1
          animate={{ y: 0, opacity: 1 }}
          initial={{ y: 10, opacity: 0 }}
          transition={{ delay: 0, duration: 0.4 }}
          className="text-balance text-center font-heading text-3xl font-bold tracking-tight sm:text-4xl md:text-5xl lg:text-6xl"
        >
          Attract talent and build an inclusive work culture with cutting-edge
          <br />
          <span className="bg-gradient-to-br from-[hsl(var(--secondary-landing))] to-[hsl(var(--primary-landing))] bg-clip-text text-transparent">
            accessibility reviews&nbsp; <br />
          </span>
          <span className="md:hidden"> </span>
          and expert training &amp; consultancy
        </motion.h1>
        <motion.p
          animate={{ y: 0, opacity: 1 }}
          initial={{ y: 10, opacity: 0 }}
          transition={{ delay: 0.2, duration: 0.4 }}
          className="max-w-lg px-4 text-center text-base text-muted-foreground sm:text-lg md:text-xl"
        >
          Transform your workplace with our tech-driven accessibility assessment
          tool and expert training.
        </motion.p>
        <motion.div
          animate={{ y: 0.4, opacity: 1 }}
          initial={{ y: 10, opacity: 0 }}
          transition={{ delay: 0.4, duration: 0.4 }}
          className="flex"
        >
          <CtaButton
            href="/sign-in"
            text="Get Started"
            className="my-0 ml-0 mr-3.5 flex justify-evenly p-0"
          />
        </motion.div>
        <motion.div
          animate={{ y: 0.4, opacity: 1 }}
          initial={{ y: 10, opacity: 0 }}
          transition={{ delay: 0.6, duration: 0.4 }}
          className="w-full"
        >
          <Image
            alt="SaaS Dashboard"
            src="/images/Screenshot-2025-03-21-at-11-55-27.png"
            width={1100}
            height={698}
            priority
            className="w-full rounded border"
          />
          <section className="container flex flex-col items-center gap-6 pt-12 sm:gap-10 md:pt-24">
            <h2 className="text-center text-base font-semibold leading-8 md:text-lg">
              Our funders and clients:
              <br />
            </h2>
            <div className="w-full">
              <InfiniteMovingCards
                items={logoItems}
                direction="left"
                speed="slow"
                pauseOnHover={true}
              />
            </div>
          </section>
        </motion.div>
      </div>
    </section>
  );
}
