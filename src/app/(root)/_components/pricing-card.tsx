import { Card, CardContent } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import { PricingFeatureItem } from "./pricing-feature-item";
import { CtaButton } from "./cta-button";
import { cn } from "@/lib/utils";

interface PricingCardProps {
  name: string;
  description: string;
  price: number;
  isMostPopular: boolean;
  features: string[];
  href?: string;
  className?: string;
  isMonthly?: boolean;
  isDaily?: boolean;
}

export function PricingCard({
  name,
  description,
  price,
  isMostPopular,
  features,
  href = "/sign-in",
  className,
  isMonthly = false,
  isDaily = false,
}: PricingCardProps) {
  return (
    <Card
      className={cn(
        "relative mx-auto h-full w-full max-w-sm border-0 shadow-lg",
        className,
      )}
    >
      <CardContent className="flex h-full flex-col items-start p-6 sm:p-8">
        <h4 className="font-heading text-2xl font-bold text-foreground sm:text-3xl">
          {name}
        </h4>
        <div className="mt-3 sm:mt-5">
          <span className="font-heading text-4xl font-semibold sm:text-5xl">
            £{price}
          </span>
          <span className="text-sm">
            {" "}
            {isMonthly ? "/month" : isDaily ? "/day" : "/"}
          </span>
        </div>
        <p className="mt-3 text-sm text-muted-foreground sm:mt-4 sm:text-base">
          {description}
        </p>
        <Separator orientation="horizontal" className="my-4 sm:my-6" />
        <ul className="min-h-[180px] w-full space-y-1 sm:space-y-2">
          {features.map(
            (feature, index) =>
              feature && <PricingFeatureItem key={index} text={feature} />,
          )}
        </ul>
        <CtaButton
          href={href}
          text="Get Started"
          className="mt-auto w-full pt-6 sm:pt-8"
        />
        {/* <p className="mx-auto mt-3 text-balance text-center text-xs text-muted-foreground sm:mt-4 sm:text-sm">
          No credit card required
        </p> */}
      </CardContent>
      {isMostPopular === true && (
        <span className="absolute inset-x-0 -top-4 mx-auto w-28 rounded-full bg-primary bg-gradient-to-br from-secondary px-2 py-1 text-center text-xs font-semibold text-primary-foreground shadow-md sm:-top-5 sm:w-32 sm:px-3 sm:py-2 sm:text-sm">
          Most popular
        </span>
      )}
    </Card>
  );
}
