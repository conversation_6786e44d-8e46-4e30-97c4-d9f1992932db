import { Check } from "lucide-react";
import { cn } from "@/lib/utils";

interface PricingFeatureItemProps {
  text: string;
  className?: string;
}

export function PricingFeatureItem({
  text,
  className,
}: PricingFeatureItemProps) {
  if (!text) return null;

  return (
    <li className={cn("flex items-center gap-3", className)}>
      <div className="flex size-6 shrink-0 items-center justify-center rounded-full bg-primary/10">
        <Check size={16} className="text-primary" />
      </div>
      <span className="font-semibold text-muted-foreground">{text}</span>
    </li>
  );
}
