import { Search, CreditCard } from "lucide-react";
import Image from "next/image";

import { FeatureItem } from "./feature-item";

export function FeaturesSection() {
  return (
    <section
      id="features"
      className="container flex max-w-6xl flex-col gap-6 py-16 md:flex-row md:items-center md:gap-[32px] md:pb-24 md:pt-48"
    >
      <div className="flex flex-1 flex-col items-start gap-6 md:gap-10">
        <div className="flex flex-col gap-3">
          <h2 className="text-balance text-left font-heading text-2xl font-bold tracking-tight text-[#d6673e] sm:text-3xl md:text-4xl">
            Our Technology
          </h2>
          <span className="text-left font-heading text-base font-bold italic text-[#002448] md:text-lg">
            &quot;Your website says you are inclusive, but it is not legible to
            a screen reader, we want to help you identify this and other
            inconsistencies to improve them.&quot;
          </span>
        </div>
        <p className="hidden max-w-lg text-balance text-left text-lg text-muted-foreground">
          Gain valuable insights to make informed decisions and optimize your
          strategy for continued success.
        </p>
        <div className="flex flex-col gap-6 md:gap-8">
          <FeatureItem
            icon={Search}
            title="Accessibility Maturity Review"
            description=" Conduct a basic or comprehensive review to determine how accessibility friendly or disability inclusive your workplace is and where to improve. "
            className="text-sm md:text-base"
          />
        </div>
      </div>
      <div className="relative mt-8 flex-1 rounded-bl-[1.5rem] rounded-tl-[1.5rem] rounded-tr-[1.5rem] bg-gradient-to-br from-secondary to-primary pb-0 pl-4 pr-0 pt-8 md:mt-0 md:rounded-bl-[2.5rem] md:rounded-tl-[2.5rem] md:rounded-tr-[2.5rem] md:pl-10 md:pt-16">
        <Image
          alt="SaaS Dashboard"
          src="/images/Screenshot-2025-03-21-at-12-36-09.png"
          fill={false}
          width={500}
          height={500}
          className="w-full max-w-full object-contain"
        />
      </div>
    </section>
  );
}
