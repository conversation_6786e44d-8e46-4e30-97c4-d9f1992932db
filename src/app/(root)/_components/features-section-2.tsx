"use client";

import { <PERSON>, <PERSON><PERSON>he<PERSON>, Gauge, Lightbulb } from "lucide-react";
import { motion } from "framer-motion";

import { FeatureHoverCard } from "./feature-hover-card";
import { CtaButton } from "./cta-button";

export function Features() {
  return (
    <section className="container flex flex-col items-center gap-4 from-secondary/30 via-primary/30 to-primary/30 px-6 pt-12 sm:gap-7 sm:pt-24">
      <div className="flex flex-col gap-2 sm:gap-3">
        <span className="text-center font-bold uppercase text-primary">
          Features
        </span>
        <h2 className="text-balance text-center font-heading text-2xl font-semibold tracking-tight sm:text-3xl md:text-4xl">
          How it works
        </h2>
      </div>
      <p className="max-w-xl text-balance text-center text-base text-muted-foreground sm:text-lg">
        <PERSON><PERSON><PERSON> is different. We help you manage the process beyond the report
        with actionable recommendations and task management.
      </p>
      <div className="relative z-10 mx-auto grid w-full max-w-7xl grid-cols-1 gap-4 py-8 sm:gap-0 sm:py-10 md:grid-cols-2 lg:grid-cols-3">
        <FeatureHoverCard
          icon={ListChecks}
          title="Conduct reviews"
          description="Start a review with easy to follow flow and guidelines."
          className="lg:border-l"
        />
        <FeatureHoverCard
          icon={Gauge}
          title="Generate reports "
          description="Generate reports with findings & recommended tasks to action"
        />
        <FeatureHoverCard
          icon={Lightbulb}
          title="Improve your workplace"
          description="Action recommended tasks to improve accessibility"
        />
      </div>
      <motion.div
        animate={{ y: 0.4, opacity: 1 }}
        initial={{ y: 10, opacity: 0 }}
        transition={{ delay: 0.4, duration: 0.4 }}
        className="flex w-full justify-center pt-2 sm:pt-3.5"
      >
        <CtaButton
          href="/sign-in"
          text="Get started"
          className="my-0 flex justify-evenly p-0"
        />
      </motion.div>
    </section>
  );
}
