"use client";

import { motion } from "framer-motion";
import { Mail, Phone, Linkedin } from "lucide-react";

export function Section2() {
  return (
    <motion.section
      id="contact"
      initial={{ y: 20, opacity: 0 }}
      viewport={{ once: true }}
      transition={{ duration: 0.5 }}
      whileInView={{ y: 0, opacity: 1 }}
      className="container mx-auto my-16 rounded-bl-[2.5rem] rounded-br-[2.5rem] rounded-tl-[2.5rem] rounded-tr-[2.5rem] bg-[#0a2245] px-4 py-16 tracking-tighter"
    >
      <div className="mx-auto max-w-4xl text-center">
        <h2 className="mb-6 font-heading text-3xl font-bold text-white sm:text-4xl">
          Get in Touch
        </h2>
        <p className="mb-8 text-balance text-lg text-[#0a2245]">
          Have questions about our accessibility solutions? We&apos;re here to
          help.
        </p>
        <div className="mb-12 grid grid-cols-1 gap-8 md:grid-cols-3">
          <div className="rounded-xl bg-white bg-gradient-to-br from-white/15 to-white/5 p-6 backdrop-blur-sm">
            <a href="mailto:<EMAIL>" className="block">
              <Mail className="mx-auto mb-4 h-8 w-8 text-[#e65e30]" />
              <h3 className="mb-2 font-medium">Email Us</h3>
              <p className="text-sm"><EMAIL></p>
            </a>
          </div>
          <div className="rounded-xl bg-white p-6">
            <a href="tel:+447424988508" className="block">
              <Phone className="mx-auto mb-4 h-8 w-8 text-[#e65e30]" />
              <h3 className="mb-2 font-medium">Call Us</h3>
              <p className="text-sm">+44 ************</p>
            </a>
          </div>
          <div className="rounded-xl bg-white bg-gradient-to-br from-white/15 to-white/5 p-6 backdrop-blur-sm">
            <a
              href="https://www.linkedin.com/company/wakari-limited/?originalSubdomain=uk"
              target="_blank"
              rel="noopener noreferrer"
              className="block"
            >
              <Linkedin className="mx-auto mb-4 h-8 w-8 text-[#e65e30]" />
              <h3 className="mb-2 font-medium">Follow Us</h3>
              <p className="text-sm">Connect on LinkedIn</p>
            </a>
          </div>
        </div>
        <form
          action="https://formsubmit.co/51d40e8858d4355a3b6a8048e2a389ae"
          method="POST"
          className="mx-auto"
        >
          {/* Hidden fields for FormSubmit configuration */}
          <input
            type="hidden"
            name="_next"
            value="https://wearewakari.com/success"
          />
          <input
            type="hidden"
            name="_subject"
            value="New Contact Form Submission!"
          />
          <input
            type="hidden"
            name="_autoresponse"
            value="Thank you for contacting Wakari. We've received your message and will be in touch shortly."
          />
          <input type="hidden" name="_template" value="box" />

          <div className="flex flex-col space-y-4">
            <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
              <input
                type="text"
                name="firstName"
                placeholder="First name"
                required
                className="rounded-lg bg-gradient-to-br from-white/15 to-white/5 px-6 py-3 placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-[#e65e30]"
              />
              <input
                type="text"
                name="lastName"
                placeholder="Last name"
                required
                className="rounded-lg bg-gradient-to-br from-white/15 to-white/5 px-6 py-3 placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-[#e65e30]"
              />
            </div>
            <input
              type="email"
              name="email"
              placeholder="Your email"
              required
              className="rounded-lg bg-gradient-to-br from-white/15 to-white/5 px-6 py-3 placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-[#e65e30]"
            />
            <input
              type="tel"
              name="phone"
              placeholder="Your phone number"
              className="rounded-lg bg-gradient-to-br from-white/15 to-white/5 px-6 py-3 placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-[#e65e30]"
            />
            <input
              type="text"
              name="company"
              placeholder="Your company"
              className="rounded-lg bg-gradient-to-br from-white/15 to-white/5 px-6 py-3 placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-[#e65e30]"
            />
            <textarea
              rows={4}
              name="message"
              placeholder="Your message"
              required
              className="rounded-2xl bg-gradient-to-br from-white/15 to-white/5 px-6 py-4 placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-[#e65e30]"
            />
            <button
              type="submit"
              className="group flex items-center justify-center gap-2 rounded-lg border px-8 py-3 font-medium text-white transition-all hover:scale-95 hover:border-[#ff4c18] hover:bg-[#FF4C18]"
            >
              Send Message
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="20"
                height="20"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
                className="transition-transform group-hover:translate-x-1"
              >
                <path d="M5 12h14"></path>
                <path d="m12 5 7 7-7 7"></path>
              </svg>
            </button>
          </div>
        </form>
      </div>
    </motion.section>
  );
}
