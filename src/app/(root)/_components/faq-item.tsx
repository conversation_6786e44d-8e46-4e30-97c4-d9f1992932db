"use client";

import { motion } from "framer-motion";
import {
  AccordionItem,
  AccordionTrigger,
  AccordionContent,
} from "@/components/ui/accordion";
import { cn } from "@/lib/utils";

interface FaqItemProps {
  question: string;
  answer: string;
  className?: string;
}

export function FaqItem({ question, answer, className }: FaqItemProps) {
  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.95 }}
      whileInView={{ opacity: 1, scale: 1 }}
      viewport={{ once: true }}
      transition={{ duration: 0.4 }}
      whileHover={{ scale: 1.01, transition: { duration: 0.2 } }}
    >
      <AccordionItem
        value={question}
        className={cn(
          "rounded-xl border-b-0 bg-card px-4 py-2 sm:rounded-3xl sm:px-8 sm:py-3",
          className,
        )}
      >
        <AccordionTrigger className="group gap-2 text-left text-base font-semibold hover:no-underline sm:gap-3 sm:text-lg [&>svg]:hidden">
          <motion.span
            initial={{ opacity: 0.9 }}
            whileHover={{ opacity: 1, x: 3 }}
            transition={{ duration: 0.2 }}
            className="transition-all duration-300"
          >
            {question}
          </motion.span>
          <motion.div
            className="flex size-8 shrink-0 items-center justify-center rounded-[0.5rem] bg-[rgba(235,190,155,0.1)] sm:size-10"
            whileHover={{
              scale: 1.1,
              backgroundColor: "rgba(235,190,155,0.2)",
            }}
            transition={{ type: "spring", stiffness: 400, damping: 17 }}
          >
            <svg
              fill="none"
              stroke="#ebbe9b"
              viewBox="0 0 24 24"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
              className="size-4 sm:size-6"
            >
              <path
                d="M12 5v14"
                className="origin-center transition-transform duration-300 ease-out [&[data-state=open]]:rotate-90"
              />
              <path d="M5 12h14" />
            </svg>
          </motion.div>
        </AccordionTrigger>
        <AccordionContent className="whitespace-pre-line text-sm text-muted-foreground sm:text-base md:text-lg">
          <motion.div
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.3, delay: 0.1 }}
          >
            {answer}
          </motion.div>
        </AccordionContent>
      </AccordionItem>
    </motion.div>
  );
}
