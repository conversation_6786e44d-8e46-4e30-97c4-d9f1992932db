import Image from "next/image";
import { cn } from "@/lib/utils";

interface SocialProofLogo2Props {
  image: string;
  className?: string;
}

export function SocialProofLogo2({ image, className }: SocialProofLogo2Props) {
  return (
    <div
      className={cn(
        "relative h-10 w-full min-w-[100px] max-w-[150px] md:h-11 md:flex-1",
        className,
      )}
    >
      <Image alt="Company Logo" src={image} fill className="object-contain" />
    </div>
  );
}
