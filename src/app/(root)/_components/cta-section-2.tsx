import Link from "next/link";

import { Button } from "@/components/ui/button";

export function CtaSection2() {
  return (
    <section className="container px-6 py-6 sm:px-8 sm:py-10">
      <div className="relative flex flex-1 flex-col items-center gap-4 overflow-hidden rounded-bl-[1.5rem] rounded-br-[1.5rem] rounded-tl-[1.5rem] rounded-tr-[1.5rem] bg-[#012243] px-6 py-12 sm:gap-6 sm:rounded-bl-[2.5rem] sm:rounded-br-[2.5rem] sm:rounded-tl-[2.5rem] sm:rounded-tr-[2.5rem] sm:px-6 sm:pb-24 sm:pt-24">
        <h2 className="text-balance text-center font-heading text-2xl font-bold tracking-tight text-primary-foreground sm:text-3xl md:text-4xl lg:text-5xl">
          Get started today
        </h2>
        <p className="max-w-xl text-center text-sm text-primary-foreground/80 sm:text-base md:text-lg">
          Transform your workplace with our tech-driven accessibility assessment
          tool and expert training.
        </p>
        <Button
          size="lg"
          asChild
          variant="outline"
          className="hover:bg-bacground/90 mt-2 cursor-pointer border-border bg-background sm:mt-4"
        >
          <Link href="/sign-in">Get Started</Link>
        </Button>
      </div>
    </section>
  );
}
