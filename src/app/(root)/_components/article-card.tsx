import Link from "next/link";
import Image from "next/image";

import { Card, CardContent } from "@/components/ui/card";
import { cn } from "@/lib/utils";

interface ArticleCardProps {
  title: string;
  description: string;
  category: string;
  image: string;
  date: string;
  className?: string;
}

export function ArticleCard({
  title,
  description,
  category,
  image,
  date,
  className,
}: ArticleCardProps) {
  return (
    <Link href="#" className={className}>
      <Card className="m-0 h-full border-0">
        <CardContent className="flex h-full flex-col items-center gap-3 p-3 sm:items-start sm:gap-5 sm:p-5">
          <div className="relative h-40 w-full sm:h-52">
            <Image
              alt={title}
              src={image}
              fill
              className="rounded-xl object-cover"
            />
          </div>
          <div className="flex w-full flex-1 flex-col gap-2 sm:gap-4">
            <h4 className="text-center text-base font-semibold sm:text-left sm:text-lg">
              {title}
            </h4>
            <p className="mb-auto text-center text-sm text-muted-foreground sm:text-left sm:text-base">
              {description}
            </p>
          </div>
        </CardContent>
      </Card>
    </Link>
  );
}
