"use client";

import { useState } from "react";
import { PricingCard } from "./pricing-card";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";

// Pricing data for different business sizes
const pricingData = {
  small: {
    technology: 99,
    training: 492,
    consulting: 492,
  },
  medium: {
    technology: 133,
    training: 662,
    consulting: 662,
  },
  enterprise: {
    technology: 170,
    training: 849,
    consulting: 849,
  },
};

export function Pricing() {
  const [activeTab, setActiveTab] = useState("small");

  // Get current pricing based on active tab
  const currentPricing = pricingData[activeTab as keyof typeof pricingData];

  return (
    <section className="container flex flex-col items-center gap-4 px-4 pb-20 pt-12 sm:gap-6 sm:pb-40 sm:pt-24">
      <div className="flex flex-col items-center gap-2 sm:gap-3">
        <span className="text-center font-heading font-bold italic text-primary">
          Pricing
        </span>
        <h2 className="text-balance text-center font-heading text-2xl font-bold tracking-tight sm:text-3xl md:text-4xl">
          Our Services
        </h2>
      </div>
      <p className="max-w-lg text-balance text-center text-sm text-muted-foreground sm:text-base md:text-lg">
        We provide comprehensive accessibility solutions to help organizations
        create inclusive environments for all employees and customers.
      </p>

      <Tabs
        defaultValue="small"
        value={activeTab}
        onValueChange={setActiveTab}
        className="mt-4 w-full max-w-7xl"
      >
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="small">Small Business</TabsTrigger>
          <TabsTrigger value="medium">Medium Business</TabsTrigger>
          <TabsTrigger value="enterprise">Enterprise</TabsTrigger>
        </TabsList>

        <TabsContent value="small" className="mt-6">
          <div className="grid w-full grid-cols-1 gap-6 sm:grid-cols-3">
            <PricingCard
              name="Technology review"
              price={currentPricing.technology}
              features={[
                "Access to online self-service review tool",
                "Automated reports",
                "Recommendations and task lists",
                "Self-service dashboard",
              ]}
              description="Suitable for small businesses"
              isMostPopular={false}
              href="/sign-in"
              className="h-full border-2 border-primary"
              isMonthly={true}
            />
            <PricingCard
              name="Consultant review"
              price={currentPricing.consulting}
              features={[
                "Consultant to do review for client",
                "Automated reports",
                "Recommendations and task lists",
                "Self-service dashboard",
              ]}
              description="Suitable for small businesses"
              isMostPopular={false}
              href="/consulting"
              className="h-full border-2 border-primary"
              isDaily={true}
            />
            <PricingCard
              name="Training"
              price={currentPricing.training}
              features={[
                "Accessibility awareness training for employees, ERGs and leaders",
                "Accessibility toolkit",
              ]}
              description="For small businesses"
              isMostPopular={false}
              href="/training"
              className="h-full border-2 border-primary"
              isDaily={true}
            />
          </div>
        </TabsContent>

        <TabsContent value="medium" className="mt-6">
          <div className="grid w-full grid-cols-1 gap-6 sm:grid-cols-3">
            <PricingCard
              name="Technology review"
              price={currentPricing.technology}
              features={[
                "Access to online self-service review tool",
                "Automated reports",
                "Recommendations and task lists",
                "Self-service dashboard",
              ]}
              description="Suitable for medium-sized businesses"
              isMostPopular={false}
              href="/sign-in"
              className="h-full border-2 border-primary"
              isMonthly={true}
            />
            <PricingCard
              name="Consultant review"
              price={currentPricing.consulting}
              features={[
                "Consultant to do review for client",
                "Automated reports",
                "Recommendations and task lists",
                "Self-service dashboard",
              ]}
              description="Suitable for medium-sized businesses"
              isMostPopular={false}
              href="/consulting"
              className="h-full border-2 border-primary"
              isDaily={true}
            />
            <PricingCard
              name="Training"
              price={currentPricing.training}
              features={[
                "Accessibility awareness training for employees, ERGs and leaders",
                "Accessibility toolkit",
              ]}
              description="For medium-sized businesses"
              isMostPopular={false}
              href="/training"
              className="h-full border-2 border-primary"
              isDaily={true}
            />
          </div>
        </TabsContent>

        <TabsContent value="enterprise" className="mt-6">
          <div className="grid w-full grid-cols-1 gap-6 sm:grid-cols-3">
            <PricingCard
              name="Technology review"
              price={currentPricing.technology}
              features={[
                "Access to online self-service review tool",
                "Automated reports",
                "Recommendations and task lists",
                "Self-service dashboard",
              ]}
              description="Suitable for enterprise businesses"
              isMostPopular={false}
              href="/sign-in"
              className="h-full border-2 border-primary"
              isMonthly={true}
            />
            <PricingCard
              name="Consultant review"
              price={currentPricing.consulting}
              features={[
                "Consultant to do review for client",
                "Automated reports",
                "Recommendations and task lists",
                "Self-service dashboard",
              ]}
              description="Suitable for enterprise businesses"
              isMostPopular={false}
              href="/consulting"
              className="h-full border-2 border-primary"
              isDaily={true}
            />
            <PricingCard
              name="Training"
              price={currentPricing.training}
              features={[
                "Accessibility awareness training for employees, ERGs and leaders",
                "Accessibility toolkit",
              ]}
              description="For enterprise businesses"
              isMostPopular={false}
              href="/training"
              className="h-full border-2 border-primary"
              isDaily={true}
            />
          </div>
        </TabsContent>
      </Tabs>
    </section>
  );
}
