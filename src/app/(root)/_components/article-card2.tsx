import Link from "next/link";
import Image from "next/image";

import { Card, CardContent } from "@/components/ui/card";
import { cn } from "@/lib/utils";

interface ArticleCard2Props {
  title: string;
  description: string;
  category: string;
  image: string;
  date: string;
  className?: string;
}

export function ArticleCard2({
  title,
  description,
  category,
  image,
  date,
  className,
}: ArticleCard2Props) {
  return (
    <Link href="#" className={className}>
      <Card className="h-full border-0 shadow-none bg-transparent text-center">
        <CardContent className="flex h-full flex-col items-start gap-5 px-0">
          <div className="relative h-52 w-full">
            <Image alt={title} src={image} fill className="object-contain" />
          </div>
          <div className="flex flex-1 flex-col gap-4 text-center">
            <h4 className="text-lg font-semibold text-center">{title}</h4>
            <p className="mb-auto text-muted-foreground text-center">{description}</p>
          </div>
        </CardContent>
      </Card>
    </Link>
  );
}
