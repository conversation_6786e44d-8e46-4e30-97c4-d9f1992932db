"use client";

import { LucideProps } from "lucide-react";
import { motion } from "framer-motion";
import { cn } from "@/lib/utils";

interface FeatureHoverCardProps {
  title: string;
  description: string;
  icon: React.ForwardRefExoticComponent<
    Omit<LucideProps, "ref"> & React.RefAttributes<SVGSVGElement>
  >;
  className?: string;
}

export function FeatureHoverCard({
  title,
  description,
  icon: Icon,
  className,
}: FeatureHoverCardProps) {
  return (
    <motion.div
      className={cn(
        "group/feature relative flex flex-col items-center rounded-lg from-[rgba(235,190,155,0.2)] to-transparent px-2 py-6 hover:bg-gradient-to-t sm:items-start sm:rounded-none sm:px-0 sm:py-10 lg:border-b lg:border-r",
        className,
      )}
      initial={{ opacity: 0, y: 20 }}
      whileInView={{ opacity: 1, y: 0 }}
      viewport={{ once: true }}
      transition={{ duration: 0.5 }}
      whileHover={{
        scale: 1.02,
        transition: { duration: 0.2 },
      }}
    >
      <motion.div
        className="relative z-10 mb-3 flex justify-center px-6 sm:mb-4 sm:justify-start sm:px-10"
        whileHover={{ scale: 1.1, x: 5 }}
        transition={{ type: "spring", stiffness: 400, damping: 17 }}
      >
        <Icon size={24} className="text-primary" />
      </motion.div>
      <div className="relative z-10 mb-2 w-full px-6 text-center text-base font-bold sm:px-10 sm:text-left sm:text-lg">
        <motion.div
          className="absolute inset-y-0 left-0 hidden h-6 w-1 origin-center rounded-r-full bg-neutral-300 sm:block"
          animate={{
            height: "1.5rem",
            backgroundColor: "#ebbe9b20",
          }}
          whileHover={{
            height: "2rem",
            backgroundColor: "#ebbe9b",
            transition: { duration: 0.3 },
          }}
          transition={{ duration: 0.5 }}
        />
        <motion.span
          className="inline-block"
          whileHover={{ x: 3 }}
          transition={{ type: "spring", stiffness: 300, damping: 10 }}
        >
          {title}
        </motion.span>
      </div>
      <motion.p
        className="relative z-10 max-w-xs px-6 text-center text-xs text-muted-foreground sm:px-10 sm:text-left sm:text-sm"
        initial={{ opacity: 0.8 }}
        whileHover={{ opacity: 1 }}
        transition={{ duration: 0.3 }}
      >
        {description}
      </motion.p>
    </motion.div>
  );
}
