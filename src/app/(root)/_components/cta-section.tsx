"use client";

import { motion } from "framer-motion";

import { CtaButton } from "./cta-button";

export function CtaSection() {
  return (
    <section className="container flex flex-col items-center justify-evenly gap-4 overflow-hidden rounded-bl-[2.5rem] rounded-br-[2.5rem] rounded-tl-[1.5rem] rounded-tr-[1.5rem] bg-[#faefec] px-6 py-12 sm:gap-10 sm:rounded-bl-[5rem] sm:rounded-br-[5rem] sm:rounded-tl-[2.5rem] sm:rounded-tr-[2.5rem] sm:px-12 sm:py-24">
      <motion.div
        animate={{ y: 0, opacity: 1 }}
        initial={{ y: 5, opacity: 0 }}
        transition={{ delay: 0.8, duration: 0.4 }}
        className="flex cursor-pointer items-center gap-1 rounded-full bg-[hsl(var(--secondary-landing)/0.15)] px-4 py-1 font-medium text-[hsl(var(--primary-landing))] hover:bg-[hsl(var(--secondary-landing)/0.25)]"
      >
        <span className="text-[14px] font-bold">
          Unlock untapped talent pools through accessibility
        </span>
      </motion.div>
      <h2 className="max-w-xl text-balance text-center font-heading text-2xl font-bold tracking-tight sm:text-3xl sm:leading-tight md:text-4xl">
        Our story
      </h2>
      <p className="flex max-w-4xl gap-0 text-balance text-center text-sm text-muted-foreground sm:text-base md:text-lg">
        Transforming workplace accessibility through technology.
        <br />
        <br />
        Wakari is a technology-based Accessibility Maturity review tool for
        governments and businesses to review and measure their disability
        inclusion and workplace accessibility practices.
        <br />
        By leveraging our digital capabilities, organizations receive practical,
        actionable recommendations based on research and lived experiences of
        disabled people.
        <br />
        Our mission is to improve overall workplace wellbeing, supporting staff
        retention, productivity, and profitability, while enabling organisations
        to attract new and disabled talent that would otherwise not be attracted
        to them.
      </p>
      <motion.div
        animate={{ y: 0.4, opacity: 1 }}
        initial={{ y: 10, opacity: 0 }}
        transition={{ delay: 0.4, duration: 0.4 }}
        className="flex pt-2 sm:pt-3.5"
      ></motion.div>
    </section>
  );
}
