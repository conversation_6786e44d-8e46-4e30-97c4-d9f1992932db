"use client";

import { Users, Heart, TrendingUp } from "lucide-react";
import { motion } from "framer-motion";

interface FeatureCardProps {
  icon: React.ElementType;
  title: string;
  description: string;
}

function FeatureCard({ icon: Icon, title, description }: FeatureCardProps) {
  return (
    <div className="flex h-full flex-col gap-3 rounded-xl border border-border bg-background p-6 transition-all hover:border-primary/20 hover:shadow-md">
      <div className="flex h-12 w-12 items-center justify-center rounded-full bg-primary/10">
        <Icon className="h-6 w-6 text-primary" />
      </div>
      <h3 className="font-heading text-xl font-semibold">{title}</h3>
      <p className="flex-grow text-muted-foreground">{description}</p>
    </div>
  );
}

export function WhyUsSection() {
  return (
    <section
      id="our-story"
      className="container flex flex-col items-center gap-8 px-6 pb-16 pt-6 sm:gap-12 sm:pb-24 sm:pt-8"
    >
      <div className="flex flex-col gap-2 sm:gap-3">
        <motion.span
          animate={{ y: 0, opacity: 1 }}
          initial={{ y: 5, opacity: 0 }}
          transition={{ delay: 0.2, duration: 0.4 }}
          className="text-center font-bold uppercase text-primary"
        >
          Why Us
        </motion.span>
        <motion.h2
          animate={{ y: 0, opacity: 1 }}
          initial={{ y: 5, opacity: 0 }}
          transition={{ delay: 0.3, duration: 0.4 }}
          className="text-balance text-center font-heading text-2xl font-semibold tracking-tight sm:text-3xl md:text-4xl"
        >
          Transform your workplace
        </motion.h2>
      </div>

      <div className="grid w-full max-w-5xl grid-cols-1 gap-6 md:grid-cols-3">
        <motion.div
          animate={{ y: 0, opacity: 1 }}
          initial={{ y: 10, opacity: 0 }}
          transition={{ delay: 0.4, duration: 0.4 }}
        >
          <FeatureCard
            icon={Users}
            title="Unlock untapped talent pools"
            description="By building accessible workplaces and inclusive work culture, you open your business up to untapped disabled talent that would otherwise not consider your place for employment."
          />
        </motion.div>

        <motion.div
          animate={{ y: 0, opacity: 1 }}
          initial={{ y: 10, opacity: 0 }}
          transition={{ delay: 0.5, duration: 0.4 }}
        >
          <FeatureCard
            icon={Heart}
            title="Enhance employee wellbeing"
            description="88% of employees have invisible disabilities and these are undisclosed. By making accommodations at work, you enhance overall organisational wellbeing for disabled and non-disabled staff."
          />
        </motion.div>

        <motion.div
          animate={{ y: 0, opacity: 1 }}
          initial={{ y: 10, opacity: 0 }}
          transition={{ delay: 0.6, duration: 0.4 }}
        >
          <FeatureCard
            icon={TrendingUp}
            title="Boost retention and productivity"
            description="By improving workplace conditions, you enable the productivity of staff."
          />
        </motion.div>
      </div>
    </section>
  );
}
