"use client";

import Image from "next/image";
import { CreditCard } from "lucide-react";
import { motion } from "framer-motion";

import { FeatureItem } from "./feature-item";
import { CtaButton } from "./cta-button";

export function FeaturesSection2() {
  return (
    <section className="container flex max-w-6xl flex-col gap-8 py-12 md:flex-row md:items-center md:gap-20 md:py-24">
      <div className="relative order-1 flex-1 overflow-hidden rounded-br-[2.5rem] rounded-tl-[1.5rem] rounded-tr-[1.5rem] bg-gradient-to-br from-secondary to-primary pl-4 pr-0 pt-8 md:order-none md:rounded-br-[5rem] md:rounded-tl-[2.5rem] md:rounded-tr-[2.5rem] md:pl-10 md:pt-16">
        <Image
          alt="SaaS Dashboard"
          src="/images/a49c4727-9cb4-458d-9433-d20e11069b6a.jpeg"
          width={500}
          height={0}
          className="w-full max-w-full object-contain"
        />
      </div>
      <div className="flex flex-1 flex-col items-start gap-4 md:gap-7">
        <div className="flex flex-col gap-2 md:gap-3">
          <h2 className="text-balance text-left font-heading text-2xl font-bold tracking-tight text-primary sm:text-3xl md:text-4xl">
            Our&nbsp;Training
          </h2>
          <span className="text-left font-heading text-base font-bold italic text-[#002448] md:text-lg">
            &quot;80% of disabilities are invisible. So while your staff may not
            have disclosed theirs, they need to be accommodated for compliance,
            productivity and retention purposes.&quot;
          </span>
        </div>
        <p className="max-w-lg text-balance text-left text-lg text-muted-foreground" />
        <div className="flex grid w-full grid-cols-1 gap-6 md:gap-8">
          <FeatureItem
            icon={CreditCard}
            title="Accessibility Awareness training"
            description="Our inclusion specialists offer Accessibility Awareness training to ERG groups, staff and leadership teams to help you embed accessibility into your workplace and across the full employee lifecycle. "
            className="text-sm md:text-base"
          />
        </div>
        <motion.div
          animate={{ y: 0.4, opacity: 1 }}
          initial={{ y: 10, opacity: 0 }}
          transition={{ delay: 0.4, duration: 0.4 }}
          className="flex pt-2 sm:pt-3.5"
        >
          <CtaButton
            href="/training"
            text="Request training"
            className="my-0 ml-0 mr-3.5 flex justify-evenly p-0"
          />
        </motion.div>
      </div>
    </section>
  );
}
