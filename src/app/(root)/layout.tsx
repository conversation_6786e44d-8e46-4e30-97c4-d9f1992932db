import React from "react";
import { AltHeader } from "@/components/AltHeader";
import { LandingFooter } from "@/components/LandingFooter";

import type { Metadata } from "next";
import { Inter, Bricolage_Grotesque } from "next/font/google";
import { cn } from "@/lib/utils";
import AccessibilityWidget from "@/components/AccessibilityWidget";

const fontSans = Inter({
  variable: "--font-sans",
  subsets: ["latin"],
});

const fontHeading = Bricolage_Grotesque({
  variable: "--font-heading",
  subsets: ["latin"],
});

export const metadata: Metadata = {
  title: "Welcome to W<PERSON><PERSON>",
  description:
    "Technology to assess and improve accessibility maturity and disability inclusion in the workplace ",
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <div
      className={cn(
        "flex min-h-screen flex-col bg-white font-sans antialiased",
        fontSans.variable,
        fontHeading.variable,
      )}
    >
      <AltHeader />
      <main className="flex-1">{children}</main>
      <LandingFooter />
      <AccessibilityWidget />
    </div>
  );
}
