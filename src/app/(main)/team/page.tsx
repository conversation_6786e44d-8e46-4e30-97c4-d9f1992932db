import { currentUser } from "@clerk/nextjs/server";
import HeaderBox from "@/components/HeaderBox";
import { getUserCompany } from "@/server/actions/companies";
import TeamTable from "@/components/TeamTable";
import { getTeamMembers } from "@/server/actions/team";
import InviteTeamDialog from "@/components/InviteTeamDialog";
import { type TeamMember } from "@/server/actions/team";

export default async function TeamPage() {
  const user = await currentUser();
  if (!user) return null;

  const company = await getUserCompany(user.id);
  if (!company.success || !company.data) {
    return <div>No company found</div>;
  }

  const teamResult = await getTeamMembers(company.data.id);
  const team: TeamMember[] =
    teamResult.success && teamResult.data ? teamResult.data : [];

  // Find current user's role
  const currentUserTeamMember = team.find(
    (member) => member.clerkUserId === user.id,
  );

  // Log values to debug
  console.log("Current User ID:", user.id);
  console.log("Team Member Found:", currentUserTeamMember);
  console.log("Team Member Role:", currentUserTeamMember?.role);

  // Explicitly check for "admin" role
  const isAdmin = currentUserTeamMember?.role === "admin";
  console.log("Is Admin:", isAdmin);

  return (
    <div className="w-full space-y-6 sm:space-y-8">
      <div className="mb-4 flex w-full items-start justify-between sm:mb-8">
        <HeaderBox
          type="title"
          title="Team"
          subtext="Manage your team members and their roles"
        />
        {/* {isAdmin && <InviteTeamDialog />} */}
      </div>

      <div className="overflow-hidden rounded-lg border bg-card">
        <div className="p-3 sm:p-6">
          <TeamTable members={team} currentUserId={user.id} isAdmin={isAdmin} />
        </div>
      </div>
    </div>
  );
}
