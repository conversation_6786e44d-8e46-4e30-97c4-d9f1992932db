"use server";

import { db } from "@/server/db";
import { TaskTable, users, companies } from "@/server/drizzle/schema";
import { eq, sql } from "drizzle-orm";
import { withAdmin } from "@/app/(auth)/auth";

export const getTaskById = withAdmin(async (taskId: string) => {
  try {
    const task = await db
      .select({
        id: TaskTable.id,
        title: TaskTable.title,
        description: TaskTable.description,
        status: TaskTable.status,
        priority: TaskTable.priority,
        assignedToName: sql<string>`concat(${users.firstName}, ' ', ${users.lastName})`,
        companyName: companies.companyName,
        startDate: TaskTable.startDate,
        dueDate: TaskTable.dueDate,
        createdAt: TaskTable.createdAt,
      })
      .from(TaskTable)
      .leftJoin(users, eq(TaskTable.assignedToUserId, users.clerkUserId))
      .leftJoin(companies, eq(TaskTable.companyId, companies.id))
      .where(eq(TaskTable.id, taskId))
      .then((res) => res[0]);

    return { success: true, data: task };
  } catch (error) {
    console.error("Error fetching task:", error);
    return { success: false, error: "Failed to fetch task" };
  }
});
