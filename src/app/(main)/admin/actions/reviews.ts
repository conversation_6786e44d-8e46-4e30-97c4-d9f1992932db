"use server";

import { db } from "@/server/db/index";
import {
  ReviewTable,
  users,
  companies,
  ReviewCommentTable,
  TaskTable,
} from "@/server/drizzle/schema";
import { eq, sql, desc, and, not } from "drizzle-orm";
import { withAdmin } from "@/app/(auth)/auth";

export const getReviewById = withAdmin(async (reviewId: string) => {
  return db
    .select({
      id: ReviewTable.id,
      title: ReviewTable.title,
      type: ReviewTable.type,
      status: ReviewTable.status,
      createdByName: sql<string>`concat(${users.firstName}, ' ', ${users.lastName})`,
      companyName: companies.companyName,
      createdAt: ReviewTable.createdAt,
    })
    .from(ReviewTable)
    .leftJoin(users, eq(ReviewTable.createdByUserId, users.clerkUserId))
    .leftJoin(companies, eq(ReviewTable.companyId, companies.id))
    .where(eq(ReviewTable.id, reviewId))
    .then((res) => res[0]);
});

export const getReviewComments = withAdmin(async (reviewId: string) => {
  return db
    .select({
      id: ReviewCommentTable.id,
      text: ReviewCommentTable.text,
      createdAt: ReviewCommentTable.createdAt,
      firstName: users.firstName,
      lastName: users.lastName,
    })
    .from(ReviewCommentTable)
    .leftJoin(users, eq(ReviewCommentTable.userId, users.clerkUserId))
    .where(eq(ReviewCommentTable.reviewId, reviewId))
    .orderBy(desc(ReviewCommentTable.createdAt));
});

export const getReviewTasks = withAdmin(async (reviewId: string) => {
  type Task = {
    id: string;
    title: string;
    status: "To Do" | "In Progress" | "Completed" | "Archived";
    priority: "high" | "medium" | "low";
  };

  return db
    .select({
      id: TaskTable.id,
      title: TaskTable.title,
      status: TaskTable.status,
      priority: TaskTable.priority,
    })
    .from(TaskTable)
    .where(
      and(eq(TaskTable.reviewId, reviewId), not(eq(TaskTable.status, "Draft"))),
    )
    .orderBy(desc(TaskTable.createdAt)) as Promise<Task[]>;
});
