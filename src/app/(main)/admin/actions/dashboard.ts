"use server";

import { db } from "@/server/db/index";
import {
  companies,
  users,
  ReviewTable,
  TaskTable,
  ReportTable,
  userCompanies,
  QuestionTable,
  CategoryTable,
  RecommendationTable,
  feedbacks,
} from "@/server/drizzle/schema";
import { count, eq, sql, inArray, not, and } from "drizzle-orm";
import { withAdmin } from "@/app/(auth)/auth";
import { clerkClient } from "@clerk/nextjs/server";

// Wrap the existing function with withAdmin
export const getGlobalStats = withAdmin(async () => {
  // First get all tasks to debug
  const allTasks = await db
    .select({
      id: TaskTable.id,
      status: TaskTable.status,
      reviewId: TaskTable.reviewId,
      isArchived: ReviewTable.isArchived,
    })
    .from(TaskTable)
    .leftJoin(ReviewTable, eq(TaskTable.reviewId, ReviewTable.id))
    .where(eq(ReviewTable.isArchived, false));

  console.log("Total tasks:", allTasks.length);
  console.log(
    "Tasks by status:",
    allTasks.reduce(
      (acc, t) => {
        acc[t.status] = (acc[t.status] || 0) + 1;
        return acc;
      },
      {} as Record<string, number>,
    ),
  );

  const [totalCompanies, totalUsers, totalReviews, totalTasks, totalReports] =
    await Promise.all([
      db.select({ count: count() }).from(companies),
      db.select({ count: count() }).from(users),
      db.select({ count: count() }).from(ReviewTable),
      db
        .select({ count: count() })
        .from(TaskTable)
        .leftJoin(ReviewTable, eq(TaskTable.reviewId, ReviewTable.id))
        .where(eq(ReviewTable.isArchived, false)),
      db.select({ count: count() }).from(ReportTable),
    ]);

  return {
    companies: totalCompanies[0].count,
    users: totalUsers[0].count,
    reviews: totalReviews[0].count,
    tasks: totalTasks[0].count,
    reports: totalReports[0].count,
  };
});

export const getCompaniesWithCounts = withAdmin(async () => {
  return db
    .select({
      id: companies.id,
      companyName: companies.companyName,
      companyDomain: companies.companyDomain,
      createdAt: companies.createdAt,
      userCount: sql<number>`count(distinct ${userCompanies.userId})`,
      reviewCount: sql<number>`count(distinct ${ReviewTable.id})`,
    })
    .from(companies)
    .leftJoin(userCompanies, eq(companies.id, userCompanies.companyId))
    .leftJoin(ReviewTable, eq(companies.id, ReviewTable.companyId))
    .groupBy(companies.id);
});

export const getUsersWithCompanies = withAdmin(async () => {
  return db
    .select({
      id: users.id,
      firstName: users.firstName,
      lastName: users.lastName,
      email: users.email,
      companyName: companies.companyName,
      role: userCompanies.role,
      createdAt: users.createdAt,
    })
    .from(users)
    .leftJoin(userCompanies, eq(users.clerkUserId, userCompanies.clerkUserId))
    .leftJoin(companies, eq(userCompanies.companyId, companies.id));
});

export const getReviewsWithDetails = withAdmin(async () => {
  return db
    .select({
      id: ReviewTable.id,
      title: ReviewTable.title,
      companyName: companies.companyName,
      type: ReviewTable.type,
      status: ReviewTable.status,
      createdByName: sql<string>`concat(${users.firstName}, ' ', ${users.lastName})`,
      createdAt: ReviewTable.createdAt,
    })
    .from(ReviewTable)
    .leftJoin(companies, eq(ReviewTable.companyId, companies.id))
    .leftJoin(users, eq(ReviewTable.createdByUserId, users.clerkUserId))
    .orderBy(ReviewTable.createdAt);
});

export const getTasksWithDetails = withAdmin(async () => {
  type Task = {
    id: string;
    title: string;
    companyName: string | null;
    status: "Draft" | "To Do" | "In Progress" | "Completed" | "Archived";
    priority: "high" | "medium" | "low";
    assignedToName: string;
    dueDate: Date | null;
    createdAt: Date;
  };

  return db
    .select({
      id: TaskTable.id,
      title: TaskTable.title,
      companyName: companies.companyName,
      status: TaskTable.status,
      priority: TaskTable.priority,
      assignedToName: sql<string>`concat(${users.firstName}, ' ', ${users.lastName})`,
      dueDate: TaskTable.dueDate,
      createdAt: TaskTable.createdAt,
    })
    .from(TaskTable)
    .leftJoin(companies, eq(TaskTable.companyId, companies.id))
    .leftJoin(users, eq(TaskTable.assignedToUserId, users.clerkUserId))
    .leftJoin(ReviewTable, eq(TaskTable.reviewId, ReviewTable.id))
    .where(eq(ReviewTable.isArchived, false))
    .orderBy(TaskTable.createdAt) as Promise<Task[]>;
});

export const getQuestionsWithDetails = withAdmin(async () => {
  try {
    const questions = await db
      .select({
        id: QuestionTable.id,
        text: QuestionTable.text,
        description: QuestionTable.description,
        guideImage: QuestionTable.guideImage,
        categoryId: QuestionTable.categoryId,
        category: CategoryTable.name ?? "Uncategorized",
        isArchived: QuestionTable.isArchived,
        createdAt: QuestionTable.createdAt,
        updatedAt: QuestionTable.updatedAt,
      })
      .from(QuestionTable)
      .leftJoin(CategoryTable, eq(QuestionTable.categoryId, CategoryTable.id))
      .orderBy(CategoryTable.name, QuestionTable.text);

    // Get recommendations separately
    const recommendations = await db
      .select()
      .from(RecommendationTable)
      .where(
        // Only get recommendations for the questions we fetched
        inArray(
          RecommendationTable.questionId,
          questions.map((q) => q.id),
        ),
      );

    // Combine questions with their recommendations
    const questionsWithRecommendations = questions.map((question) => ({
      ...question,
      recommendations: recommendations.filter(
        (r) => r.questionId === question.id,
      ),
    }));

    return questionsWithRecommendations;
  } catch (error) {
    console.error("Error fetching questions:", error);
    return [];
  }
});

export const getFeedbackWithUsers = withAdmin(async () => {
  try {
    const feedbackResults = await db.select().from(feedbacks);
    const userIds = Array.from(new Set(feedbackResults.map((f) => f.userId)));

    const usersResults = await db
      .select({
        id: users.clerkUserId,
        firstName: users.firstName,
        lastName: users.lastName,
        email: users.email,
      })
      .from(users)
      .where(inArray(users.clerkUserId, userIds));

    return feedbackResults.map((feedback) => {
      const user = usersResults.find((u) => u.id === feedback.userId);
      return {
        ...feedback,
        user: {
          firstName: user?.firstName ?? "Unknown",
          lastName: user?.lastName ?? "User",
          email: user?.email ?? "",
        },
      };
    });
  } catch (error) {
    console.error("Error fetching feedback:", error);
    return [];
  }
});
