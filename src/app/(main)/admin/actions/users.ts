"use server";

import { db } from "@/server/db";
import { users } from "@/server/drizzle/schema";
import { eq } from "drizzle-orm";
import { withAdmin } from "@/app/(auth)/auth";

export const setUserAsAdmin = withAdmin(
  async (clerkUserId: string, isAdmin: boolean) => {
    await db
      .update(users)
      .set({ isSuperAdmin: isAdmin })
      .where(eq(users.clerkUserId, clerkUserId));
  },
);

export async function checkUserIsAdmin(userId: string): Promise<boolean> {
  const dbUser = await db.query.users.findFirst({
    where: eq(users.clerkUserId, userId),
  });
  return dbUser?.isSuperAdmin ?? false;
}
