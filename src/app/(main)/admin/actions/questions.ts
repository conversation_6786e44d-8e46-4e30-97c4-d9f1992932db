"use server";

import { db } from "@/server/db";
import { QuestionTable, RecommendationTable } from "@/server/drizzle/schema";
import { eq } from "drizzle-orm";
import { revalidatePath } from "next/cache";

export async function updateQuestion(questionId: string, data: any) {
  try {
    console.log("Updating question with data:", {
      questionId,
      data,
      guideImage: data.guideImage,
    });

    const updateData = {
      ...(data.text && { text: data.text }),
      ...(data.description && { description: data.description }),
      ...(data.categoryId && { categoryId: data.categoryId }),
      ...(data.guideImage && { guideImage: data.guideImage }),
      updatedAt: new Date(),
    };

    console.log("Final update data:", updateData);

    const result = await db
      .update(QuestionTable)
      .set(updateData)
      .where(eq(QuestionTable.id, questionId))
      .returning({
        id: QuestionTable.id,
        guideImage: QuestionTable.guideImage,
      });

    console.log("Updated question result:", result);

    revalidatePath("/admin");
    return { success: true, data: result };
  } catch (error) {
    console.error("Error updating question:", error);
    return { success: false, error };
  }
}

export async function updateRecommendation(
  recommendationId: string,
  data: any,
) {
  try {
    console.log("Updating recommendation:", { recommendationId, data });

    const updateData = {
      ...(data.text && { text: data.text }),
      ...(data.description && { description: data.description }),
      ...(data.priority && { priority: data.priority }),
      ...(data.estimatedEffort && { estimatedEffort: data.estimatedEffort }),
      updatedAt: new Date(),
    };

    console.log("Recommendation update data:", updateData);

    const result = await db
      .update(RecommendationTable)
      .set(updateData)
      .where(eq(RecommendationTable.id, recommendationId))
      .returning({
        id: RecommendationTable.id,
        text: RecommendationTable.text,
        description: RecommendationTable.description,
      });

    console.log("Updated recommendation result:", result);

    revalidatePath("/admin");
    return { success: true, data: result };
  } catch (error) {
    console.error("Error updating recommendation:", error);
    return { success: false, error };
  }
}

export async function deleteQuestion(questionId: string) {
  try {
    // Delete recommendations first
    await db
      .delete(RecommendationTable)
      .where(eq(RecommendationTable.questionId, questionId));

    // Then delete the question
    await db.delete(QuestionTable).where(eq(QuestionTable.id, questionId));

    revalidatePath("/admin");
    return { success: true };
  } catch (error) {
    console.error("Error deleting question:", error);
    return { success: false, error };
  }
}

interface CreateQuestionData {
  text: string;
  description: string;
  categoryId: string;
  guideImage?: string;
  recommendations: {
    text: string;
    priority: string;
    estimatedEffort: string;
  }[];
}

export async function createQuestion(data: CreateQuestionData) {
  try {
    // Create the question first
    const [question] = await db
      .insert(QuestionTable)
      .values({
        text: data.text,
        description: data.description,
        categoryId: data.categoryId,
        guideImage: data.guideImage || null,
        createdAt: new Date(),
        updatedAt: new Date(),
      })
      .returning();

    // Create recommendations if any
    if (data.recommendations.length > 0) {
      await db.insert(RecommendationTable).values(
        data.recommendations.map((rec) => ({
          text: rec.text,
          priority: rec.priority,
          estimatedEffort: rec.estimatedEffort || null,
          questionId: question.id,
          createdAt: new Date(),
          updatedAt: new Date(),
        })),
      );
    }

    revalidatePath("/admin");
    return { success: true };
  } catch (error) {
    console.error("Error creating question:", error);
    return { success: false, error };
  }
}

export async function toggleQuestionArchive(
  questionId: string,
  isArchived: boolean,
) {
  try {
    const result = await db
      .update(QuestionTable)
      .set({
        isArchived,
        updatedAt: new Date(),
      })
      .where(eq(QuestionTable.id, questionId))
      .returning({
        id: QuestionTable.id,
        isArchived: QuestionTable.isArchived,
      });

    revalidatePath("/admin");
    return { success: true, data: result };
  } catch (error) {
    console.error("Error toggling question archive status:", error);
    return { success: false, error };
  }
}
