"use server";

import { db } from "@/server/db/index";
import {
  companies,
  userCompanies,
  ReviewTable,
  TaskTable,
  users,
} from "@/server/drizzle/schema";
import { count, eq, sql, and, not } from "drizzle-orm";
import { withAdmin } from "@/app/(auth)/auth";

export const getCompanyById = withAdmin(async (companyId: string) => {
  return db
    .select()
    .from(companies)
    .where(eq(companies.id, companyId))
    .then((res) => res[0]);
});

export const getCompanyStats = withAdmin(async (companyId: string) => {
  return Promise.all([
    db
      .select({ count: count() })
      .from(userCompanies)
      .where(eq(userCompanies.companyId, companyId)),
    db
      .select({ count: count() })
      .from(ReviewTable)
      .where(eq(ReviewTable.companyId, companyId)),
    db
      .select({ count: count() })
      .from(TaskTable)
      .where(eq(TaskTable.companyId, companyId)),
  ]);
});

export const getCompanyUsers = withAdmin(async (companyId: string) => {
  return db
    .select({
      id: users.id,
      firstName: users.firstName,
      lastName: users.lastName,
      email: users.email,
      role: userCompanies.role,
      createdAt: users.createdAt,
    })
    .from(userCompanies)
    .leftJoin(users, eq(users.clerkUserId, userCompanies.clerkUserId))
    .where(eq(userCompanies.companyId, companyId));
});

export const getCompanyReviews = withAdmin(async (companyId: string) => {
  return db
    .select({
      id: ReviewTable.id,
      title: ReviewTable.title,
      type: ReviewTable.type,
      status: ReviewTable.status,
      createdByName: sql<string>`concat(${users.firstName}, ' ', ${users.lastName})`,
      createdAt: ReviewTable.createdAt,
    })
    .from(ReviewTable)
    .leftJoin(users, eq(ReviewTable.createdByUserId, users.clerkUserId))
    .where(eq(ReviewTable.companyId, companyId))
    .orderBy(ReviewTable.createdAt);
});

export const getCompanyTasks = withAdmin(async (companyId: string) => {
  type CompanyTask = {
    id: string;
    title: string;
    status: "To Do" | "In Progress" | "Completed" | "Archived";
    priority: "high" | "medium" | "low";
    assignedToName: string;
    dueDate: Date | null;
    createdAt: Date;
  };

  return db
    .select({
      id: TaskTable.id,
      title: TaskTable.title,
      status: TaskTable.status,
      priority: TaskTable.priority,
      assignedToName: sql<string>`concat(${users.firstName}, ' ', ${users.lastName})`,
      dueDate: TaskTable.dueDate,
      createdAt: TaskTable.createdAt,
    })
    .from(TaskTable)
    .leftJoin(users, eq(TaskTable.assignedToUserId, users.clerkUserId))
    .where(
      and(
        eq(TaskTable.companyId, companyId),
        not(eq(TaskTable.status, "Draft")),
      ),
    )
    .orderBy(TaskTable.createdAt) as Promise<CompanyTask[]>;
});
