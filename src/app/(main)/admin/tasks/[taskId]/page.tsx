import { notFound } from "next/navigation";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>ontent } from "@/components/ui/card";
import { formatDate } from "@/lib/utils";
import { Badge } from "@/components/ui/badge";
import { BackButton } from "@/app/(main)/admin/components/BackButton";
import { getTaskById } from "../../actions/tasks";
export default async function TaskDetailPage({
  params,
}: {
  params: { taskId: string };
}) {
  const taskResponse = await getTaskById(params.taskId);

  if (!taskResponse.success || !taskResponse.data) {
    notFound();
  }

  const task = taskResponse.data;

  const getPriorityColor = (priority: typeof task.priority) => {
    switch (priority) {
      case "high":
        return "bg-red-500";
      case "medium":
        return "bg-yellow-500";
      case "low":
        return "bg-green-500";
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <BackButton />
          <div>
            <h1 className="text-3xl font-bold">{task.title}</h1>
            <p className="text-sm text-muted-foreground">
              for {task.companyName}
            </p>
          </div>
        </div>
        <div className="flex items-center gap-2">
          <Badge>{task.status}</Badge>
          <Badge variant="outline" className={getPriorityColor(task.priority)}>
            {task.priority}
          </Badge>
        </div>
      </div>

      <div className="grid gap-4 md:grid-cols-2">
        <Card>
          <CardHeader>
            <CardTitle className="text-sm font-medium">Details</CardTitle>
          </CardHeader>
          <CardContent className="space-y-2">
            <div>
              <p className="text-sm font-medium">Description</p>
              <p>{task.description || "No description"}</p>
            </div>
            <div>
              <p className="text-sm font-medium">Assigned To</p>
              <p>{task.assignedToName ?? "Unassigned"}</p>
            </div>
            <div>
              <p className="text-sm font-medium">Due Date</p>
              <p>{task.dueDate ? formatDate(task.dueDate) : "No due date"}</p>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="text-sm font-medium">Timeline</CardTitle>
          </CardHeader>
          <CardContent className="space-y-2">
            <div>
              <p className="text-sm font-medium">Start Date</p>
              <p>{formatDate(task.startDate)}</p>
            </div>
            <div>
              <p className="text-sm font-medium">Created At</p>
              <p>{formatDate(task.createdAt)}</p>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Add sections for task updates, comments, etc. */}
    </div>
  );
}
