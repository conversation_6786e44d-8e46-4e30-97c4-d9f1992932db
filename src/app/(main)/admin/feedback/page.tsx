import { Card } from "@/components/ui/card";
import { getFeedbackWithUsers } from "../actions/dashboard";
import { FeedbackList } from "../components/FeedbackList";

interface Feedback {
  usabilityRating: number;
  easeOfUseRating: number;
  helpfulnessRating: number;
  willingToPay: boolean;
  willingToReview: boolean;
}

export default async function AdminFeedbackPage() {
  const feedbacks = await getFeedbackWithUsers();

  return (
    <div className="w-full space-y-8">
      <div className="mb-8 flex w-full items-start justify-between">
        <div>
          <h2 className="text-2xl font-bold tracking-tight">User Feedback</h2>
          <p className="text-muted-foreground">
            View and analyze user feedback and suggestions
          </p>
        </div>
      </div>

      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card className="p-4">
          <h3 className="text-sm font-medium text-muted-foreground">
            Total Feedback
          </h3>
          <p className="mt-2 text-2xl font-bold">{feedbacks.length}</p>
        </Card>
        <Card className="p-4">
          <h3 className="text-sm font-medium text-muted-foreground">
            Average Rating
          </h3>
          <p className="mt-2 text-2xl font-bold">
            {(
              feedbacks.reduce(
                (acc: number, f: Feedback) =>
                  acc +
                  (f.usabilityRating +
                    f.easeOfUseRating +
                    f.helpfulnessRating) /
                    3,
                0,
              ) / feedbacks.length || 0
            ).toFixed(1)}
          </p>
        </Card>
        <Card className="p-4">
          <h3 className="text-sm font-medium text-muted-foreground">
            Willing to Pay
          </h3>
          <p className="mt-2 text-2xl font-bold">
            {feedbacks.filter((f) => f.willingToPay).length}
          </p>
        </Card>
        <Card className="p-4">
          <h3 className="text-sm font-medium text-muted-foreground">
            Interested in Review
          </h3>
          <p className="mt-2 text-2xl font-bold">
            {feedbacks.filter((f) => f.willingToReview).length}
          </p>
        </Card>
      </div>

      <FeedbackList feedbacks={feedbacks} />
    </div>
  );
}
