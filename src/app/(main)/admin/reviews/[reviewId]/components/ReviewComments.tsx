"use client";

import { formatDate } from "@/lib/utils";

interface Comment {
  id: string;
  text: string;
  createdAt: Date;
  firstName: string | null;
  lastName: string | null;
}

interface ReviewCommentsProps {
  initialComments: Comment[];
}

export default function ReviewComments({
  initialComments,
}: ReviewCommentsProps) {
  return (
    <div className="space-y-4">
      {initialComments.map((comment) => (
        <div key={comment.id} className="rounded-lg border p-4">
          <div className="flex items-center justify-between">
            <p className="font-medium">
              {comment.firstName} {comment.lastName}
            </p>
            <p className="text-sm text-muted-foreground">
              {formatDate(comment.createdAt)}
            </p>
          </div>
          <p className="mt-2">{comment.text}</p>
        </div>
      ))}
      {initialComments.length === 0 && (
        <p className="text-center text-muted-foreground">No comments found</p>
      )}
    </div>
  );
}
