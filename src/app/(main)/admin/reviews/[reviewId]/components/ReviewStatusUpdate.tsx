"use client";

import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

interface ReviewStatusUpdateProps {
  reviewId: string;
  currentStatus: "draft" | "in_progress" | "completed";
}

export default function ReviewStatusUpdate({
  reviewId,
  currentStatus,
}: ReviewStatusUpdateProps) {
  const handleStatusChange = async (newStatus: string) => {
    // TODO: Add API endpoint to handle status update
    // await fetch(`/api/reviews/${reviewId}/status`, {
    //   method: "PATCH",
    //   body: JSON.stringify({ status: newStatus }),
    // });
  };

  return (
    <Select value={currentStatus} onValueChange={handleStatusChange}>
      <SelectTrigger className="w-[180px]">
        <SelectValue placeholder="Update status" />
      </SelectTrigger>
      <SelectContent>
        <SelectItem value="draft">Draft</SelectItem>
        <SelectItem value="in_progress">In Progress</SelectItem>
        <SelectItem value="completed">Completed</SelectItem>
      </SelectContent>
    </Select>
  );
}
