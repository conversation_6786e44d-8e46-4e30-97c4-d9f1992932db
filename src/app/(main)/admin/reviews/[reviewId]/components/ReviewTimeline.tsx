"use client";

import { formatDate } from "@/lib/utils";
import { Card } from "@/components/ui/card";

interface TimelineEvent {
  id: string;
  type: "status_change" | "comment" | "task_created" | "task_completed";
  description: string;
  timestamp: Date;
  user: string;
}

interface ReviewTimelineProps {
  reviewId: string;
}

export default function ReviewTimeline({ reviewId }: ReviewTimelineProps) {
  // TODO: Fetch timeline events from API
  const events: TimelineEvent[] = [
    // Placeholder data
    {
      id: "1",
      type: "status_change",
      description: "Review status changed from Draft to In Progress",
      timestamp: new Date(),
      user: "John <PERSON>",
    },
  ];

  const getEventIcon = (type: TimelineEvent["type"]) => {
    switch (type) {
      case "status_change":
        return "��";
      case "comment":
        return "💬";
      case "task_created":
        return "➕";
      case "task_completed":
        return "✅";
    }
  };

  return (
    <div className="space-y-4">
      {events.map((event) => (
        <Card key={event.id} className="p-4">
          <div className="flex items-start gap-3">
            <div className="text-xl">{getEventIcon(event.type)}</div>
            <div className="flex-1">
              <p className="font-medium">{event.description}</p>
              <div className="mt-1 flex items-center gap-2 text-sm text-muted-foreground">
                <span>{event.user}</span>
                <span>•</span>
                <span>{formatDate(event.timestamp)}</span>
              </div>
            </div>
          </div>
        </Card>
      ))}
    </div>
  );
}
