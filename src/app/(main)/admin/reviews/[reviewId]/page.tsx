import { notFound } from "next/navigation";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardContent } from "@/components/ui/card";
import { formatDate } from "@/lib/utils";
import { Badge } from "@/components/ui/badge";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import ReviewComments from "./components/ReviewComments";
import ReviewTimeline from "./components/ReviewTimeline";
import RelatedTasks from "./components/RelatedTasks";
import { ReportPageContent } from "@/components/ReportPageContent";
import { generateReport } from "@/server/actions/reports";
import { BackButton } from "../../components/BackButton";
import {
  getReviewById,
  getReviewComments,
  getReviewTasks,
} from "../../actions/reviews";

export default async function ReviewDetailPage({
  params,
}: {
  params: { reviewId: string };
}) {
  const review = await getReviewById(params.reviewId);

  if (!review) {
    notFound();
  }

  // Fetch comments
  const comments = await getReviewComments(params.reviewId);

  // Fetch related tasks
  const relatedTasks = await getReviewTasks(params.reviewId);
  // Generate or get existing report
  const reportResult = await generateReport(params.reviewId);

  const getStatusColor = (status: typeof review.status) => {
    switch (status) {
      case "completed":
        return "bg-green-500";
      case "in_progress":
        return "bg-blue-500";
      default:
        return "bg-gray-500";
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <BackButton />
          <div>
            <h1 className="text-3xl font-bold">{review.title}</h1>
            <p className="text-sm text-muted-foreground">
              for {review.companyName}
            </p>
          </div>
        </div>
        <Badge variant="secondary" className={getStatusColor(review.status)}>
          {review.status.replace("_", " ")}
        </Badge>
      </div>

      <div className="grid gap-4 md:grid-cols-2">
        <Card>
          <CardHeader>
            <CardTitle className="text-sm font-medium">Details</CardTitle>
          </CardHeader>
          <CardContent className="space-y-2">
            <div>
              <p className="text-sm font-medium">Type</p>
              <p className="capitalize">{review.type}</p>
            </div>
            <div>
              <p className="text-sm font-medium">Created By</p>
              <p>{review.createdByName}</p>
            </div>
            <div>
              <p className="text-sm font-medium">Created At</p>
              <p>{formatDate(review.createdAt)}</p>
            </div>
          </CardContent>
        </Card>

        {/* Add more cards for review-specific data */}
      </div>

      <Tabs defaultValue="comments" className="space-y-4">
        <TabsList>
          <TabsTrigger value="comments">Comments</TabsTrigger>
          <TabsTrigger value="timeline">Timeline</TabsTrigger>
          <TabsTrigger value="tasks">Related Tasks</TabsTrigger>
          <TabsTrigger value="report">Report</TabsTrigger>
        </TabsList>

        <TabsContent value="comments">
          <ReviewComments initialComments={comments} />
        </TabsContent>

        <TabsContent value="timeline">
          <ReviewTimeline reviewId={review.id} />
        </TabsContent>

        <TabsContent value="tasks">
          <RelatedTasks reviewId={review.id} tasks={relatedTasks} />
        </TabsContent>

        <TabsContent value="report">
          {reportResult.success && reportResult.data ? (
            <ReportPageContent
              isAdmin={true}
              reviewId={params.reviewId}
              ragScores={reportResult.data.ragScores}
              categoryScores={reportResult.data.categoryScores}
              recommendations={reportResult.data.recommendations}
            />
          ) : (
            <div className="text-center text-muted-foreground">
              No report available
            </div>
          )}
        </TabsContent>
      </Tabs>
    </div>
  );
}
