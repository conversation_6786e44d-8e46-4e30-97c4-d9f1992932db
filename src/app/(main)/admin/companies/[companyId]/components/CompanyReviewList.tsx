"use client";

import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { formatDate } from "@/lib/utils";
import { Badge } from "@/components/ui/badge";
import { useRouter } from "next/navigation";

interface CompanyReview {
  id: string;
  title: string;
  type: "basic" | "comprehensive";
  status: "draft" | "in_progress" | "completed";
  createdByName: string | null;
  createdAt: Date;
}

interface CompanyReviewListProps {
  reviews: CompanyReview[];
}

export default function CompanyReviewList({ reviews }: CompanyReviewListProps) {
  const router = useRouter();

  const getStatusColor = (status: CompanyReview["status"]) => {
    switch (status) {
      case "completed":
        return "bg-green-500";
      case "in_progress":
        return "bg-blue-500";
      default:
        return "bg-gray-500";
    }
  };

  return (
    <div className="rounded-md border">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>Title</TableHead>
            <TableHead>Type</TableHead>
            <TableHead>Status</TableHead>
            <TableHead>Created By</TableHead>
            <TableHead>Created</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {reviews.map((review) => (
            <TableRow
              key={review.id}
              className="cursor-pointer hover:bg-muted/50"
              onClick={() => router.push(`/admin/reviews/${review.id}`)}
            >
              <TableCell className="font-medium">{review.title}</TableCell>
              <TableCell className="capitalize">{review.type}</TableCell>
              <TableCell>
                <Badge
                  variant="secondary"
                  className={getStatusColor(review.status)}
                >
                  {review.status.replace("_", " ")}
                </Badge>
              </TableCell>
              <TableCell>{review.createdByName}</TableCell>
              <TableCell>{formatDate(review.createdAt)}</TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
  );
}
