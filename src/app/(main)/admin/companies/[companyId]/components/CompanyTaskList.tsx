"use client";

import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { formatDate } from "@/lib/utils";
import { Badge } from "@/components/ui/badge";
import { useRouter } from "next/navigation";

interface CompanyTask {
  id: string;
  title: string;
  status: "To Do" | "In Progress" | "Completed" | "Archived";
  priority: "high" | "medium" | "low";
  assignedToName: string | null;
  dueDate: Date | null;
  createdAt: Date;
}

interface CompanyTaskListProps {
  tasks: CompanyTask[];
}

export default function CompanyTaskList({ tasks }: CompanyTaskListProps) {
  const router = useRouter();
  const getPriorityColor = (priority: CompanyTask["priority"]) => {
    switch (priority) {
      case "high":
        return "bg-red-500";
      case "medium":
        return "bg-yellow-500";
      case "low":
        return "bg-green-500";
    }
  };

  return (
    <div className="rounded-md border">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>Title</TableHead>
            <TableHead>Status</TableHead>
            <TableHead>Priority</TableHead>
            <TableHead>Assigned To</TableHead>
            <TableHead>Due Date</TableHead>
            <TableHead>Created</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {tasks.map((task) => (
            <TableRow
              key={task.id}
              className="cursor-pointer hover:bg-muted/50"
              onClick={() => router.push(`/admin/tasks/${task.id}`)}
            >
              <TableCell className="font-medium">{task.title}</TableCell>
              <TableCell>{task.status}</TableCell>
              <TableCell>
                <Badge
                  variant="outline"
                  className={getPriorityColor(task.priority)}
                >
                  {task.priority}
                </Badge>
              </TableCell>
              <TableCell>{task.assignedToName ?? "Unassigned"}</TableCell>
              <TableCell>
                {task.dueDate ? formatDate(task.dueDate) : "No due date"}
              </TableCell>
              <TableCell>{formatDate(task.createdAt)}</TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
  );
}
