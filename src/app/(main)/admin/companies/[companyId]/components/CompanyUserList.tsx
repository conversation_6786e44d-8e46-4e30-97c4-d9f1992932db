"use client";

import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { formatDate } from "@/lib/utils";

interface CompanyUser {
  id: string | null;
  firstName: string | null;
  lastName: string | null;
  email: string | null;
  role: "admin" | "member";
  createdAt: Date | null;
}

interface CompanyUserListProps {
  users: CompanyUser[];
}

export default function CompanyUserList({ users }: CompanyUserListProps) {
  return (
    <div className="rounded-md border">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>Name</TableHead>
            <TableHead>Email</TableHead>
            <TableHead>Role</TableHead>
            <TableHead>Joined</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {users.map((user) => (
            <TableRow key={user.id}>
              <TableCell className="font-medium">
                {user.firstName} {user.lastName}
              </TableCell>
              <TableCell>{user.email}</TableCell>
              <TableCell>{user.role ?? "No Role"}</TableCell>
              <TableCell>
                {user.createdAt ? formatDate(user.createdAt) : "N/A"}
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
  );
}
