import { notFound } from "next/navigation";
import { <PERSON>, CardHeader, CardTitle, CardContent } from "@/components/ui/card";
import { formatDate } from "@/lib/utils";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import CompanyUserList from "./components/CompanyUserList";
import CompanyReviewList from "./components/CompanyReviewList";
import CompanyTaskList from "./components/CompanyTaskList";
import { BackButton } from "../../components/BackButton";
import {
  getCompanyReviews,
  getCompanyStats,
  getCompanyTasks,
  getCompanyUsers,
  getCompanyById,
} from "../../actions/companies";

export default async function CompanyDetailPage({
  params,
}: {
  params: { companyId: string };
}) {
  // Fetch basic company info
  const company = await getCompanyById(params.companyId);

  if (!company) {
    notFound();
  }

  // Get company statistics
  const [userCount, reviewCount, taskCount] = await getCompanyStats(company.id);

  // Fetch company users
  const companyUsers = await getCompanyUsers(company.id);

  // Fetch company reviews
  const companyReviews = await getCompanyReviews(company.id);

  // Fetch company tasks
  const companyTasks = await getCompanyTasks(company.id);

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <BackButton />
          <h1 className="text-3xl font-bold">{company.companyName}</h1>
        </div>
        <p className="text-muted-foreground">
          Created {formatDate(company.createdAt)}
        </p>
      </div>

      <div className="grid gap-6 md:grid-cols-3">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Users</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-xl font-bold">{userCount[0].count}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Reviews</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-xl font-bold">{reviewCount[0].count}</div>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Tasks</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-xl font-bold">{taskCount[0].count}</div>
          </CardContent>
        </Card>
      </div>

      <Tabs defaultValue="users">
        <TabsList>
          <TabsTrigger value="users">Users ({userCount[0].count})</TabsTrigger>
          <TabsTrigger value="reviews">
            Reviews ({reviewCount[0].count})
          </TabsTrigger>
          <TabsTrigger value="tasks">Tasks ({taskCount[0].count})</TabsTrigger>
        </TabsList>

        <TabsContent value="users">
          <CompanyUserList users={companyUsers} />
        </TabsContent>
        <TabsContent value="reviews">
          <CompanyReviewList reviews={companyReviews} />
        </TabsContent>
        <TabsContent value="tasks">
          <CompanyTaskList tasks={companyTasks} />
        </TabsContent>
      </Tabs>
    </div>
  );
}
