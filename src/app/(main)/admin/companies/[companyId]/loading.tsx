export default function CompanyDetailLoading() {
  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div className="h-9 w-[200px] animate-pulse rounded bg-gray-200" />
        <div className="h-5 w-[150px] animate-pulse rounded bg-gray-200" />
      </div>

      <div className="grid gap-4 md:grid-cols-3">
        {[...Array(3)].map((_, i) => (
          <div
            key={i}
            className="h-[120px] animate-pulse rounded bg-gray-200"
          />
        ))}
      </div>

      <div className="h-[400px] animate-pulse rounded bg-gray-200" />
    </div>
  );
}
