"use client";

import { Card } from "@/components/ui/card";
import { Star } from "lucide-react";
import { formatDistanceToNow } from "date-fns";

interface FeedbackListProps {
  feedbacks: {
    id: string;
    userId: string;
    type: string;
    usabilityRating: number;
    easeOfUseRating: number;
    helpfulnessRating: number;
    message: string | null;
    willingToReview: boolean;
    willingToPay: boolean;
    createdAt: Date;
    user: {
      firstName: string;
      lastName: string;
      email: string;
    };
  }[];
}

export function FeedbackList({ feedbacks }: FeedbackListProps) {
  return (
    <div className="space-y-4">
      {feedbacks.map((feedback) => (
        <Card key={feedback.id} className="p-6">
          <div className="flex flex-col space-y-4">
            <div className="flex items-center justify-between">
              <div>
                <h3 className="font-semibold">
                  {feedback.user.firstName} {feedback.user.lastName}
                </h3>
                <p className="text-sm text-muted-foreground">
                  {feedback.user.email}
                </p>
              </div>
              <div className="text-sm text-muted-foreground">
                {formatDistanceToNow(new Date(feedback.createdAt), {
                  addSuffix: true,
                })}
              </div>
            </div>

            <div className="grid gap-4 sm:grid-cols-3">
              <div>
                <p className="text-sm font-medium">Usability</p>
                <div className="flex items-center">
                  {Array.from({ length: feedback.usabilityRating }).map(
                    (_, i) => (
                      <Star
                        key={i}
                        className="h-4 w-4 fill-primary text-primary"
                      />
                    ),
                  )}
                </div>
              </div>
              <div>
                <p className="text-sm font-medium">Ease of Use</p>
                <div className="flex items-center">
                  {Array.from({ length: feedback.easeOfUseRating }).map(
                    (_, i) => (
                      <Star
                        key={i}
                        className="h-4 w-4 fill-primary text-primary"
                      />
                    ),
                  )}
                </div>
              </div>
              <div>
                <p className="text-sm font-medium">Helpfulness</p>
                <div className="flex items-center">
                  {Array.from({ length: feedback.helpfulnessRating }).map(
                    (_, i) => (
                      <Star
                        key={i}
                        className="h-4 w-4 fill-primary text-primary"
                      />
                    ),
                  )}
                </div>
              </div>
            </div>

            {feedback.message && (
              <div>
                <p className="text-sm font-medium">Feedback</p>
                <p className="text-sm text-muted-foreground">
                  {feedback.message}
                </p>
              </div>
            )}

            <div className="flex gap-4">
              <div className="flex items-center gap-2">
                <div className="text-sm font-medium">
                  Interested in comprehensive review:
                </div>
                <div
                  className={`text-sm ${
                    feedback.willingToReview
                      ? "text-green-600"
                      : "text-muted-foreground"
                  }`}
                >
                  {feedback.willingToReview ? "Yes" : "No"}
                </div>
              </div>
              <div className="flex items-center gap-2">
                <div className="text-sm font-medium">
                  Willing to pay for tool:
                </div>
                <div
                  className={`text-sm ${
                    feedback.willingToPay
                      ? "text-green-600"
                      : "text-muted-foreground"
                  }`}
                >
                  {feedback.willingToPay ? "Yes" : "No"}
                </div>
              </div>
            </div>
          </div>
        </Card>
      ))}
    </div>
  );
}
