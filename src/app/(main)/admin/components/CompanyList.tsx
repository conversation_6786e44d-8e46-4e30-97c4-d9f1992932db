"use client";

import { useState } from "react";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { formatDate } from "@/lib/utils";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { useRouter } from "next/navigation";

interface Company {
  id: string;
  companyName: string;
  companyDomain: string;
  createdAt: Date;
  userCount: number;
  reviewCount: number;
}

interface CompanyListProps {
  companies: Company[];
}

export default function CompanyList({ companies }: CompanyListProps) {
  const router = useRouter();
  const [search, setSearch] = useState("");
  const [sortBy, setSortBy] = useState<"users" | "reviews" | "date" | "name">(
    "name",
  );

  const filteredCompanies = companies.filter((company) => {
    const matchesSearch =
      company.companyName.toLowerCase().includes(search.toLowerCase()) ||
      company.companyDomain.toLowerCase().includes(search.toLowerCase());

    return matchesSearch;
  });

  const sortedCompanies = [...filteredCompanies].sort((a, b) => {
    switch (sortBy) {
      case "users":
        return b.userCount - a.userCount;
      case "reviews":
        return b.reviewCount - a.reviewCount;
      case "date":
        return b.createdAt.getTime() - a.createdAt.getTime();
      default:
        return a.companyName.localeCompare(b.companyName);
    }
  });

  return (
    <div className="space-y-4">
      <div className="flex items-center gap-4">
        <Input
          placeholder="Search companies..."
          value={search}
          onChange={(e) => setSearch(e.target.value)}
          className="max-w-sm"
        />
        <Select
          value={sortBy}
          onValueChange={(value: typeof sortBy) => setSortBy(value)}
        >
          <SelectTrigger className="w-[180px]">
            <SelectValue placeholder="Sort by" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="name">Company Name</SelectItem>
            <SelectItem value="users">Most Users</SelectItem>
            <SelectItem value="reviews">Most Reviews</SelectItem>
            <SelectItem value="date">Newest First</SelectItem>
          </SelectContent>
        </Select>
      </div>

      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Company Name</TableHead>
              <TableHead>Domain</TableHead>
              <TableHead>Users</TableHead>
              <TableHead>Reviews</TableHead>
              <TableHead>Created</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {sortedCompanies.map((company) => (
              <TableRow
                key={company.id}
                className="cursor-pointer hover:bg-muted/50"
                onClick={() => router.push(`/admin/companies/${company.id}`)}
              >
                <TableCell className="font-medium">
                  {company.companyName}
                </TableCell>
                <TableCell>{company.companyDomain}</TableCell>
                <TableCell>{company.userCount}</TableCell>
                <TableCell>{company.reviewCount}</TableCell>
                <TableCell>{formatDate(company.createdAt)}</TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>
    </div>
  );
}
