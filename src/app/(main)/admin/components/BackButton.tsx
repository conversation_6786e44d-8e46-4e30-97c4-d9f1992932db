"use client";

import { But<PERSON> } from "@/components/ui/button";
import { ChevronLeft } from "lucide-react";
import { useRouter } from "next/navigation";

export function BackButton() {
  const router = useRouter();

  return (
    <Button
      variant="ghost"
      onClick={() => router.back()}
      className="ml-0 gap-2 pl-0"
      aria-label="Go back"
    >
      <ChevronLeft className="h-6 w-6" />
    </Button>
  );
}
