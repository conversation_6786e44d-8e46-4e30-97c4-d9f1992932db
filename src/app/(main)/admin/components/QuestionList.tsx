"use client";

import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { Archive, ArchiveRestore, Pencil, Plus, Search } from "lucide-react";
import { EditQuestionDialog } from "./EditQuestionDialog";
import { NewQuestionDialog } from "./NewQuestionDialog";
import { getQuestionsWithDetails } from "../actions/dashboard";
import { toggleQuestionArchive } from "../actions/questions";
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Input } from "@/components/ui/input";
import { useToast } from "@/components/hooks/use-toast";
import { sortQuestionsByCategory as sortQuestions } from "@/lib/utils/sort";

export interface Recommendation {
  id: string;
  text: string;
  description: string | null;
  priority: string;
  estimatedEffort: string | null;
  questionId: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface Question {
  id: string;
  text: string;
  description: string;
  guideImage: string | null;
  categoryId: string;
  category: string | null;
  isArchived: boolean;
  createdAt: Date;
  updatedAt: Date | null;
  recommendations: Recommendation[];
}

interface QuestionListProps {
  questions: Question[];
  categories: { id: string; name: string }[];
}

// Using the imported sortQuestionsByCategory function as sortQuestions

export default function QuestionList({
  questions: initialQuestions,
  categories,
}: QuestionListProps) {
  const [questions, setQuestions] = useState(sortQuestions(initialQuestions));
  const [editingQuestion, setEditingQuestion] = useState<Question | null>(null);
  const [showNewQuestionDialog, setShowNewQuestionDialog] = useState(false);
  const [searchQuery, setSearchQuery] = useState("");
  const [activeTab, setActiveTab] = useState<string | null>(null);
  const [showArchived, setShowArchived] = useState(false);
  const { toast } = useToast();

  // Filter questions based on archive status
  const filteredQuestions = questions.filter(
    (q) => q.isArchived === showArchived,
  );

  // First group questions by category
  const questionsByCategory = filteredQuestions.reduce(
    (acc, question) => {
      const category = question.category ?? "Uncategorized";
      if (!acc[category]) {
        acc[category] = [];
      }
      acc[category].push(question);
      return acc;
    },
    {} as Record<string, Question[]>,
  );

  // Then filter questions within each category based on search
  const filteredQuestionsByCategory = Object.entries(
    questionsByCategory,
  ).reduce(
    (acc, [category, categoryQuestions]) => {
      const filtered = categoryQuestions.filter(
        (question) =>
          question.text.toLowerCase().includes(searchQuery.toLowerCase()) ||
          question.description
            .toLowerCase()
            .includes(searchQuery.toLowerCase()),
      );

      if (filtered.length > 0) {
        acc[category] = filtered;
      }
      return acc;
    },
    {} as Record<string, Question[]>,
  );

  // Set initial active tab if not set
  const defaultCategory =
    Object.keys(filteredQuestionsByCategory)[0] || "Uncategorized";
  const currentTab = activeTab || defaultCategory;

  // Get all matching questions when searching
  const allMatchingQuestions = searchQuery
    ? filteredQuestions.filter(
        (question) =>
          question.text.toLowerCase().includes(searchQuery.toLowerCase()) ||
          question.description
            .toLowerCase()
            .includes(searchQuery.toLowerCase()),
      )
    : [];

  // Use categorized view only when not searching
  const showSearchResults = searchQuery.length > 0;

  const handleArchiveToggle = async (
    questionId: string,
    currentArchiveStatus: boolean,
  ) => {
    try {
      const result = await toggleQuestionArchive(
        questionId,
        !currentArchiveStatus,
      );
      if (result.success) {
        const updatedQuestions = questions.map((q) =>
          q.id === questionId ? { ...q, isArchived: !currentArchiveStatus } : q,
        );
        setQuestions(sortQuestions(updatedQuestions));
        toast({
          title: "Success",
          description: `Question ${!currentArchiveStatus ? "archived" : "unarchived"} successfully`,
        });
      } else {
        throw new Error("Failed to update archive status");
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to update archive status",
        variant: "destructive",
      });
    }
  };

  const renderQuestionCard = (question: Question, index: number) => (
    <Card key={question.id} className="p-6">
      <div className="flex w-full items-start gap-4">
        <div className="flex h-12 w-12 items-center justify-center rounded-full border">
          {index + 1}
        </div>
        <div className="flex w-full flex-col items-start gap-1">
          <div className="flex w-full items-center justify-between">
            <div className="text-xl font-bold">{question.text}</div>
            <div className="flex items-center gap-2">
              <Button
                variant="ghost"
                size="icon"
                onClick={() =>
                  handleArchiveToggle(question.id, question.isArchived)
                }
                className="h-8 w-8 flex-shrink-0"
                title={question.isArchived ? "Unarchive" : "Archive"}
              >
                {question.isArchived ? (
                  <ArchiveRestore className="h-4 w-4" />
                ) : (
                  <Archive className="h-4 w-4" />
                )}
              </Button>
              <Button
                variant="ghost"
                size="icon"
                onClick={() => setEditingQuestion(question)}
                className="h-8 w-8 flex-shrink-0"
              >
                <Pencil className="h-4 w-4" />
              </Button>
            </div>
          </div>
          <div className="flex items-center gap-2">
            <span className="rounded-full bg-muted px-2 py-0.5 text-xs text-muted-foreground">
              {question.category ?? "Uncategorized"}
            </span>
          </div>
        </div>
      </div>
    </Card>
  );

  return (
    <>
      <div className="my-6 flex items-center justify-between">
        <div className="flex items-center gap-4">
          <h2 className="text-xl font-semibold tracking-tight">
            {showArchived ? "Archived Questions" : "Manage Questions"}
          </h2>
          <Button
            variant="outline"
            onClick={() => setShowArchived(!showArchived)}
            className="gap-2"
          >
            {showArchived ? (
              <>
                <ArchiveRestore className="h-4 w-4" />
                Show Active
              </>
            ) : (
              <>
                <Archive className="h-4 w-4" />
                Show Archived
              </>
            )}
          </Button>
        </div>
        {!showArchived && (
          <Button
            onClick={() => setShowNewQuestionDialog(true)}
            className="gap-2"
          >
            <Plus className="h-4 w-4" />
            New Question
          </Button>
        )}
      </div>

      <div className="mb-6 flex items-center gap-4">
        <div className="relative flex-1">
          <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search questions..."
            className="pl-8"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
        </div>
      </div>

      {showSearchResults ? (
        <div className="grid max-h-[calc(100vh-12rem)] auto-rows-max items-start gap-4 overflow-y-auto">
          {allMatchingQuestions.map((question, index) =>
            renderQuestionCard(question, index),
          )}
        </div>
      ) : (
        <Tabs
          value={currentTab}
          onValueChange={setActiveTab}
          className="w-full"
        >
          <div className="flex items-center justify-between pb-3">
            <TabsList className="inline-flex h-9 w-full items-center justify-start rounded-none border-b bg-transparent p-0 text-muted-foreground">
              {Object.keys(filteredQuestionsByCategory).map((category) => (
                <TabsTrigger
                  key={category}
                  value={category}
                  className="relative inline-flex h-9 items-center justify-center whitespace-nowrap rounded-none border-b-2 border-b-transparent bg-transparent px-4 py-1 pb-3 pt-2 text-sm font-semibold text-muted-foreground shadow-none ring-offset-background transition-none focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:border-b-primary data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-none"
                >
                  {category}
                </TabsTrigger>
              ))}
            </TabsList>
          </div>

          {Object.entries(filteredQuestionsByCategory).map(
            ([category, categoryQuestions]) => (
              <TabsContent key={category} value={category}>
                <div className="grid gap-6">
                  <div className="grid max-h-[calc(100vh-12rem)] auto-rows-max items-start gap-4 overflow-y-auto">
                    {categoryQuestions.map((question, index) =>
                      renderQuestionCard(question, index),
                    )}
                  </div>
                </div>
              </TabsContent>
            ),
          )}
        </Tabs>
      )}

      <NewQuestionDialog
        categories={categories}
        open={showNewQuestionDialog}
        onOpenChange={setShowNewQuestionDialog}
        onQuestionCreated={() => {
          setQuestions(sortQuestions(initialQuestions));
        }}
      />

      {editingQuestion && (
        <EditQuestionDialog
          question={editingQuestion}
          categories={categories}
          isOpen={!!editingQuestion}
          onClose={() => setEditingQuestion(null)}
          onQuestionUpdated={async () => {
            const result = await getQuestionsWithDetails();
            if (result) {
              setQuestions(sortQuestions(result));
            }
          }}
        />
      )}
    </>
  );
}
