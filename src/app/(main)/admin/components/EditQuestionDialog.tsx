"use client";

import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  <PERSON><PERSON>,
  Dialog<PERSON>ontent,
  <PERSON><PERSON>Header,
  DialogTitle,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { ImageUpload } from "@/components/ImageUpload";
import { Loader2 } from "lucide-react";
import { useToast } from "@/components/hooks/use-toast";
import { updateQuestion, updateRecommendation } from "../actions/questions";
import type { Question, Recommendation } from "./QuestionList";
import { Trash2 } from "lucide-react";

interface EditQuestionDialogProps {
  question: Question;
  categories: { id: string; name: string }[];
  isOpen: boolean;
  onClose: () => void;
  onQuestionUpdated: () => void;
}

interface FormRecommendation {
  id: string;
  text: string;
  description: string;
  priority: string;
  estimatedEffort: string;
}

interface FormData {
  text: string;
  description: string;
  categoryId: string;
  guideImage: string;
  recommendations: FormRecommendation[];
}

export function EditQuestionDialog({
  question,
  categories,
  isOpen,
  onClose,
  onQuestionUpdated,
}: EditQuestionDialogProps) {
  const [isLoading, setIsLoading] = useState(false);
  const { toast } = useToast();
  const [formData, setFormData] = useState<FormData>({
    text: question.text,
    description: question.description,
    categoryId: question.categoryId,
    guideImage: question.guideImage || "",
    recommendations: question.recommendations.map((rec: Recommendation) => ({
      id: rec.id,
      text: rec.text,
      description: rec.description || "",
      priority: rec.priority,
      estimatedEffort: rec.estimatedEffort || "",
    })),
  });

  const handleSubmit = async () => {
    setIsLoading(true);
    try {
      const questionData = {
        text: formData.text,
        description: formData.description,
        categoryId: formData.categoryId,
        guideImage: formData.guideImage,
      };

      // Optimistic update
      onQuestionUpdated();

      const result = await updateQuestion(question.id, questionData);
      if (!result.success) {
        throw new Error("Failed to update question");
      }

      // Update recommendations
      for (const rec of formData.recommendations) {
        const recData = {
          text: rec.text,
          description: rec.description,
          priority: rec.priority,
          estimatedEffort: rec.estimatedEffort,
        };
        const recResult = await updateRecommendation(rec.id, recData);
        if (!recResult.success) {
          throw new Error("Failed to update recommendations");
        }
      }

      // Success - revalidate and close
      onQuestionUpdated(); // Call again to ensure latest data
      toast({
        title: "Success",
        description: "Question updated successfully",
      });
      onClose();
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to update question",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-h-[90vh] overflow-y-auto sm:max-w-[800px]">
        <DialogHeader>
          <DialogTitle>Edit Question</DialogTitle>
        </DialogHeader>
        <div className="space-y-6 py-4">
          <div className="space-y-4">
            <div className="space-y-2">
              <Label>Question Text</Label>
              <Textarea
                value={formData.text}
                onChange={(e) =>
                  setFormData((prev) => ({ ...prev, text: e.target.value }))
                }
                className="min-h-[100px]"
              />
            </div>

            <div className="space-y-2">
              <Label>Category</Label>
              <Select
                value={formData.categoryId}
                onValueChange={(value) =>
                  setFormData((prev) => ({ ...prev, categoryId: value }))
                }
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select category" />
                </SelectTrigger>
                <SelectContent>
                  {categories.map((category) => (
                    <SelectItem key={category.id} value={category.id}>
                      {category.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label>Description</Label>
              <Textarea
                value={formData.description}
                onChange={(e) =>
                  setFormData((prev) => ({
                    ...prev,
                    description: e.target.value,
                  }))
                }
                className="min-h-[100px]"
              />
            </div>

            <div className="space-y-2">
              <Label>Guide Image</Label>
              <ImageUpload
                currentImage={question.guideImage}
                onUpload={(url) =>
                  setFormData((prev) => ({ ...prev, guideImage: url }))
                }
              />
            </div>

            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <Label>Recommendation</Label>
              </div>
              {formData.recommendations.length > 0 ? (
                <div className="space-y-2 rounded-lg border p-4">
                  <Input
                    value={formData.recommendations[0].text}
                    onChange={(e) => {
                      setFormData((prev) => ({
                        ...prev,
                        recommendations: [
                          {
                            ...prev.recommendations[0],
                            text: e.target.value,
                          },
                        ],
                      }));
                    }}
                    placeholder="Recommendation text"
                  />
                  <Textarea
                    value={formData.recommendations[0].description}
                    onChange={(e) => {
                      setFormData((prev) => ({
                        ...prev,
                        recommendations: [
                          {
                            ...prev.recommendations[0],
                            description: e.target.value,
                          },
                        ],
                      }));
                    }}
                    placeholder="Recommendation description"
                    className="min-h-[100px]"
                  />
                  <div className="flex gap-2">
                    <div className="flex-1 space-y-2">
                      <Label>Priority</Label>
                      <Select
                        value={formData.recommendations[0].priority}
                        onValueChange={(value) => {
                          setFormData((prev) => ({
                            ...prev,
                            recommendations: [
                              {
                                ...prev.recommendations[0],
                                priority: value,
                              },
                            ],
                          }));
                        }}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="Priority" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="high">High</SelectItem>
                          <SelectItem value="medium">Medium</SelectItem>
                          <SelectItem value="low">Low</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    <div className="flex-1 space-y-2">
                      <Label>Estimated Effort</Label>
                      <Input
                        value={formData.recommendations[0].estimatedEffort}
                        onChange={(e) => {
                          setFormData((prev) => ({
                            ...prev,
                            recommendations: [
                              {
                                ...prev.recommendations[0],
                                estimatedEffort: e.target.value,
                              },
                            ],
                          }));
                        }}
                        placeholder="e.g., 2-3 days"
                      />
                    </div>
                  </div>
                </div>
              ) : (
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => {
                    setFormData((prev) => ({
                      ...prev,
                      recommendations: [
                        {
                          id: "",
                          text: "",
                          description: "",
                          priority: "medium",
                          estimatedEffort: "",
                        },
                      ],
                    }));
                  }}
                >
                  Add Recommendation
                </Button>
              )}
            </div>
          </div>

          <div className="flex justify-end gap-2">
            <Button variant="outline" onClick={onClose} disabled={isLoading}>
              Cancel
            </Button>
            <Button onClick={handleSubmit} disabled={isLoading}>
              {isLoading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
              Save Changes
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
