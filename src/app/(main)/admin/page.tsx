import HeaderBox from "@/components/HeaderBox";
import { Card } from "@/components/ui/card";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { requireAdmin } from "@/app/(auth)/actions/auth";
import CompanyList from "@/app/(main)/admin/components/CompanyList";
import UserList from "@/app/(main)/admin/components/UserList";
import ReviewList from "@/app/(main)/admin/components/ReviewList";
import TaskList from "@/app/(main)/admin/components/TaskList";
import QuestionList from "@/app/(main)/admin/components/QuestionList";

import {
  getGlobalStats,
  getCompaniesWithCounts,
  getUsersWithCompanies,
  getReviewsWithDetails,
  getTasksWithDetails,
  getQuestionsWithDetails,
  getFeedbackWithUsers,
} from "./actions/dashboard";

import { getCategories } from "@/server/actions/categories";
import { FeedbackList } from "./components/FeedbackList";
import { ExportButton } from "@/components/ExportButton";

export default async function AdminDashboardPage() {
  const user = await requireAdmin();

  // Get global stats
  const [
    globalStats,
    companiesWithCounts,
    usersWithCompanies,
    reviewsWithDetails,
    tasksWithDetails,
    questionsWithDetails,
    categories,
    feedbacks,
  ] = await Promise.all([
    getGlobalStats(),
    getCompaniesWithCounts(),
    getUsersWithCompanies(),
    getReviewsWithDetails(),
    getTasksWithDetails(),
    getQuestionsWithDetails(),
    getCategories(),
    getFeedbackWithUsers(),
  ]);

  return (
    <div className="w-full space-y-8">
      <div className="mb-8 flex w-full items-start justify-between">
        <HeaderBox
          type="greeting"
          title="Admin Dashboard - "
          subtext="Monitor and manage your entire application"
          user={user?.firstName ?? ""}
        />
      </div>

      <div className="grid gap-4 md:grid-cols-2 md:gap-8 lg:grid-cols-5">
        <Card className="p-4">
          <h3 className="text-sm font-medium text-muted-foreground">
            Companies
          </h3>
          <p className="mt-2 text-2xl font-bold">{globalStats.companies}</p>
        </Card>
        <Card className="p-4">
          <h3 className="text-sm font-medium text-muted-foreground">Users</h3>
          <p className="mt-2 text-2xl font-bold">{globalStats.users}</p>
        </Card>
        <Card className="p-4">
          <h3 className="text-sm font-medium text-muted-foreground">Reviews</h3>
          <p className="mt-2 text-2xl font-bold">{globalStats.reviews}</p>
        </Card>
        <Card className="p-4">
          <h3 className="text-sm font-medium text-muted-foreground">Tasks</h3>
          <p className="mt-2 text-2xl font-bold">{globalStats.tasks}</p>
        </Card>
        <Card className="p-4">
          <h3 className="text-sm font-medium text-muted-foreground">Reports</h3>
          <p className="mt-2 text-2xl font-bold">{globalStats.reports}</p>
        </Card>
      </div>

      <Tabs defaultValue="companies" className="w-full">
        <TabsList>
          <TabsTrigger value="companies">Companies</TabsTrigger>
          <TabsTrigger value="users">Users</TabsTrigger>
          <TabsTrigger value="reviews">Reviews</TabsTrigger>
          <TabsTrigger value="tasks">Tasks</TabsTrigger>
          <TabsTrigger value="questions">Questions</TabsTrigger>
          <TabsTrigger value="feedback">Feedback</TabsTrigger>
        </TabsList>

        <TabsContent value="companies">
          <CompanyList companies={companiesWithCounts} />
        </TabsContent>
        <TabsContent value="users">
          <UserList users={usersWithCompanies} />
        </TabsContent>
        <TabsContent value="reviews">
          <ReviewList reviews={reviewsWithDetails} />
        </TabsContent>
        <TabsContent value="tasks">
          <TaskList tasks={tasksWithDetails} />
        </TabsContent>
        <TabsContent value="questions">
          <QuestionList
            questions={questionsWithDetails}
            categories={categories}
          />
        </TabsContent>
        <TabsContent value="feedback">
          <div className="space-y-8">
            <div className="flex items-center justify-between">
              <h2 className="text-xl font-semibold tracking-tight">
                User Feedback
              </h2>
              <ExportButton data={feedbacks} filename="feedback-export.csv" />
            </div>

            <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
              <Card className="p-4">
                <h3 className="text-sm font-medium text-muted-foreground">
                  Total Feedback
                </h3>
                <p className="mt-2 text-2xl font-bold">{feedbacks.length}</p>
              </Card>
              <Card className="p-4">
                <h3 className="text-sm font-medium text-muted-foreground">
                  Average Rating
                </h3>
                <p className="mt-2 text-2xl font-bold">
                  {(
                    feedbacks.reduce(
                      (acc: number, f) =>
                        acc +
                        (f.usabilityRating +
                          f.easeOfUseRating +
                          f.helpfulnessRating) /
                          3,
                      0,
                    ) / feedbacks.length || 0
                  ).toFixed(1)}
                </p>
              </Card>
              <Card className="p-4">
                <h3 className="text-sm font-medium text-muted-foreground">
                  Willing to Pay
                </h3>
                <p className="mt-2 text-2xl font-bold">
                  {feedbacks.filter((f) => f.willingToPay).length}
                </p>
              </Card>
              <Card className="p-4">
                <h3 className="text-sm font-medium text-muted-foreground">
                  Interested in Review
                </h3>
                <p className="mt-2 text-2xl font-bold">
                  {feedbacks.filter((f) => f.willingToReview).length}
                </p>
              </Card>
            </div>

            <FeedbackList feedbacks={feedbacks} />
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}
