"use client";

import { startTransition, useEffect, useState, useRef } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { LoadingSpinner } from "@/components/LoadingSpinner";
import { createReview, deleteReview } from "@/server/actions/reviews";
import { useToast } from "@/components/hooks/use-toast";
import ConfirmAssessmentDialog from "@/components/ConfirmAssessmentDialog";
import { useUser } from "@clerk/nextjs";
import { getUserCompany } from "@/server/actions/companies";

export default function NewReviewPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const { toast } = useToast();
  const { user } = useUser();
  const type = searchParams.get("type") as "basic" | "comprehensive";
  const [isLoading, setIsLoading] = useState(true);
  const [showConfirm, setShowConfirm] = useState(false);
  const [newReviewId, setNewReviewId] = useState<string | null>(null);
  const initialized = useRef(false);

  useEffect(() => {
    if (!type || !user) {
      router.push("/reviews/select");
      return;
    }

    if (initialized.current) return;
    initialized.current = true;

    const initializeReview = async () => {
      try {
        setIsLoading(true);

        // Get company first
        const companyResult = await getUserCompany(user.id);
        if (!companyResult.success || !companyResult.data) {
          throw new Error("Failed to get company");
        }

        const newReview = await createReview({
          title: `New ${type.charAt(0).toUpperCase() + type.slice(1)} Review`,
          type,
          companyId: companyResult.data.id,
          createdByUserId: user.id,
        });

        if (newReview.success && newReview.data) {
          setNewReviewId(newReview.data.id);
          setShowConfirm(true);
        } else {
          toast({
            title: "Error",
            description: "Failed to create review",
            variant: "destructive",
          });
          router.push("/reviews/manage");
        }
      } catch (error) {
        console.error("Error creating review:", error);
        toast({
          title: "Error",
          description: "Failed to create review",
          variant: "destructive",
        });
        router.push("/reviews/manage");
      } finally {
        setIsLoading(false);
      }
    };

    initializeReview();
  }, [type, router, toast, user]);

  const handleConfirm = () => {
    router.push(`/reviews/${newReviewId}/review`);
  };

  const handleCancel = async () => {
    try {
      setIsLoading(true);
      if (newReviewId) {
        const result = await deleteReview(newReviewId);
        if (!result.success) {
          toast({
            title: "Error",
            description: "Failed to delete review",
            variant: "destructive",
          });
        }
      }
      router.push("/reviews/select");
    } catch (error) {
      console.error("Error during cancellation:", error);
      toast({
        title: "Error",
        description: "An error occurred while canceling",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  if (isLoading) {
    return (
      <div className="flex min-h-screen items-center justify-center">
        <LoadingSpinner className="h-8 w-8" />
      </div>
    );
  }

  if (showConfirm && newReviewId) {
    return (
      <ConfirmAssessmentDialog
        isOpen={showConfirm}
        onClose={() => {
          startTransition(() => {
            router.push("/reviews/select");
          });
        }}
        onConfirm={handleConfirm}
        onCancel={handleCancel}
        type={type}
      />
    );
  }

  return null;
}
