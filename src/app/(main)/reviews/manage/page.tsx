import { currentUser } from "@clerk/nextjs/server";
import Header<PERSON>ox from "@/components/HeaderBox";
import { But<PERSON> } from "@/components/ui/button";
import { PlusCircle } from "lucide-react";
import AssessmentManageCard from "@/components/AssessmentManageCard";
import InviteTeamDialog from "@/components/InviteTeamDialog";
import Link from "next/link";
import { getCompanyReviews } from "@/server/actions/reviews";
import { getUserCompany } from "@/server/actions/companies";
import StatsCard from "@/components/StatsCard";
import { Suspense } from "react";
import CreateReviewButton from "@/components/CreateReviewButton";

interface Review {
  id: string;
  title: string;
  type: "basic" | "comprehensive";
  status: "draft" | "in_progress" | "completed";
  createdAt: Date;
  progress?: Record<string, { total: number; answered: number }>;
  isArchived: boolean | null;
}

export default async function AssessmentsPage() {
  const user = await currentUser();
  if (!user) return null;

  // Get user's company
  const company = await getUserCompany(user.id);
  if (!company.success || !company.data) {
    return <div>No company found</div>;
  }

  // Get reviews for the company
  const reviewsResult = await getCompanyReviews(company.data.id);
  const reviews: Review[] =
    reviewsResult.success && reviewsResult.data
      ? reviewsResult.data.map((review) => ({
          id: review.id,
          title: review.title,
          type: review.type,
          status: review.status,
          createdAt: review.createdAt,
          progress: review.progress,
          isArchived: review.isArchived,
        }))
      : [];

  // Filter out archived reviews by default
  const activeReviews = reviews.filter((review) => !review.isArchived);

  // Calculate stats (only for active reviews)
  const stats = {
    total: activeReviews.length,
    drafts: activeReviews.filter((r) => r.status === "draft").length,
    completed: activeReviews.filter((r) => r.status === "completed").length,
    lastReview: activeReviews.length
      ? new Date(
          Math.max(...activeReviews.map((r) => r.createdAt.getTime())),
        ).toLocaleDateString("en-GB", {
          day: "2-digit",
          month: "2-digit",
          year: "numeric",
        })
      : "No reviews yet",
  };

  // Get archived reviews
  const archivedReviews = reviews.filter((review) => review.isArchived);

  return (
    <div className="w-full space-y-6 sm:space-y-8">
      <div className="mb-4 flex w-full items-start justify-between sm:mb-8">
        <HeaderBox
          type="greeting"
          title="Welcome"
          subtext="Build an inclusive workplace and culture"
          user={user.firstName ?? ""}
        />
        {/* <InviteTeamDialog /> */}
      </div>

      <div className="grid grid-cols-2 gap-3 sm:gap-4 md:grid-cols-2 md:gap-6 lg:grid-cols-4">
        <StatsCard stats={stats} />
      </div>

      <div className="space-y-6 sm:space-y-8">
        {/* Active Reviews Section */}
        <div className="space-y-3 sm:space-y-4">
          <div className="flex items-center justify-between px-1">
            <h3 className="text-base font-bold sm:text-lg">Active Reviews</h3>
            <CreateReviewButton />
          </div>

          <div className="space-y-3 sm:space-y-4">
            {activeReviews.map((review) => (
              <AssessmentManageCard
                key={review.id}
                assessment={{
                  id: review.id,
                  title: review.title,
                  type: review.type,
                  date: new Date(review.createdAt),
                  status: review.status,
                  tasksCompleted: 0,
                  totalTasks: 0,
                  isArchived: review.isArchived,
                }}
              />
            ))}

            {activeReviews.length === 0 && (
              <div className="rounded-lg border bg-card px-4 py-6 text-center text-sm text-muted-foreground sm:px-6 sm:py-8 sm:text-base">
                No active reviews found. Create your first review to get
                started.
              </div>
            )}
          </div>
        </div>

        {/* Archived Reviews Section */}
        <div className="space-y-3 sm:space-y-4">
          <div className="flex items-center justify-between px-1">
            <h3 className="text-base font-bold sm:text-lg">Archived Reviews</h3>
          </div>

          <div className="space-y-3 sm:space-y-4">
            {archivedReviews.map((review) => (
              <AssessmentManageCard
                key={review.id}
                assessment={{
                  id: review.id,
                  title: review.title,
                  type: review.type,
                  date: new Date(review.createdAt),
                  status: review.status,
                  tasksCompleted: 0,
                  totalTasks: 0,
                  isArchived: review.isArchived,
                }}
              />
            ))}

            {archivedReviews.length === 0 && (
              <div className="rounded-lg border bg-card px-4 py-6 text-center text-sm text-muted-foreground sm:px-6 sm:py-8 sm:text-base">
                No archived reviews found.
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
