export default function ManageReviewsLoading() {
  return (
    <div className="animate-pulse">
      <div className="mb-6 h-8 w-64 rounded bg-gray-200" />
      <div className="grid gap-4 md:grid-cols-2 md:gap-8 lg:grid-cols-4">
        {[...Array(4)].map((_, i) => (
          <div key={i} className="h-32 rounded bg-gray-200" />
        ))}
      </div>
      <div className="mt-8 grid grid-cols-1 gap-4">
        {[...Array(3)].map((_, i) => (
          <div key={i} className="h-32 rounded bg-gray-200" />
        ))}
      </div>
    </div>
  );
}
