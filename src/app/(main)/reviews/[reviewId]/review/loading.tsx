export default function ReviewLoading() {
  return (
    <div className="animate-pulse">
      <div className="mb-6 h-8 w-64 gap-2 rounded bg-gray-200" />

      {/* 4 small grids */}
      <div className="mb-6 grid grid-cols-2 gap-4 sm:grid-cols-4">
        {[...Array(4)].map((_, i) => (
          <div key={i} className="h-16 rounded bg-gray-200" />
        ))}
      </div>

      {/* 2/3 and 1/3 columns */}
      <div className="grid grid-cols-1 gap-4 lg:grid-cols-3">
        <div className="h-96 rounded bg-gray-200 lg:col-span-2" />
        <div className="h-96 rounded bg-gray-200" />
      </div>
    </div>
  );
}
