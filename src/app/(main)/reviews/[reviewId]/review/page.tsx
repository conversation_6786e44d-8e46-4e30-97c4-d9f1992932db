import { currentUser } from "@clerk/nextjs/server";
import { redirect } from "next/navigation";
import { getUserCompany } from "@/server/actions/companies";
import { getReviewQuestions, getReviewDetails } from "@/server/actions/reviews";
import NewAssessmentForm from "@/components/NewAssessmentForm";
import { type QuestionWithCategory } from "@/types";
import { sortQuestionsByCategory } from "@/lib/utils/sort";

export default async function ReviewPage({
  params,
}: {
  params: { reviewId: string };
}) {
  const { reviewId } = params;

  const user = await currentUser();
  if (!user) {
    redirect("/sign-in");
  }

  const company = await getUserCompany(user.id);
  if (!company.success || !company.data) {
    redirect("/onboarding");
  }

  // Get review details first
  const reviewDetails = await getReviewDetails(reviewId);
  if (!reviewDetails.success || !reviewDetails.data) {
    redirect("/reviews");
  }

  // Get questions and merge with existing answers
  const questionsResult = await getReviewQuestions();
  if (!questionsResult.success || !questionsResult.data) {
    redirect("/reviews");
  }

  const initialQuestions: QuestionWithCategory[] = sortQuestionsByCategory(
    questionsResult.data.map((q) => {
      const existingAnswer = reviewDetails.data.questions?.find(
        (rq) => rq.id === q.id,
      );

      return {
        id: q.id,
        text: q.text,
        description: q.description,
        guideImage: q.guideImage,
        category: q.category,
        questionGuide: {
          description: q.description,
          image: q.guideImage || "",
        },
        categoryId: "",
        createdAt: new Date(),
        updatedAt: new Date(),
        answer: existingAnswer?.answer,
        comment: existingAnswer?.comment || undefined,
      };
    }),
  );

  return (
    <div className="mx-auto">
      <NewAssessmentForm
        userId={user.id}
        userName={user.firstName ?? "User"}
        initialQuestions={initialQuestions}
        reviewId={reviewId}
        reviewType={reviewDetails.data.review.type}
      />
    </div>
  );
}
