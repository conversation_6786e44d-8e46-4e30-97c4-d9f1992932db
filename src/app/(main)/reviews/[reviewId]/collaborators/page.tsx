import { currentUser, clerkClient } from "@clerk/nextjs/server";
import { redirect } from "next/navigation";
import Header<PERSON>ox from "@/components/HeaderBox";
import { getUserCompany } from "@/server/actions/companies";
import { getReviewCollaborators } from "@/server/actions/reviews";
import CollaboratorManagement from "@/components/CollaboratorManagement";
import { type Collaborator } from "@/types";

export default async function CollaboratorsPage({
  params,
}: {
  params: { reviewId: string };
}) {
  // Get current user
  const user = await currentUser();
  if (!user) {
    redirect("/sign-in");
  }

  // Get company
  const company = await getUserCompany(user.id);
  if (!company.success || !company.data) {
    return <div>No company found</div>;
  }

  const collaboratorsResult = await getReviewCollaborators(params.reviewId);

  // Fetch all user details in parallel if we have collaborators
  const collaborators: Collaborator[] = await Promise.all(
    collaboratorsResult.success && collaboratorsResult.data
      ? collaboratorsResult.data.map(async (c) => {
          // Fetch user details from Clerk using clerkClient
          const userInfo = await clerkClient.users.getUser(c.clerkUserId);

          return {
            id: c.id,
            userId: c.clerkUserId,
            name: `${userInfo.firstName} ${userInfo.lastName}`,
            email: userInfo.emailAddresses[0]?.emailAddress ?? "",
            role: c.role === "owner" ? "editor" : "viewer",
          };
        })
      : [],
  );

  return (
    <div className="w-full space-y-8">
      <div className="mb-8 flex w-full items-start justify-between">
        <HeaderBox
          type="title"
          title="Collaborators"
          subtext="Manage review collaborators and their roles"
        />
      </div>

      <div className="rounded-lg border bg-card">
        <div className="p-6">
          <CollaboratorManagement
            reviewId={params.reviewId}
            collaborators={collaborators}
            currentUserId={user.id}
            isOwner={true}
          />
        </div>
      </div>
    </div>
  );
}
