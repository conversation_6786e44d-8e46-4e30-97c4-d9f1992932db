import { currentUser } from "@clerk/nextjs/server";
import { redirect } from "next/navigation";
import Link from "next/link";
import { getUserCompany } from "@/server/actions/companies";
import { getReviewDetails, getReviewTitle } from "@/server/actions/reviews";
import { Card, CardContent, CardHeader } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Progress } from "@/components/ui/progress";
import {
  ArrowLeft,
  CheckCircle,
  XCircle,
  MinusCircle,
  HelpCircle,
  FileText,
  ChevronLeft,
} from "lucide-react";
import { ReviewFeedbackSection } from "@/components/ReviewFeedbackSection";
import { ViewReportButton } from "@/components/ViewReportButton";

export default async function FeedbackPage({
  params,
}: {
  params: { reviewId: string };
}) {
  const { reviewId } = params;

  const user = await currentUser();
  if (!user) {
    redirect("/sign-in");
  }

  const company = await getUserCompany(user.id);
  if (!company.success || !company.data) {
    redirect("/onboarding");
  }

  const reviewDetails = await getReviewDetails(reviewId);
  if (!reviewDetails.success || !reviewDetails.data) {
    redirect("/reviews");
  }

  const reviewTitleResult = await getReviewTitle(reviewId);
  const reviewTitle = reviewTitleResult.success ? reviewTitleResult.data : "";

  // Calculate category scores
  const categoryScores = reviewDetails.data.questions.reduce(
    (acc, question) => {
      if (!acc[question.category]) {
        acc[question.category] = {
          total: 0,
          yes: 0,
          no: 0,
          partially: 0,
          na: 0,
        };
      }
      acc[question.category].total++;
      if (question.answer) {
        acc[question.category][
          question.answer as "yes" | "no" | "partially" | "na"
        ]++;
      }
      return acc;
    },
    {} as Record<
      string,
      { total: number; yes: number; no: number; partially: number; na: number }
    >,
  );

  // Format questions for review cards
  const answeredQuestions = reviewDetails.data.questions
    .filter((q) => q.answer)
    .map((q) => ({
      id: q.id,
      text: q.text,
      description: q.description,
      guideImage: q.guideImage,
      category: q.category,
      categoryId: "",
      createdAt: new Date(),
      updatedAt: new Date(),
      questionGuide: {
        description: q.description,
        image: q.guideImage || "",
      },
      answer: q.answer as "yes" | "no" | "partially" | "na",
      comment: q.comment || undefined,
    }));

  return (
    <div className="mx-auto">
      <div className="mb-8 flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Link href="/reviews/manage">
            <Button variant="ghost" size="icon" className="h-9 w-9">
              <ChevronLeft className="h-6 w-6" />
            </Button>
          </Link>
          <div>
            <h1 className="text-2xl font-bold">
              Review Feedback - {reviewTitle}
            </h1>
            <p className="text-muted-foreground">
              Review completed on{" "}
              {new Date(
                reviewDetails.data.review.updatedAt,
              ).toLocaleDateString()}
            </p>
          </div>
        </div>
        <ViewReportButton reviewId={reviewId} />
      </div>

      {/* Updated Questions Section */}
      <div className="mx-auto grid w-full flex-1 auto-rows-max gap-4">
        <div className="flex items-center justify-between gap-4">
          <h3 className="text-lg tracking-tight">Review</h3>
        </div>

        <ReviewFeedbackSection
          answeredQuestions={answeredQuestions}
          reviewId={reviewId}
          userId={user.id}
          firstName={user.firstName ?? ""}
          lastName={user.lastName ?? ""}
        />
      </div>
    </div>
  );
}
