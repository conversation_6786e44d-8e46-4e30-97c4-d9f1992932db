export default function ReportLoading() {
  return (
    <div className="animate-pulse">
      <div className="mb-6 h-8 w-64 rounded bg-gray-200" />
      <div className="grid grid-cols-6 gap-4">
        {/* First row */}
        {[...Array(1)].map((_, i) => (
          <div
            key={`row1-${i}`}
            className="col-span-6 h-64 rounded bg-gray-200"
          />
        ))}

        {/* Second row */}
        {[...Array(3)].map((_, i) => (
          <div
            key={`row2-${i}`}
            className="col-span-2 h-32 rounded bg-gray-200"
          />
        ))}

        {/* Third row */}
        {[...Array(2)].map((_, i) => (
          <div
            key={`row3-${i}`}
            className="col-span-3 h-64 rounded bg-gray-200"
          />
        ))}
      </div>
    </div>
  );
}
