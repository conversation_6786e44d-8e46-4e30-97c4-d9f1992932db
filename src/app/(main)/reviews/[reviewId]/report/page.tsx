import { currentUser } from "@clerk/nextjs/server";
import { redirect } from "next/navigation";
import { getUserCompany } from "@/server/actions/companies";
import { generateReport } from "@/server/actions/reports";
import { ReportPageContent } from "@/components/ReportPageContent";

export default async function ReportPage({
  params,
}: {
  params: { reviewId: string };
}) {
  const user = await currentUser();
  if (!user) {
    redirect("/sign-in");
  }

  const company = await getUserCompany(user.id);
  if (!company.success || !company.data) {
    redirect("/onboarding");
  }

  // Generate or get existing report
  const reportResult = await generateReport(params.reviewId, user.id);
  if (!reportResult.success || !reportResult.data) {
    redirect("/reviews");
  }

  return (
    <ReportPageContent
      reviewId={params.reviewId}
      isAdmin={false}
      ragScores={reportResult.data.ragScores}
      categoryScores={reportResult.data.categoryScores}
      recommendations={reportResult.data.recommendations.map((rec) => ({
        ...rec,
        description: rec.description || null,
      }))}
    />
  );
}
