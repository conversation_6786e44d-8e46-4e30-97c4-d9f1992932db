import { currentUser } from "@clerk/nextjs/server";
import Header<PERSON><PERSON> from "@/components/HeaderBox";
import { getUserCompany } from "@/server/actions/companies";
import { getReviewHistory } from "@/server/actions/reviews";
import { getReportHistory } from "@/server/actions/reports";
import ReportHistory from "@/components/ReportHistory";
import { formatDistanceToNow, parseISO, format } from "date-fns";
import { Card } from "@/components/ui/card";
import { Clock, MessageSquare, RefreshCcw, Edit } from "lucide-react";
import { ReviewTimelineChart } from "@/components/ReviewTimelineChart";

type HistoryChange = {
  id: string;
  changes: {
    userName: string;
    timestamp: string;
    type: "answer" | "comment" | "status";
    details: {
      oldValue?: string;
      newValue: string;
    };
  }[];
};

type TimelineData = {
  date: string;
  answers: number;
  comments: number;
  status: number;
  total: number;
  yes: number;
  no: number;
  partially: number;
  na: number;
};

export default async function ReviewHistoryPage({
  params,
}: {
  params: { reviewId: string };
}) {
  const user = await currentUser();
  if (!user) return null;

  const company = await getUserCompany(user.id);
  if (!company.success || !company.data) {
    return <div>No company found</div>;
  }

  // Get both review history and report history
  const [reviewHistoryResult, reportsResult] = await Promise.all([
    getReviewHistory(params.reviewId),
    getReportHistory(params.reviewId),
  ]);

  const reviewHistory = reviewHistoryResult.success
    ? (reviewHistoryResult.data?.map((item: any) => ({
        id: item.id,
        changes: item.changes.map((change: any) => ({
          userName: change.userName,
          timestamp: change.timestamp.toISOString(),
          type: change.type,
          details: {
            oldValue: change.details.oldValue,
            newValue: change.details.newValue,
          },
        })),
      })) ?? [])
    : [];
  const reports = reportsResult.success ? reportsResult.data : [];

  // Process history data for timeline
  const timelineData = processTimelineData(reviewHistory);

  // Add console.log to debug
  console.log("Timeline Data:", timelineData);

  return (
    <div className="w-full space-y-8">
      <div className="mb-8 flex w-full items-start justify-between">
        <HeaderBox
          type="title"
          title="Review History"
          subtext="View changes and reports over time"
        />
      </div>

      <ReviewTimelineChart timelineData={timelineData} />

      <div className="grid gap-6 lg:grid-cols-3">
        <div>
          <h3 className="mb-4 text-lg font-semibold">Changes History</h3>
          <div className="space-y-4">
            {reviewHistory.map((change: HistoryChange) => (
              <Card key={change.id} className="p-4">
                <div className="flex items-start space-x-4">
                  {getChangeIcon(change.changes[0].type)}
                  <div className="flex-1">
                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium">
                        {change.changes[0].userName}
                      </span>
                      <span className="text-sm text-muted-foreground">
                        {formatDistanceToNow(
                          new Date(change.changes[0].timestamp),
                          { addSuffix: true },
                        )}
                      </span>
                    </div>
                    <p className="mt-1 text-sm text-muted-foreground">
                      {getChangeDescription(change.changes[0])}
                    </p>
                  </div>
                </div>
              </Card>
            ))}
            {reviewHistory.length === 0 && (
              <div className="p-4 text-center text-muted-foreground">
                No changes recorded yet
              </div>
            )}
          </div>
        </div>

        <div className="lg:col-span-2">
          <ReportHistory reports={reports ?? []} />
        </div>
      </div>
    </div>
  );
}

function getChangeIcon(type: "answer" | "comment" | "status") {
  switch (type) {
    case "answer":
      return <Edit className="h-5 w-5 text-blue-500" />;
    case "comment":
      return <MessageSquare className="h-5 w-5 text-green-500" />;
    case "status":
      return <RefreshCcw className="h-5 w-5 text-orange-500" />;
    default:
      return <Clock className="h-5 w-5 text-gray-500" />;
  }
}

function getChangeDescription(change: {
  type: "answer" | "comment" | "status";
  details: { oldValue?: string; newValue: string };
}) {
  switch (change.type) {
    case "answer":
      return `Changed answer from "${change.details.oldValue}" to "${change.details.newValue}"`;
    case "comment":
      return "Added a comment";
    case "status":
      return `Changed status from "${change.details.oldValue}" to "${change.details.newValue}"`;
    default:
      return "Made a change";
  }
}

function processTimelineData(history: HistoryChange[]): TimelineData[] {
  // Create a map to store changes by date
  const changesByDate = new Map<
    string,
    {
      answers: number;
      comments: number;
      status: number;
      yes: number;
      no: number;
      partially: number;
      na: number;
    }
  >();

  // Process each history item and its changes
  history.forEach((item) => {
    item.changes.forEach((change) => {
      const timestamp = new Date(change.timestamp);
      if (isNaN(timestamp.getTime())) {
        console.error("Invalid timestamp:", change.timestamp);
        return;
      }

      const date = format(timestamp, "yyyy-MM-dd");

      const current = changesByDate.get(date) || {
        answers: 0,
        comments: 0,
        status: 0,
        yes: 0,
        no: 0,
        partially: 0,
        na: 0,
      };

      // Increment the appropriate counters
      switch (change.type) {
        case "answer":
          current.answers += 1;
          // Also track the specific answer
          switch (change.details.newValue.toLowerCase()) {
            case "yes":
              current.yes += 1;
              break;
            case "no":
              current.no += 1;
              break;
            case "partially":
              current.partially += 1;
              break;
            case "na":
              current.na += 1;
              break;
          }
          break;
        case "comment":
          current.comments += 1;
          break;
        case "status":
          current.status += 1;
          break;
      }

      changesByDate.set(date, current);
    });
  });

  return Array.from(changesByDate.entries())
    .map(([date, counts]) => ({
      date,
      ...counts,
      total: counts.answers + counts.comments + counts.status,
    }))
    .sort((a, b) => a.date.localeCompare(b.date));
}
