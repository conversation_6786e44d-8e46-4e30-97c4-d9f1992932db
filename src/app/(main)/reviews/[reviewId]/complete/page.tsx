"use client";

import { useEffect, useState } from "react";
import { FeedbackDialog } from "@/components/FeedbackDialog";
import { Card } from "@/components/ui/card";
import { CheckCircle2 } from "lucide-react";
import Link from "next/link";
import { But<PERSON> } from "@/components/ui/button";
import { useParams } from "next/navigation";
import { getReview, getCompanyReviews } from "@/server/actions/reviews";

export default function ReviewCompletePage() {
  const { reviewId } = useParams();
  const [showFeedback, setShowFeedback] = useState(false);

  useEffect(() => {
    const checkReviewCount = async () => {
      const review = await getReview(reviewId as string);
      if (!review?.success || !review.data?.companyId) return;

      const result = await getCompanyReviews(review.data.companyId);
      if (result.success && result.data) {
        const completedCount = result.data.filter(
          (r) => r.status === "completed",
        ).length;
        console.log("Company review count:", {
          companyId: review.data.companyId,
          completedCount,
          willShowFeedback: completedCount === 1,
        });
        if (completedCount === 1) {
          setShowFeedback(true);
        }
      }
    };

    checkReviewCount();
  }, [reviewId]);

  return (
    <div className="mx-auto max-w-2xl space-y-8 px-4">
      <Card className="p-6">
        <div className="flex flex-col items-center space-y-4 text-center">
          <div className="rounded-full bg-green-100 p-3">
            <CheckCircle2 className="h-8 w-8 text-green-600" />
          </div>
          <h1 className="text-2xl font-bold">Review Completed!</h1>
          <p className="text-muted-foreground">
            Thank you for completing the review. Your responses have been saved.
          </p>
          <Link href={`/reviews/${reviewId}/feedback`}>
            <Button>View Feedback</Button>
          </Link>
        </div>
      </Card>

      <FeedbackDialog
        open={showFeedback}
        onOpenChange={setShowFeedback}
        type="post_review"
      />
    </div>
  );
}
