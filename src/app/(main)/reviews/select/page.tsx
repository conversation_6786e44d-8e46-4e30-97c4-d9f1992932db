import HeaderBox from "@/components/HeaderBox";
import { <PERSON><PERSON> } from "@/components/ui/button";
import React from "react";
import { UserPlusIcon } from "lucide-react";
import { Card } from "@/components/ui/card";

import Link from "next/link";

import Image from "next/image";
import AssessmentCard from "@/components/AssessmentCard";
import FAQCard from "@/components/FAQCard";
import { faqItems } from "@/constants/index";
import InviteTeamDialog from "@/components/InviteTeamDialog";

const SelectAssessment = () => {
  return (
    <div className="w-full space-y-8">
      <div className="mb-8 flex w-full items-start justify-between">
        <HeaderBox
          type="title"
          title="Start a Review"
          subtext="Select a review type to begin"
        />
        {/* <InviteTeamDialog /> */}
      </div>
      <div className="mx-auto w-full max-w-7xl">
        <div className="grid gap-6 lg:grid-cols-3">
          <Card className="p-6 shadow-xl lg:col-span-2">
            <div className="mb-12 mt-0">
              <Image
                src="/assets/no-data.png"
                alt="Assessment Type"
                width={100}
                height={100}
                className="mx-auto"
              />
              <h3 className="custom-navyblue-text text-center text-xl font-semibold">
                Select Review Type
              </h3>
              <p className="custom-navyblue-text text-center text-sm">
                Please select the type of review you would like to proceed with
              </p>
            </div>

            <div className="grid gap-6 md:grid-cols-2">
              <AssessmentCard
                title="Basic Review"
                description="This review can be completed within a few hours."
                complexity="Low"
                suitability="SMEs, Quick sampling"
                link="/reviews/new"
                disabled={false}
              />

              <AssessmentCard
                title="Comprehensive Review"
                description="This review can be completed within 1-2 business days."
                complexity="High"
                suitability="All business types, HR &amp; Ops leads "
                link="/reviews/new"
                disabled={true}
              />
            </div>

            <div className="mt-6 text-center text-sm">
              <Link href="/dashboard/home">
                <Button
                  variant="link"
                  className="custom-text-gray hover:underline"
                >
                  Do this later
                </Button>
              </Link>
            </div>
          </Card>

          <FAQCard
            title="F.A.Q"
            description="Frequently asked questions about the assessment process"
            faqs={faqItems}
          />
        </div>
      </div>
    </div>
  );
};

export default SelectAssessment;
