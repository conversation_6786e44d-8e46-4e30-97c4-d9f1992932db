import { currentUser } from "@clerk/nextjs/server";
import Header<PERSON>ox from "@/components/HeaderBox";
import { getUserCompany } from "@/server/actions/companies";
import { getCompanyReviews } from "@/server/actions/reviews";
import { getTaskStats } from "@/server/actions/tasks";
import ReviewCard from "@/components/ReviewCard";
import { type Review } from "@/types";
import { Suspense } from "react";

async function getReviewWithStats(companyId: string, review: any) {
  const statsResult = await getTaskStats(companyId, review.id);
  return {
    review: {
      ...review,
      companyId,
      updatedAt: review.updatedAt ?? review.createdAt,
      createdAt: review.createdAt,
      type: review.type,
      status: review.status,
      progress: review.progress,
    },
    taskStats: statsResult.success ? statsResult.data : null,
  };
}

export default async function ReviewsPage() {
  const user = await currentUser();
  if (!user) return null;

  // Get user's company
  const company = await getUserCompany(user.id);
  if (!company.success || !company.data) return null;

  const companyData = company.data;

  // Get all reviews for the company
  const reviewsResult = await getCompanyReviews(companyData.id);
  const reviews =
    reviewsResult.success && reviewsResult.data ? reviewsResult.data : [];

  // Get task stats for each review
  const reviewsWithStats = await Promise.all(
    reviews.map((review) => getReviewWithStats(companyData.id, review)),
  );

  return (
    <div className="w-full space-y-8">
      <div className="mb-8 flex w-full items-start justify-between">
        <HeaderBox
          type="title"
          title="Reviews"
          subtext="View and manage your organization's reviews"
        />
      </div>

      <Suspense
        fallback={
          <div className="animate-pulse space-y-3">
            {[...Array(3)].map((_, i) => (
              <div key={i} className="h-24 rounded bg-gray-200" />
            ))}
          </div>
        }
      >
        <div className="space-y-4">
          {reviews.length === 0 ? (
            <div className="flex w-full items-center justify-center rounded-lg border bg-card p-8">
              <p className="text-center text-muted-foreground">
                No reviews found. Start by creating a new review.
              </p>
            </div>
          ) : (
            reviewsWithStats.map(({ review, taskStats }) => (
              <ReviewCard
                key={review.id}
                review={review}
                taskStats={taskStats || undefined}
              />
            ))
          )}
        </div>
      </Suspense>
    </div>
  );
}
