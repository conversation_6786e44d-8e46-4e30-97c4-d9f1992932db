import { currentUser } from "@clerk/nextjs/server";
import HeaderBox from "@/components/HeaderBox";
import { Button } from "@/components/ui/button";
import React from "react";
import StatsCard from "@/components/StatsCard";
import { UserPlusIcon } from "lucide-react";
import LatestAssessmentEmpty from "@/components/LatestAssessmentEmpty";
import RemediationEmpty from "@/components/RemediationEmpty";
import CommentCardEmpty from "@/components/CommentCardEmpty";
import InviteTeamDialog from "@/components/InviteTeamDialog";

const Dashboard = async () => {
  const user = await currentUser();

  // Add default stats for empty state
  const emptyStats = {
    total: 0,
    drafts: 0,
    completed: 0,
    lastReview: "No reviews yet",
  };

  return (
    <div className="w-full space-y-8">
      <div className="mb-8 flex w-full items-start justify-between">
        <HeaderBox
          type="greeting"
          title="Welcome"
          subtext="Build an inclusive workplace and culture"
          user={user?.firstName ?? ""}
        />
        {/* <InviteTeamDialog /> */}
      </div>
      <div className="grid gap-4 md:grid-cols-2 md:gap-8 lg:grid-cols-4">
        <StatsCard stats={emptyStats} />
      </div>
      <div className="grid gap-4 md:grid-cols-2 md:gap-8 lg:grid-cols-2">
        <LatestAssessmentEmpty />
        <RemediationEmpty />
      </div>
      <div className="grid gap-4 md:grid-cols-1 md:gap-8 lg:grid-cols-1">
        <CommentCardEmpty />
      </div>
    </div>
  );
};

export default Dashboard;
