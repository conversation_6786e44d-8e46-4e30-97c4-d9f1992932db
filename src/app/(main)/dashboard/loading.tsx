export default function DashboardLoading() {
  return (
    <div className="animate-pulse space-y-8">
      <div className="mb-6 h-8 w-64 rounded bg-gray-200" />
      <div className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-3">
        {[...Array(3)].map((_, i) => (
          <div key={i} className="h-32 rounded bg-gray-200" />
        ))}
      </div>

      {/* Two-column grid */}
      <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
        {[...Array(2)].map((_, i) => (
          <div key={i} className="h-96 rounded bg-gray-200" />
        ))}
      </div>

      {/* Single long column */}
      <div className="h-48 rounded bg-gray-200" />
    </div>
  );
}
