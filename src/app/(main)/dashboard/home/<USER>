import HeaderBox from "@/components/HeaderBox";
import React from "react";
import StatsCard from "@/components/StatsCard";
import LatestAssessment from "@/components/LatestAssessment";
import Remediation from "@/components/Remediation";
import CommentCard from "@/components/CommentCard";
import { Button } from "@/components/ui/button";
import Link from "next/link";
import { currentUser } from "@clerk/nextjs/server";
import { redirect } from "next/navigation";
import { getUserCompany } from "@/server/actions/companies";
import { getDashboardStats } from "@/server/actions/dashboard";
import {
  getLatestReview,
  getReviewComments,
  getReviewTasksAndRecommendations,
} from "@/server/actions/reviews";
import { type ReviewWithProgress, type Task } from "@/server/drizzle/schema";
import { type ReviewComment } from "@/types";
import CreateReviewButton from "@/components/CreateReviewButton";

// Add type for recommendation
type Recommendation = {
  id: string;
  text: string;
  priority: string;
  category: string | null;
  questionId: string;
};

export default async function HomePage() {
  const user = await currentUser();
  if (!user) {
    redirect("/sign-in");
  }

  const company = await getUserCompany(user.id);
  if (!company.success || !company.data) {
    redirect("/onboarding");
  }

  // Fetch all data in parallel
  const [statsResult, latestReviewResult] = await Promise.all([
    getDashboardStats(company.data.id),
    getLatestReview(company.data.id),
  ]);

  const stats = statsResult.success ? statsResult.data : null;
  const latestReview =
    latestReviewResult.success && latestReviewResult.data
      ? {
          ...latestReviewResult.data,
          progressData: latestReviewResult.data.progressData?.map((item) => ({
            ...item,
            title: item.title ?? "Untitled",
            value: item.value ?? 0,
            total: item.total ?? 0,
          })),
        }
      : null;

  // Get remediation tasks only for latest review
  const remediationTasksResult = latestReview
    ? await getReviewTasksAndRecommendations(latestReview.id)
    : { success: true, data: null };

  // Transform the data to match RemediationData format
  const tasks =
    remediationTasksResult.success && remediationTasksResult.data
      ? {
          tasks: remediationTasksResult.data.recommendations.map(
            (rec: Recommendation) => ({
              id: rec.id,
              title: rec.text,
              status: "To Do" as const,
              category: rec.category || "Uncategorized",
              reviewId: latestReview!.id,
              createdAt: new Date(),
              updatedAt: null,
              companyId: (company.data as { id: string }).id,
              description: null,
              priority: rec.priority as "high" | "medium" | "low",
              recommendationId: rec.id,
              assignedToUserId: null,
              startDate: new Date(),
              dueDate: null,
              questionId: rec.questionId,
              reviewAnswerId: null,
            }),
          ),
          pieChartData: Object.entries(
            remediationTasksResult.data.recommendations.reduce(
              (acc: Record<string, number>, rec: Recommendation) => {
                const category = rec.category || "Uncategorized";
                acc[category] = (acc[category] || 0) + 1;
                return acc;
              },
              {},
            ),
          ).map(([browser, visitors]) => ({
            browser,
            visitors,
          })),
          barChartData: Object.entries(
            remediationTasksResult.data.recommendations.reduce(
              (
                acc: Record<string, { value: number; total: number }>,
                rec: Recommendation,
              ) => {
                const category = rec.category || "Uncategorized";
                if (!acc[category]) {
                  acc[category] = { value: 0, total: 0 };
                }
                acc[category].total++;
                return acc;
              },
              {},
            ),
          ).map(([title, counts]) => ({
            title,
            value: counts.value,
            total: counts.total,
          })),
          totalTasks: remediationTasksResult.data.recommendations.length,
        }
      : null;

  // Format stats for StatsCard
  const reviewStats = {
    total: stats?.reviews.total ?? 0,
    drafts: stats?.reviews.drafts ?? 0,
    completed: stats?.reviews.completed ?? 0,
    lastReview: stats?.reviews.lastReviewDate
      ? stats.reviews.lastReviewDate.toLocaleDateString("en-GB", {
          day: "2-digit",
          month: "2-digit",
          year: "numeric",
        })
      : "No reviews yet",
  };

  // Get comments for the latest review and ensure proper typing
  const comments = latestReview
    ? await getReviewComments(latestReview.id)
    : { success: true, data: [] };

  // Transform the comments data to match ReviewComment type explicitly
  const commentsList: ReviewComment[] =
    comments.success && comments.data && latestReview
      ? comments.data.map((comment) => ({
          id: comment.id,
          reviewId: latestReview.id,
          questionId: comment.questionNumber ?? "",
          username: String(comment.username),
          timestamp: new Date(comment.timestamp).toLocaleDateString("en-GB", {
            day: "2-digit",
            month: "2-digit",
            year: "numeric",
            hour: "2-digit",
            minute: "2-digit",
          }),
          content: comment.content,
          category: comment.category,
          questionNumber: comment.questionNumber,
        }))
      : [];

  // Transform the review data into the format expected by LatestAssessment
  const formattedProgressData =
    latestReview?.progressData?.map((item) => ({
      title: item.title,
      value: item.value,
      total: item.total,
    })) ?? [];

  return (
    <div className="w-full space-y-4 sm:space-y-8">
      <div className="mb-4 flex w-full flex-col gap-4 sm:mb-8 sm:flex-row sm:items-start sm:justify-between">
        <HeaderBox
          type="greeting"
          title="Welcome"
          subtext="Build an inclusive workplace and culture"
          user={user?.firstName ?? ""}
        />
        <CreateReviewButton />
      </div>
      <div className="grid grid-cols-2 gap-2 sm:grid-cols-4 sm:gap-4 md:gap-8">
        <StatsCard stats={reviewStats} />
      </div>
      <div className="grid gap-4 sm:grid-cols-2 sm:gap-8">
        <div className="h-[450px]">
          <div className="h-full overflow-y-auto overflow-x-hidden">
            <LatestAssessment
              review={latestReview}
              latestAssessmentData={formattedProgressData}
            />
          </div>
        </div>
        <div className="h-[450px]">
          <div className="h-full overflow-y-auto overflow-x-hidden">
            <Remediation
              tasks={tasks}
              latestReviewId={latestReview?.id ?? ""}
            />
          </div>
        </div>
      </div>
      <div className="mt-24 grid gap-4 sm:mt-12">
        <CommentCard comments={commentsList} />
      </div>
    </div>
  );
}
