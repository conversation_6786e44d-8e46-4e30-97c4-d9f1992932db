import { currentUser } from "@clerk/nextjs/server";
import HeaderBox from "@/components/HeaderBox";
import { getUserCompany } from "@/server/actions/companies";
import {
  getCompanyTasks,
  getOverallTaskStats,
  getTaskStats,
  type TaskWithDetails,
} from "@/server/actions/tasks";
import TasksTable from "@/components/TasksTable";
import TasksStats from "@/components/TasksStats";
import TaskFilters from "@/components/TaskFilters";
import { Suspense } from "react";
import { LoadingSpinner } from "@/components/LoadingSpinner";
import { type TaskStats } from "@/types/task";
import { getCompanyReviews } from "@/server/actions/reviews";
import ReviewCard from "@/components/ReviewCard";
import {
  ArrowLeft,
  ChevronLeft,
  ArrowRight,
  ClipboardList,
  ClipboardCheck,
} from "lucide-react";
import Link from "next/link";
import { Button } from "@/components/ui/button";

interface SearchParams {
  status?: "To Do" | "In Progress" | "Completed" | "Archived";
  category?: string;
  priority?: "high" | "medium" | "low";
  reviewId?: string;
}

export default async function TasksPage({
  searchParams,
}: {
  searchParams: SearchParams;
}) {
  const user = await currentUser();
  if (!user) return null;

  // Get user's company
  const company = await getUserCompany(user.id);
  if (!company.success || !company.data) return null;

  const companyData = company.data;

  // Get all reviews for the company and ensure it's always an array
  const reviewsResult = await getCompanyReviews(companyData.id);
  const reviews =
    reviewsResult.success && reviewsResult.data ? reviewsResult.data : [];

  // Get overall company task stats (without reviewId filter)
  const overallStatsResult = await getOverallTaskStats(companyData.id);
  const overallStats = overallStatsResult.success
    ? overallStatsResult.data
    : null;

  // If no review is selected, show overall stats and review cards
  if (!searchParams.reviewId) {
    // Get stats for each review
    const reviewsWithStats = await Promise.all(
      reviews.map(async (review) => {
        const stats = await getTaskStats(companyData.id, review.id);
        return {
          review,
          stats: stats.success ? stats.data : null,
        };
      }),
    );

    return (
      <div className="mx-auto w-full max-w-[100vw] space-y-6 overflow-hidden px-4 sm:space-y-8">
        <div className="mb-4 flex w-full items-start justify-between sm:mb-8">
          <HeaderBox
            type="title"
            title="Tasks Overview"
            subtext="View and manage tasks across all reviews"
          />
        </div>

        <Suspense fallback={<LoadingSpinner />}>
          <TasksStats stats={overallStats ?? null} />
        </Suspense>

        <div className="space-y-4">
          <div className="flex flex-col space-y-1">
            <h2 className="text-base font-semibold sm:text-lg">
              Select a Review
            </h2>
            <p className="text-sm text-muted-foreground">
              Choose a review to view and manage its specific tasks
            </p>
          </div>

          <div className="grid gap-4">
            {reviewsWithStats.map(({ review, stats }) => (
              <ReviewCard
                key={review.id}
                review={{
                  ...review,
                  companyId: companyData.id,
                  updatedAt: review.createdAt,
                }}
                taskStats={stats || undefined}
              />
            ))}
          </div>
        </div>
      </div>
    );
  }

  // Clean and validate search params
  const cleanedSearchParams = Object.entries(searchParams).reduce(
    (acc, [key, value]) => {
      if (value && value !== "all") {
        // Only include non-empty and non-"all" values
        acc[key] = value;
      }
      return acc;
    },
    {} as Record<string, string>,
  ) as SearchParams;

  // Use Promise.all to parallelize data fetching
  const [tasksResult, reviewStatsResult] = await Promise.all([
    getCompanyTasks(companyData.id, cleanedSearchParams),
    getTaskStats(companyData.id, searchParams.reviewId),
  ]);

  const tasks = tasksResult.success && tasksResult.data ? tasksResult.data : [];
  const reviewStats = reviewStatsResult.success ? reviewStatsResult.data : null;

  const selectedReview = reviews.find(
    (review) => review.id === searchParams.reviewId,
  );

  return (
    <div className="mx-auto w-full max-w-[100vw] space-y-6 overflow-hidden px-4 sm:space-y-8">
      <div className="mb-4 flex w-full items-start sm:mb-8">
        <div className="flex items-center gap-2 sm:gap-4">
          <Link href="/tasks">
            <Button
              variant="ghost"
              size="icon"
              className="h-8 w-8 shrink-0 sm:h-9 sm:w-9"
            >
              <ChevronLeft className="h-5 w-5 sm:h-6 sm:w-6" />
            </Button>
          </Link>
          <div className="min-w-0 flex-1">
            <HeaderBox
              type="title"
              title={`Tasks - ${selectedReview?.title || "Review"}`}
              subtext="Manage and track your organization's tasks"
            />
          </div>
        </div>
      </div>

      <Suspense fallback={<LoadingSpinner />}>
        <TasksStats stats={reviewStats ?? null} />
      </Suspense>

      <div className="space-y-2">
        <div className="flex flex-col space-y-1">
          <h2 className="text-base font-semibold sm:text-lg">Task List</h2>
          <p className="text-xs text-muted-foreground sm:text-sm">
            Manage and track tasks for this review
          </p>
        </div>

        <TaskFilters />

        <div className="overflow-hidden rounded-lg border bg-card">
          <div className="p-3 sm:p-6">
            <Suspense
              fallback={
                <TasksTable
                  tasks={[]}
                  currentUserId={user.id}
                  companyId={companyData.id}
                  isLoading
                />
              }
            >
              {tasks.length === 0 ? (
                <div className="flex flex-col items-center justify-center space-y-3 py-8 sm:space-y-4 sm:py-12">
                  <div className="rounded-full bg-primary/10 p-2 sm:p-3">
                    <ClipboardCheck className="h-5 w-5 text-primary sm:h-6 sm:w-6" />
                  </div>
                  <div className="text-center">
                    <h3 className="text-base font-semibold sm:text-lg">
                      No Tasks Yet
                    </h3>
                    <p className="text-xs text-muted-foreground sm:text-sm">
                      Continue with your review to generate tasks
                    </p>
                  </div>
                  <Link href={`/reviews/${searchParams.reviewId}/review`}>
                    <Button className="mt-1 text-xs sm:mt-2 sm:text-sm">
                      Continue Review
                      <ArrowRight className="ml-1 h-3 w-3 sm:ml-2 sm:h-4 sm:w-4" />
                    </Button>
                  </Link>
                </div>
              ) : (
                <TasksTable
                  tasks={tasks}
                  currentUserId={user.id}
                  companyId={companyData.id}
                />
              )}
            </Suspense>
          </div>
        </div>
      </div>
    </div>
  );
}
