export default function TasksLoading() {
  return (
    <div className="animate-pulse">
      <div className="mb-8 grid grid-cols-4 gap-4">
        {[...Array(4)].map((_, i) => (
          <div key={`top-${i}`} className="h-24 rounded bg-gray-200" />
        ))}
      </div>
      <div className="mb-4 h-8 w-48 rounded bg-gray-200" />
      <div className="space-y-3">
        {[...Array(5)].map((_, i) => (
          <div key={i} className="h-24 rounded bg-gray-200" />
        ))}
      </div>
    </div>
  );
}
