import { currentUser } from "@clerk/nextjs/server";
import { redirect } from "next/navigation";
import { acceptInvitation } from "@/server/actions/invitations";

export default async function AcceptInvitePage({
  params,
}: {
  params: { token: string };
}) {
  const user = await currentUser();
  if (!user) {
    // Store the token in the URL when redirecting to sign in
    redirect(`/sign-in?redirect=/invite/accept/${params.token}`);
  }

  // Accept the invitation
  const result = await acceptInvitation(params.token, user.id);
  if (!result.success || !result.data) {
    // Handle error - redirect to error page or show message
    redirect("/invite/error");
  }

  // Redirect to the review page on success
  redirect(`/reviews/${result.data.reviewId}`);
}
