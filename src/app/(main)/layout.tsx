export const dynamic = "force-dynamic";

import Footer from "@/components/Footer";
import NavBar from "@/components/NavBar";
import SideBar from "@/components/SideBar";
import AccessibilityWidget from "@/components/AccessibilityWidget";
import { auth, currentUser } from "@clerk/nextjs/server";
import { User } from "../../../types";
import { redirect } from "next/navigation";
import { getUserCompany } from "@/server/actions/companies";
import { type CompanyData } from "@/types/index";

export default async function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  try {
    const { userId } = auth();
    if (!userId) {
      redirect("/sign-in");
    }

    const user = await currentUser();
    if (!user) {
      redirect("/sign-in");
    }

    const company = await getUserCompany(user.id);
    if (!company.success || !company.data) return null;

    const userProps = {
      $id: user.id,
      firstName: user.firstName ?? "",
      lastName: user.lastName ?? "",
      email: user.emailAddresses[0]?.emailAddress ?? "",
      UserId: userId,
    };

    // Ensure company data has all required properties
    const companyData: CompanyData = {
      id: company.data.id,
      name: company.data.companyName,
      domain: company.data.companyDomain,
      teamSize: company.data.teamSize,
      industry: company.data.industry,
      locationCount: company.data.locationCount,
      headquarterCity: company.data.headquarterCity,
      headquarterCountry: company.data.headquarterCountry,
      createdAt: company.data.createdAt,
      updatedAt: company.data.updatedAt,
    };

    // Center content on the page for larger screens
    const centerContent =
      "lg:auto-rows-max lg:mx-auto lg:w-[1120px] lg:max-w-[calc(100vw-280px)]";

    return (
      <div className="grid min-h-screen w-full">
        <SideBar user={userProps} company={companyData} />
        <div className="ml-0 flex min-h-screen flex-col lg:ml-[280px]">
          <div className="sticky top-0 z-50 w-full border-b bg-white">
            <div className={centerContent}>
              <NavBar />
            </div>
          </div>
          <main className="flex-grow">
            <div className={centerContent}>
              <div className="w-full p-4 md:p-8">{children}</div>
            </div>
          </main>
          <div className="sticky bottom-0 z-50 w-full border-t bg-white">
            <div className={centerContent}>
              <Footer />
            </div>
          </div>
        </div>
        <AccessibilityWidget />
      </div>
    );
  } catch (error) {
    console.error("Authentication error:", error);
    redirect("/sign-in");
  }
}
