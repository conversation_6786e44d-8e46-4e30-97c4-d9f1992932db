import { currentUser } from "@clerk/nextjs/server";
import { redirect } from "next/navigation";
import { getUserCompany } from "@/server/actions/companies";
import SettingsContent from "@/components/SettingsContent";
import { getCompanyDetails } from "@/server/actions/settings";

export default async function SettingsPage() {
  const user = await currentUser();
  if (!user) {
    redirect("/sign-in");
  }

  const company = await getUserCompany(user.id);
  if (!company.success || !company.data) {
    redirect("/onboarding");
  }

  const companyId = company.data.id;
  const companyName = company.data.companyName;
  const companyDomain = company.data.companyDomain;

  return (
    <SettingsContent
      companyId={companyId}
      companyName={companyName}
      companyDomain={companyDomain}
    />
  );
}
