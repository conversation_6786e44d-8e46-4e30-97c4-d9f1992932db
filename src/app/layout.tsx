import type { Metada<PERSON> } from "next";
import { Inter } from "next/font/google";
import "./globals.css";
import { cn } from "@/lib/utils";
import { Clerk<PERSON>rovider } from "@clerk/nextjs";
import { Toaster } from "@/components/ui/toaster";

const inter = Inter({ subsets: ["latin"] });

export const metadata: Metadata = {
  title: {
    template: "%s | W<PERSON>ri App",
    default: "W<PERSON><PERSON>",
  },
  description: "Make your workplace and culture inclusive",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <ClerkProvider
      appearance={{
        layout: {
          termsPageUrl: "https://clerk.com/terms",
          helpPageUrl: "https://clerk.com/help",
          logoImageUrl: "/assets/wakari-logo.png",
          logoLinkUrl: "/dashboard/home",
          logoPlacement: "inside",
          privacyPageUrl: "https://clerk.com/privacy",
        },
        variables: {
          fontSize: "14px",
          colorText: "#0a2245",
        },
      }}
    >
      <html lang="en" className="h-full">
        {/* add cn to body tag */}
        <body className={cn("relative h-full antialiased", inter.className)}>
          {/* add main tag with className to body tag */}
          <main className="relative flex min-h-screen flex-col">
            <div className="flex-1">{children}</div>
          </main>
          <Toaster />
        </body>
      </html>
    </ClerkProvider>
  );
}
