"use server";

import { db } from "@/server/db";
import { users } from "@/server/drizzle/schema";
import { eq } from "drizzle-orm";
import { currentUser } from "@clerk/nextjs/server";
import { redirect } from "next/navigation";

export async function requireAdmin() {
  const user = await currentUser();

  if (!user) {
    redirect("/sign-in");
  }

  const dbUser = await db.query.users.findFirst({
    where: eq(users.clerkUserId, user.id),
  });

  if (!dbUser?.isSuperAdmin) {
    redirect("/dashboard/home");
  }

  return user;
}

export async function checkUserIsAdmin(userId: string): Promise<boolean> {
  const dbUser = await db.query.users.findFirst({
    where: eq(users.clerkUserId, userId),
  });
  return dbUser?.isSuperAdmin ?? false;
}
