import React from "react";

import Image from "next/image";
import Link from "next/link";

import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";

import { Label } from "@/components/ui/label";

import { SignUp } from "@clerk/nextjs";

const SignUpPage = () => {
  return (
    <div className="w-full lg:grid lg:min-h-screen lg:grid-cols-2 xl:min-h-screen">
      <div className="m-6 rounded-lg lg:block">
        <Image
          src="https://images.pexels.com/photos/8091204/pexels-photo-8091204.jpeg"
          alt="Image"
          width="1920"
          height="1080"
          className="h-full w-full rounded-lg object-cover dark:brightness-[0.2] dark:grayscale"
        />
      </div>
      <div className="flex items-center justify-center py-12">
        <div className="mx-auto grid w-[350px] gap-6">
          {/* <div className="grid gap-2 text-center">
            <div className="flex items-center justify-center py-5">
              <Image
                className="relative dark:drop-shadow-[0_0_0.3rem_#ffffff70] dark:invert"
                src="https://www.wearewakari.com/wp-content/uploads/2024/03/cropped-wakari-logo.png"
                alt="Wakari Logo"
                width={120}
                height={120}
                priority
              />
            </div>
            <h1 className="text-3xl font-bold">Create an account</h1>
            <p className="text-balance">
              Make your workplace and culture inclusive
            </p>
          </div> */}
          <div className="grid gap-4">
            {/* <div className="grid grid-cols-2 gap-4">
              <div className="grid gap-2">
                <Label htmlFor="first-name">First name</Label>
                <Input id="first-name" placeholder="Max" required />
              </div>
              <div className="grid gap-2">
                <Label htmlFor="last-name">Last name</Label>
                <Input id="last-name" placeholder="Robinson" required />
              </div>
            </div> */}
            {/* <div className="grid gap-2">
              <Label htmlFor="email">Email</Label>
              <Input
                id="email"
                type="email"
                placeholder="<EMAIL>"
                required
              />
            </div> */}
            {/* <div className="grid gap-2">
              <div className="flex items-center">
                <Label htmlFor="password">Password</Label>
                <Link
                  href="/forgot-password"
                  className="ml-auto inline-block text-sm underline"
                >
                  Forgot your password?
                </Link>
              </div>
              <Input id="password" type="password" required />
            </div> */}
            {/* <Link href="/onboarding"> */}
            <div className="w-full">
              <Button asChild>
                <SignUp />
              </Button>
            </div>
            {/* </Link> */}
          </div>
          {/* <div className="mt-4 text-center text-sm">
            Already have an account?
            <Link href="/sign-in" className="underline">
              Sign in
            </Link>
          </div> */}
        </div>
      </div>
    </div>
  );
};

export default SignUpPage;
