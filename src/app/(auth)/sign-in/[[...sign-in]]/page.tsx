import React from "react";

import Image from "next/image";
import Link from "next/link";

import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";

import { Label } from "@/components/ui/label";

import { SignIn } from "@clerk/nextjs";

const SignInPage = () => {
  return (
    <div className="w-full lg:grid lg:min-h-screen lg:grid-cols-2 xl:min-h-screen">
      <div className="m-6 rounded-lg lg:block">
        <Image
          src="https://images.pexels.com/photos/8091204/pexels-photo-8091204.jpeg"
          alt="Image"
          width="1920"
          height="1080"
          className="h-full w-full rounded-lg object-cover dark:brightness-[0.2] dark:grayscale"
        />
      </div>
      <div className="flex items-center justify-center py-12">
        <div className="mx-auto grid w-[350px] gap-6">
          {/* <div className="grid gap-2 text-center">
            <div className="flex items-center justify-center py-5">
              <Image
                className="relative dark:drop-shadow-[0_0_0.3rem_#ffffff70] dark:invert"
                src="https://www.wearewakari.com/wp-content/uploads/2024/03/cropped-wakari-logo.png"
                alt="Wakari Logo"
                width={120}
                height={120}
                priority
              />
            </div>
            <h1 className="text-3xl font-bold">Sign in to continue</h1>
            <p className="text-balance">
              Make your workplace and culture inclusive
            </p>
          </div> */}
          <div className="grid gap-4">
            {/* <div className="grid gap-2">
              <Label htmlFor="email">Email</Label>
              <Input
                id="email"
                type="email"
                placeholder="<EMAIL>"
                required
              />
            </div> */}
            {/* <div className="grid gap-2">
              <div className="flex items-center">
                <Label htmlFor="password">Password</Label>
                <Link
                  href="/forgot-password"
                  className="ml-auto inline-block text-sm underline"
                >
                  Forgot your password?
                </Link>
              </div>
              <Input id="password" type="password" required />
            </div> */}
            {/* <Link href="/dashboard/home"> */}
            <div className="w-full">
              <Button asChild>
                <SignIn />
              </Button>
            </div>
            {/* </Link> */}
          </div>
          {/* <div className="mt-4 text-center text-sm">
            Don&apos;t have an account?
            <Link href="/sign-up" className="underline">
              Sign up
            </Link>
          </div> */}
        </div>
      </div>
    </div>
  );
};

export default SignInPage;
