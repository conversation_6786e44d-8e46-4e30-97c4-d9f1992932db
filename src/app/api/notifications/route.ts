import { NextRequest, NextResponse } from "next/server";
import { getNotifications } from "@/server/actions/comments";

export async function GET(request: NextRequest) {
  const userId = request.nextUrl.searchParams.get("userId");
  if (!userId) {
    return NextResponse.json(
      { success: false, error: "User ID is required" },
      { status: 400 },
    );
  }

  const result = await getNotifications(userId);
  return NextResponse.json(result);
}
