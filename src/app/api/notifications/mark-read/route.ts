import { NextRequest, NextResponse } from "next/server";
import { markNotificationAsRead } from "@/server/actions/comments";

export async function POST(request: NextRequest) {
  const { notificationId } = await request.json();
  if (!notificationId) {
    return NextResponse.json(
      { success: false, error: "Notification ID is required" },
      { status: 400 },
    );
  }

  const result = await markNotificationAsRead(notificationId);
  return NextResponse.json(result);
}
