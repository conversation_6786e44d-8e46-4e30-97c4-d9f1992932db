import { NextRequest, NextResponse } from "next/server";
import { generateReportPDF } from "@/lib/pdf";
import { getReport } from "@/server/actions/reports";
import { auth } from "@clerk/nextjs/server";
import { type ReportData } from "@/types";

export async function GET(
  request: NextRequest,
  { params }: { params: { reviewId: string } },
) {
  try {
    const { userId } = auth();
    if (!userId) {
      return new NextResponse("Unauthorized", { status: 401 });
    }

    const reportResult = await getReport(params.reviewId);
    if (!reportResult.success || !reportResult.data) {
      return new NextResponse("Report not found", { status: 404 });
    }

    const reportData: ReportData = {
      ...reportResult.data,
      id: params.reviewId,
      recommendations: [], // Add missing property with default empty array
      ragScores: {
        red: 0,
        amber: 0,
        green: 0,
        total: 0,
      },
      categoryScores: {},
    };

    const pdfBuffer = await generateReportPDF(reportData);

    return new NextResponse(pdfBuffer, {
      headers: {
        "Content-Type": "application/pdf",
        "Content-Disposition": `attachment; filename="report-${params.reviewId}.pdf"`,
      },
    });
  } catch (error) {
    console.error("Error generating PDF:", error);
    return new NextResponse("Error generating PDF", { status: 500 });
  }
}
