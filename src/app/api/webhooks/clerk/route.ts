export const dynamic = "force-dynamic";
import { Webhook } from "svix";
import { headers } from "next/headers";
import { WebhookEvent } from "@clerk/nextjs/server";
import { env } from "@/server/env/server";
import { createNewUser, deleteUser } from "@/server/actions/users";

export async function POST(req: Request) {
  // You can find this in the Clerk Dashboard -> Webhooks -> choose the endpoint
  //   const WEBHOOK_SECRET = process.env.WEBHOOK_SECRET;

  if (!env.CLERK_WEBHOOK_SECRET) {
    throw new Error(
      "Please add WEBHOOK_SECRET from Clerk Dashboard to .env or .env.local",
    );
  }

  // Get the headers
  const headerPayload = headers();
  const svix_id = headerPayload.get("svix-id");
  const svix_timestamp = headerPayload.get("svix-timestamp");
  const svix_signature = headerPayload.get("svix-signature");

  // If there are no headers, error out
  if (!svix_id || !svix_timestamp || !svix_signature) {
    return new Response("Error occured -- no svix headers", {
      status: 400,
    });
  }

  // Get the body
  const payload = await req.json();
  const body = JSON.stringify(payload);

  // Create a new Svix instance with your secret.
  const wh = new Webhook(env.CLERK_WEBHOOK_SECRET);

  let event: WebhookEvent;

  // Verify the payload with the headers
  try {
    event = wh.verify(body, {
      "svix-id": svix_id,
      "svix-timestamp": svix_timestamp,
      "svix-signature": svix_signature,
    }) as WebhookEvent;
  } catch (err) {
    console.error("Error verifying webhook:", err);
    return new Response("Error occured", {
      status: 400,
    });
  }

  // Do something with the payload
  // For this guide, you simply log the payload to the console
  const { id } = event.data;
  const eventType = event.type;
  //   console.log(`Webhook with and ID of ${id} and type of ${eventType}`);
  //   console.log("Webhook body:", body);

  switch (event.type) {
    case "user.created":
      // User created
      await createNewUser({
        data: {
          firstName: event.data.first_name ?? "",
          lastName: event.data.last_name ?? "",
          email: event.data.email_addresses[0].email_address,
          clerkUserId: event.data.id ?? "",
        },
      });
      break;
    case "user.deleted": {
      if (event.data.id != null) {
        await deleteUser(event.data.id);
      }
      break;
    }
  }

  return new Response("", { status: 200 });
}
