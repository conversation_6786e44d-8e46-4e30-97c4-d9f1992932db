import OnboardingCard from "@/components/OnboardingCard";
import { auth } from "@clerk/nextjs/server";
import { redirect } from "next/navigation";
import { checkUserOnboardingStatus } from "@/server/actions/onboarding-check";

export default async function Onboarding() {
  const { userId } = auth();
  if (!userId) redirect("/sign-in");

  // Check user's onboarding status
  const status = await checkUserOnboardingStatus(userId);

  // If user is already a member (not admin), redirect to dashboard
  if (status.isMember) {
    redirect("/dashboard/home");
  }

  // Otherwise show onboarding (for admins or new users)
  return (
    <main className="container mx-auto px-4 py-4 sm:py-8">
      <OnboardingCard />
    </main>
  );
}
