import {
  <PERSON>Help,
  CircleCheck,
  CircleX,
  CircleAlert,
  Ban,
} from "lucide-react";
import { ChartConfig } from "@/components/ui/chart";
import { Building, Headphones, Heart, Hammer } from "lucide-react";
import {
  AnswerButton,
  Assessment,
  Question,
  Step,
  TaskData,
} from "../../types";

// First, define the option type
interface OptionType {
  value: string;
  label: string;
  country?: string;
}

// Add this interface at the top with your other interfaces
interface SidebarLink {
  icon: string;
  route: string;
  label: string;
  subLinks?: Array<{
    route: string;
    label: string;
  }>;
}

// Export the arrays with proper typing
export const industries: OptionType[] = [
  { value: "technology", label: "Technology & Software" },
  { value: "financial", label: "Financial Services" },
  { value: "healthcare", label: "Healthcare & Medical" },
  { value: "education", label: "Education" },
  { value: "retail", label: "Retail & E-commerce" },
  { value: "manufacturing", label: "Manufacturing" },
  { value: "professional_services", label: "Professional Services" },
  { value: "hospitality", label: "Hospitality & Tourism" },
  { value: "construction", label: "Construction & Real Estate" },
  { value: "media", label: "Media & Entertainment" },
  { value: "transportation", label: "Transportation & Logistics" },
  { value: "energy", label: "Energy & Utilities" },
  { value: "agriculture", label: "Agriculture" },
  { value: "nonprofit", label: "Non-Profit & NGO" },
  { value: "government", label: "Government" },
];

export const roles: OptionType[] = [
  { value: "executive", label: "Executive/C-Level" },
  { value: "director", label: "Director" },
  { value: "manager", label: "Manager" },
  { value: "team_lead", label: "Team Lead" },
  { value: "developer", label: "Developer" },
  { value: "designer", label: "Designer" },
  { value: "product_manager", label: "Product Manager" },
  { value: "project_manager", label: "Project Manager" },
  { value: "analyst", label: "Analyst" },
  { value: "consultant", label: "Consultant" },
  { value: "hr", label: "HR Professional" },
  { value: "marketing", label: "Marketing Professional" },
  { value: "sales", label: "Sales Professional" },
  { value: "support", label: "Support Professional" },
  { value: "other", label: "Other" },
];

export const teamSize: OptionType[] = [
  { value: "1-10", label: "1-10 employees" },
  { value: "11-50", label: "11-50 employees" },
  { value: "50+", label: "50+ employees" },
];

export const locationCount: OptionType[] = [
  { value: "1-5", label: "1-5 locations" },
  { value: "11-50", label: "21-50 locations" },
  { value: "50+", label: "50+ locations" },
];

// Top global cities
export const cities: OptionType[] = [
  // Africa
  { value: "johannesburg", label: "Johannesburg", country: "ZA" },
  { value: "cape_town", label: "Cape Town", country: "ZA" },
  { value: "durban", label: "Durban", country: "ZA" },
  { value: "pretoria", label: "Pretoria", country: "ZA" },
  { value: "lagos", label: "Lagos", country: "NG" },
  { value: "cairo", label: "Cairo", country: "EG" },
  { value: "nairobi", label: "Nairobi", country: "KE" },

  // Europe
  { value: "london", label: "London", country: "GB" },
  { value: "paris", label: "Paris", country: "FR" },
  { value: "berlin", label: "Berlin", country: "DE" },
  { value: "amsterdam", label: "Amsterdam", country: "NL" },

  // North America
  { value: "new_york", label: "New York", country: "US" },
  { value: "san_francisco", label: "San Francisco", country: "US" },
  { value: "toronto", label: "Toronto", country: "CA" },

  // Asia
  { value: "tokyo", label: "Tokyo", country: "JP" },
  { value: "singapore", label: "Singapore", country: "SG" },
  { value: "dubai", label: "Dubai", country: "AE" },
];

export const countries: OptionType[] = [
  // Europe
  { value: "GB", label: "United Kingdom" },
  { value: "DE", label: "Germany" },
  { value: "FR", label: "France" },
  { value: "NL", label: "Netherlands" },
  { value: "ES", label: "Spain" },

  // Africa
  { value: "ZA", label: "South Africa" },
  { value: "NG", label: "Nigeria" },
  { value: "KE", label: "Kenya" },
  { value: "EG", label: "Egypt" },
  { value: "GH", label: "Ghana" },

  // North America
  { value: "US", label: "United States" },
  { value: "CA", label: "Canada" },
  { value: "MX", label: "Mexico" },

  // Asia
  { value: "JP", label: "Japan" },
  { value: "SG", label: "Singapore" },
  { value: "AE", label: "United Arab Emirates" },
  { value: "IN", label: "India" },
  { value: "CN", label: "China" },

  // Oceania
  { value: "AU", label: "Australia" },
  { value: "NZ", label: "New Zealand" },
];

export const steps: Step[] = [
  {
    id: 1,
    img: "https://static.vecteezy.com/system/resources/previews/036/041/446/large_2x/confetti-icon-for-uiux-web-app-infographic-etc-vector.jpg",
    tagline: "Hi There",
    title: "Welcome to Wakari!",
    content: "Please wait while we check for your organization account",
    helptitle: "",
    helpcontent: "",
    selectOptions: [],
    inputlabel: "",
    descr: "",
    additionalinputlabel: "",
    additionalselectOptions: [],
    video: "",
  },
  {
    id: 2,
    img: "https://static.vecteezy.com/system/resources/previews/036/041/446/large_2x/confetti-icon-for-uiux-web-app-infographic-etc-vector.jpg",
    tagline: "One moment please...",
    title: "Setting up your organization account",
    content: "Please wait while we setup your organization account",
    helptitle: "",
    helpcontent: "",
    selectOptions: [],
    inputlabel: "",
    descr: "",
    additionalinputlabel: "",
    additionalselectOptions: [],
    video: "",
  },
  {
    id: 3,
    title: "Position",
    content: "What is your main role in the company?",
    inputlabel: "Select Role",
    selectOptions: roles,
    helpicon: CircleHelp,
    helptitle: "Why do we need this info?",
    helpcontent:
      "Every user may be allocated tasks based on their roles to ensure inclusivity remediations are resolved.",
    img: "",
    tagline: "",
    descr:
      "Select a role that closely matches what you do. You can always change it later if you are not sure",
    additionalinputlabel: "",
    additionalselectOptions: [],
    video: "",
  },
  {
    id: 4,
    title: "Employees",
    content: "How many employees does the company have?",
    inputlabel: "Select number of employees",
    selectOptions: teamSize,
    helpicon: CircleHelp,
    helptitle: "Why do we need this info?",
    helpcontent: "To ensure what is required to best assist your company.",
    img: "",
    tagline: "",
    descr: "",
    additionalinputlabel: "",
    additionalselectOptions: [],
    video: "",
  },
  {
    id: 5,
    title: "Industry",
    content: "In which industry does the company operate?",
    inputlabel: "Select an industry",
    selectOptions: industries,
    helpicon: CircleHelp,
    helptitle: "Why do we need this info?",
    helpcontent: "To ensure what is required to best assist your company.",
    img: "",
    tagline: "",
    descr:
      "Select an industry that closely matches what you do. You can always change it later if you are not sure.",
    additionalinputlabel: "",
    additionalselectOptions: [],
    video: "",
  },
  {
    id: 6,
    title: "Company Locations",
    content: "How many physical locations does the company have?",
    inputlabel: "Select number of physical locations",
    selectOptions: locationCount,
    helpicon: CircleHelp,
    helptitle: "Why do we need this info?",
    helpcontent: "To ensure what is required to best assist your company.",
    img: "",
    tagline: "",
    descr:
      "Each location can perform parts or an entire an independent assessment. This helps to separate reports and tasks.",
    additionalinputlabel: "",
    additionalselectOptions: [],
    video: "",
  },
  {
    id: 7,
    title: "Headquarters",
    content: "Please select your head office location",
    inputlabel: "City",
    selectOptions: cities,
    additionalinputlabel: "Country",
    additionalselectOptions: countries,
    helpicon: CircleHelp,
    helptitle: "Why do we need this info?",
    helpcontent: "To ensure what is required to best assist your company.",
    img: "",
    tagline: "",
    descr:
      "Each location can perform parts or an entire an independent assessment. This helps to separate reports and tasks.",
    video: "",
  },
  {
    id: 8,
    title: "Invite your team",
    content: "Invite team members to your workspace",
    inputlabel: "Role",
    selectOptions: roles,
    helpicon: CircleHelp,
    helptitle: "Why do we need this info?",
    helpcontent: "To ensure what is required to best assist your company.",
    img: "",
    tagline: "",
    descr:
      "Each location can perform parts or an entire an independent assessment. This helps to separate reports and tasks.",
    additionalinputlabel: "",
    additionalselectOptions: [],
    video: "",
  },
  {
    id: 9,
    title: "Great Work!",
    content:
      "You are ready to start your reviews. Here is a 30 second video to get you started.",
    inputlabel: "",
    selectOptions: [],
    helpicon: CircleHelp,
    helptitle: "",
    helpcontent: "",
    img: "https://static.vecteezy.com/system/resources/previews/036/041/446/large_2x/confetti-icon-for-uiux-web-app-infographic-etc-vector.jpg",
    tagline: "",
    descr: "Here is a 30 second video to get you started.",
    additionalinputlabel: "",
    additionalselectOptions: [],
    video: "https://www.youtube.com/embed/9rvBZnqYnGw?si=MNbrpDqFcRX9FJFu",
  },
];

export const SidebarLinks: SidebarLink[] = [
  {
    icon: "Home",
    route: "/dashboard/home",
    label: "Home",
  },
  {
    icon: "ListChecks",
    route: "/reviews/manage",
    label: "Reviews",
  },
  {
    icon: "ClipboardCheck",
    route: "/tasks",
    label: "Tasks",
    subLinks: [
      {
        route: "/all-tasks",
        label: "All",
      },
      {
        route: "/tasks-in-progress",
        label: "In Progress",
      },
      {
        route: "/tasks-completed",
        label: "Completed",
      },
      {
        route: "/tasks-archived",
        label: "Archived",
      },
    ],
  },
  {
    icon: "BarChart2",
    route: "/reports",
    label: "Reports",
  },
  {
    icon: "Users",
    route: "/team",
    label: "Team",
  },
  {
    icon: "Settings",
    route: "/settings",
    label: "Settings",
  },
  {
    icon: "HelpCircle",
    route: "/help",
    label: "Help",
  },
] as const;

export const StatsCardData = [
  {
    title: "Reviews Initiated",
    value: 0,
  },
  {
    title: "Reviews In Progress",
    value: 0,
  },
  {
    title: "Reviews Completed",
    value: 0,
  },
  {
    title: "Last Review",
    value: "20-09-2024",
  },
];

export const faqItems = [
  {
    question: "Is it accessible?",
    answer: "Yes. It adheres to the WAI-ARIA design pattern.",
  },
  {
    question: "Is it styled?",
    answer:
      "Yes. It comes with default styles that matches the other components' aesthetic.",
  },
  {
    question: "Is it animated?",
    answer:
      "Yes. It's animated by default, but you can disable it if you prefer.",
  },
];

export const defaultAnswers: AnswerButton[] = [
  { text: "Yes", value: "yes", icon: CircleCheck },
  { text: "No", value: "no", icon: CircleX },
  { text: "Partially", value: "partially", icon: CircleAlert },
  { text: "Not Applicable", value: "na", icon: Ban },
];

export const assessmentQuestions: Question[] = [
  {
    id: "1",
    text: "Does your building have Automatic doors?",
    answer: "",
    comment: "",
    description: "",
    questionGuide: {
      image: "/assets/1.png",
      description:
        "These are doors that open and close automatically, without the need for physical force. These doors are equipped with sensors that detect motion, pressure, or other cues, and are designed to open in a variety of ways,e.g. sliding, swinging, or folding and are ideal to support those who may have mobility or sight challenges.",
      otherOrganizations: [
        "/assets/sensory-space.png",
        "/assets/sensory-space.png",
        "/assets/sensory-space.png",
      ],
    },
    recommendations: [
      {
        id: "1",
        questionId: "1",
        text: "Install automatic doors at main entrances",
        priority: "high",
        effort: "4-6 weeks",
      },
      {
        id: "2",
        questionId: "1",
        text: "Add motion sensors to existing doors",
        priority: "medium",
        effort: "2-3 weeks",
      },
    ],
    category: "Building & Infrastructure",
  },
  {
    id: "2",
    text: "Does your building have Adjustable lighting?",
    answer: "",
    comment: "",
    description: "",
    questionGuide: {
      image: "/assets/2.png",
      description:
        "Also known as tunable lighting, these are lighting systems that can change intensity, color temperature, and direction to suit different needs and preferences. These lighting systems can mimic natural daylight, which helps regulate circadian rhythms, improve sleep quality, and even reduce eye strain.",
      otherOrganizations: [
        "/assets/sensory-space.png",
        "/assets/sensory-space.png",
        "/assets/sensory-space.png",
      ],
    },
    recommendations: [
      {
        id: "1",
        questionId: "2",
        text: "Install adjustable temperature systems",
        priority: "high",
        effort: "4-6 weeks",
      },
      {
        id: "2",
        questionId: "2",
        text: "Add motion sensors to existing temperature systems",
        priority: "medium",
        effort: "2-3 weeks",
      },
    ],
    category: "Building & Infrastructure",
  },
  {
    id: "3",
    text: "Does your building have Adjustable temperatures?",
    answer: "",
    comment: "",
    description: "",
    questionGuide: {
      image: "/assets/3.png",
      description:
        "This is usually a space with air conditioning that allows for people to manually change the settings. Having this at work allows for different ambiences to be set in different rooms so that people are working in spaces most conducive to their needs.",
      otherOrganizations: [
        "/assets/sensory-space.png",
        "/assets/sensory-space.png",
        "/assets/sensory-space.png",
      ],
    },
    recommendations: [
      {
        id: "1",
        questionId: "3",
        text: "Install adjustable temperature systems",
        priority: "high",
        effort: "4-6 weeks",
      },
    ],
    category: "Building & Infrastructure",
  },
  {
    id: "4",
    text: "Does your building have Step free access?",
    answer: "",
    comment: "",
    description: "",
    questionGuide: {
      image: "/assets/4.png",
      description:
        "Step free access refers to any mechanisms that allow people to navigate spaces without the need to use stairs or escalators and can include ramps, lifts, or level surfaces.",
      otherOrganizations: [
        "/assets/sensory-space.png",
        "/assets/sensory-space.png",
        "/assets/sensory-space.png",
      ],
    },
    recommendations: [
      {
        id: "1",
        questionId: "4",
        text: "Install step free access",
        priority: "high",
        effort: "4-6 weeks",
      },
    ],
    category: "Building & Infrastructure",
  },
  {
    id: "5",
    text: "Does your building have Highly visible signage?",
    answer: "",
    comment: "",
    description: "",
    questionGuide: {
      image: "/assets/5.png",
      description:
        "This is any signage that is printed or written with font and styles that are typically larger, brighter or that create easier ability to be seen, and that would be visible to peple with less than optimal or standard eye sight. e.g. use of 14 size font vs 12 on documents, use of large print guides, etc. These can also include adopting of Map Boards, wayfinders or alternative ways of accessing reading information other than text. ",
      otherOrganizations: [
        "/assets/sensory-space.png",
        "/assets/sensory-space.png",
        "/assets/sensory-space.png",
      ],
    },
    recommendations: [
      {
        id: "1",
        questionId: "5",
        text: "Install highly visible signage",
        priority: "high",
        effort: "4-6 weeks",
      },
    ],
    category: "Building & Infrastructure",
  },
  {
    id: "6",
    text: "Does your building have Hearing loops (with working batteries) or other sound transmitting devices e.g. rogers?",
    answer: "",
    comment: "",
    description: "",
    questionGuide: {
      image: "/assets/6.png",
      description:
        "These are one or more physical loops of cable which are placed around a designated area, usually a room or a building. The cable generates an electromagnetic field which can be picked up by hearing aids, cochlear implant (CI) processors, or specialized hand-held hearing loop receivers. Can also be called audio induction loops.",
      otherOrganizations: [
        "/assets/sensory-space.png",
        "/assets/sensory-space.png",
        "/assets/sensory-space.png",
      ],
    },
    recommendations: [
      {
        id: "1",
        questionId: "6",
        text: "Install hearing loops with working batteries",
        priority: "high",
        effort: "4-6 weeks",
      },
    ],
    category: "Building & Infrastructure",
  },
  {
    id: "7",
    text: "Does your building have designated quiet work areas / floors?",
    answer: "",
    comment: "",
    description: "",
    questionGuide: {
      image: "/assets/7.png",
      description:
        "These are spaces designated for use by those who need quiet or low noise.",
      otherOrganizations: [
        "/assets/sensory-space.png",
        "/assets/sensory-space.png",
        "/assets/sensory-space.png",
      ],
    },
    recommendations: [
      {
        id: "1",
        questionId: "7",
        text: "Install designated quiet work areas",
        priority: "high",
        effort: "4-6 weeks",
      },
    ],
    category: "Building & Infrastructure",
  },
  {
    id: "8",
    text: "Does your building have fully accessible bathrooms/toilets?",
    answer: "",
    comment: "",
    description: "",
    questionGuide: {
      image: "/assets/8.png",
      description:
        "These are toilet facilities that are usable to people who can't use the standard sized ones found in buildings. Typical features of these are: Wide doors - space sufficient for wheel chair to move through Low set equipment e.g. below waist level taps and dryers Motion sensor or easy to handle lighting Motion sensor or easy to handle taps or soap dispensers Automatic hand dryers Rails or Handles Emergency pull cords  - must reach floor.",
      otherOrganizations: [
        "/assets/sensory-space.png",
        "/assets/sensory-space.png",
        "/assets/sensory-space.png",
      ],
    },
    recommendations: [
      {
        id: "1",
        questionId: "8",
        text: "Install fully accessible bathrooms/toilets",
        priority: "high",
        effort: "4-6 weeks",
      },
    ],
    category: "Building & Infrastructure",
  },
  {
    id: "9",
    text: "When purchasing technology tools and products does your business base the selection of productivity tools also on accessibility considerations?",
    answer: "",
    comment: "",
    description: "",
    questionGuide: {
      image: "/assets/9.png",
      description:
        "The aim here is that when choosing work productivity tools e.g. MS office, GSuite, etc. that organisations are selecting tools that have accessibility features embedded in them so that clients, customers and employees with disabilities can also use these.",
      otherOrganizations: [
        "/assets/sensory-space.png",
        "/assets/sensory-space.png",
        "/assets/sensory-space.png",
      ],
    },
    recommendations: [
      {
        id: "1",
        questionId: "9",
        text: "Install fully accessible bathrooms/toilets",
        priority: "high",
        effort: "4-6 weeks",
      },
    ],
    category: "Equipment & Ergonomics",
  },
  {
    id: "10",
    text: "Does your business conduct accessibility equipment product checks - at least every 3 months?",
    answer: "",
    comment: "",
    description: "",
    questionGuide: {
      image: "/assets/10.png",
      description:
        "This entails making sure that any and all accessibility tools and equipment are always checked for fitness for use. e.g. check batteries in hearing loops.",
      otherOrganizations: [
        "/assets/sensory-space.png",
        "/assets/sensory-space.png",
        "/assets/sensory-space.png",
      ],
    },
    recommendations: [
      {
        id: "1",
        questionId: "10",
        text: "Install fully accessible bathrooms/toilets",
        priority: "high",
        effort: "4-6 weeks",
      },
    ],
    category: "Equipment & Ergonomics",
  },
  {
    id: "11",
    text: "Do you as a business offer your employees product training for new tools your procure?",
    answer: "",
    comment: "",
    description: "",
    questionGuide: {
      image: "/assets/11.png",
      description:
        "This entails making sure that any and all staff are trained to use the accessibility features, tools and equipment at work so that they can make accommodations when talking to cients, customers and other employees who may need accessibility features enabled during meetings. Examples include using the captioning featues on Zoom, the subtitles function in Powerpoint, enabling text to speech converters and record functions etc.",
      otherOrganizations: [
        "/assets/sensory-space.png",
        "/assets/sensory-space.png",
        "/assets/sensory-space.png",
      ],
    },
    recommendations: [
      {
        id: "1",
        questionId: "11",
        text: "Install fully accessible bathrooms/toilets",
        priority: "high",
        effort: "4-6 weeks",
      },
    ],
    category: "Equipment & Ergonomics",
  },
  {
    id: "12",
    text: "During new employee onboarding, do you offer Display Screen Equipment workstation asessment?",
    answer: "",
    comment: "",
    description: "",
    questionGuide: {
      image: "/assets/12.png",
      description:
        "This is a legally required assessment of risks associated with the use of Display Screen Equipment (DSE) such as computers, laptops, tablets, and other display screens at work.",
      otherOrganizations: [
        "/assets/sensory-space.png",
        "/assets/sensory-space.png",
        "/assets/sensory-space.png",
      ],
    },
    recommendations: [
      {
        id: "1",
        questionId: "12",
        text: "Install fully accessible bathrooms/toilets",
        priority: "high",
        effort: "4-6 weeks",
      },
    ],
    category: "Equipment & Ergonomics",
  },
  {
    id: "13",
    text: "In your offices, do you have fidget toys / autism or sensory comfort packs?",
    answer: "",
    comment: "",
    description: "",
    questionGuide: {
      image: "/assets/13.png",
      description:
        "These are packs of sensory toys and equipment specifically designed to help improve focus and provide stress relief through object engagement.",
      otherOrganizations: [
        "/assets/sensory-space.png",
        "/assets/sensory-space.png",
        "/assets/sensory-space.png",
      ],
    },
    recommendations: [
      {
        id: "1",
        questionId: "13",
        text: "Install fidget toys / autism or sensory comfort packs",
        priority: "high",
        effort: "4-6 weeks",
      },
    ],
    category: "Equipment & Ergonomics",
  },
  {
    id: "14",
    text: "Is there a mechanism at your workplace for employees to disclose ortheir disabilityies, e.g. a disability declaration/communication form, disability passpot etc.? These must be within a private and confidential context?",
    answer: "",
    comment: "",
    description: "",
    questionGuide: {
      image: "/assets/14.png",
      description:
        "This refers to any mechanism the employer has for those with disabilities to disclose their disability and request for support. It can take many forms, e.g. Disability passport / Accommodations request forms / Health adjustment passport.",
      otherOrganizations: [
        "/assets/sensory-space.png",
        "/assets/sensory-space.png",
        "/assets/sensory-space.png",
      ],
    },
    recommendations: [
      {
        id: "1",
        questionId: "14",
        text: "Install fully accessible bathrooms/toilets",
        priority: "high",
        effort: "4-6 weeks",
      },
    ],
    category: "Work culture & experience",
  },
  {
    id: "15",
    text: "In your organsiational budget, do you have a line item specifically allocated to DEI generically?",
    answer: "",
    comment: "",
    description: "",
    questionGuide: {
      image: "/assets/15.png",
      description:
        "This is a budget or portion of funds that is designated for use to build the DEI practices of the organisation. The budget can be for equipment, training or anything related to DEI.",
      otherOrganizations: [
        "/assets/sensory-space.png",
        "/assets/sensory-space.png",
        "/assets/sensory-space.png",
      ],
    },
    recommendations: [
      {
        id: "1",
        questionId: "15",
        text: "Install fully accessible bathrooms/toilets",
        priority: "high",
        effort: "4-6 weeks",
      },
    ],
    category: "Work culture & experience",
  },
  {
    id: "16",
    text: "Does your business have Enterprise Resource Groups specifically for disability?",
    answer: "",
    comment: "",
    description: "",
    questionGuide: {
      image: "/assets/16.png",
      description:
        "An ERG is a voluntary network of employees within a company that focuses on a specific demographic or affinity group. They are designed to help employees network and create a more inclusive workplace.",
      otherOrganizations: [
        "/assets/sensory-space.png",
        "/assets/sensory-space.png",
        "/assets/sensory-space.png",
      ],
    },
    recommendations: [
      {
        id: "1",
        questionId: "16",
        text: "Install fully accessible bathrooms/toilets",
        priority: "high",
        effort: "4-6 weeks",
      },
    ],
    category: "Work culture & experience",
  },
  {
    id: "17",
    text: "Does your HR/people/health and safety team have a readilly accessible list of disabled employees?",
    answer: "",
    comment: "",
    description: "",
    questionGuide: {
      image: "/assets/17.png",
      description:
        "Database of employees who have declared disabilities, (for HR) disclosure numbers.",
      otherOrganizations: [
        "/assets/sensory-space.png",
        "/assets/sensory-space.png",
        "/assets/sensory-space.png",
      ],
    },
    recommendations: [
      {
        id: "1",
        questionId: "17",
        text: "Install fully accessible bathrooms/toilets",
        priority: "high",
        effort: "4-6 weeks",
      },
    ],
    category: "Work culture & experience",
  },
  {
    id: "18",
    text: "Is there person in your leadership/executive team who is the DEI sponsor, advocate or champion?",
    answer: "",
    comment: "",
    description: "",
    questionGuide: {
      image: "/assets/18.png",
      description:
        "This is an identifiable leader in the organisation who has taken the mantle of being the ambassador or champion for any work or activities related to diversity, equity and inclusion.",
      otherOrganizations: [
        "/assets/sensory-space.png",
        "/assets/sensory-space.png",
        "/assets/sensory-space.png",
      ],
    },
    recommendations: [
      {
        id: "1",
        questionId: "18",
        text: "Install fully accessible bathrooms/toilets",
        priority: "high",
        effort: "4-6 weeks",
      },
    ],
    category: "Work culture & experience",
  },
  {
    id: "19",
    text: "Where the rest of your emploment and business policies are, do you also have and allow access to DEI policies - this is either on your intranet or internet?",
    answer: "",
    comment: "",
    description: "",
    questionGuide: {
      image: "/assets/19.png",
      description:
        "Can be any documentation that speaks to how DEI is managed and engaged in the organisation.",
      otherOrganizations: [
        "/assets/sensory-space.png",
        "/assets/sensory-space.png",
        "/assets/sensory-space.png",
      ],
    },
    recommendations: [
      {
        id: "1",
        questionId: "19",
        text: "Install fully accessible bathrooms/toilets",
        priority: "high",
        effort: "4-6 weeks",
      },
    ],
    category: "Work culture & experience",
  },
  {
    id: "20",
    text: "Do you ever conduct Accessibility Reviews / Audits - at least 1 annually?",
    answer: "",
    comment: "",
    description: "",
    questionGuide: {
      image: "/assets/20.png",
      description:
        "This is any audit, review, assessment or work done by the organisation whose intent is specifically to check the organisation's progress towards disability inclusion. An example is this work.",
      otherOrganizations: [
        "/assets/sensory-space.png",
        "/assets/sensory-space.png",
        "/assets/sensory-space.png",
      ],
    },
    recommendations: [
      {
        id: "1",
        questionId: "20",
        text: "Install fully accessible bathrooms/toilets",
        priority: "high",
        effort: "4-6 weeks",
      },
    ],
    category: "Work culture & experience",
  },
  {
    id: "21",
    text: "Is your business' website an Accessible website - WCA.G 2?",
    answer: "",
    comment: "",
    description: "",
    questionGuide: {
      image: "/assets/21.png",
      description:
        "WCAG compliance is the process of making digital content accessible to people with a range of disabilities, including those who are blind, deaf, hard of hearing, or have learning difficulties. WCAG stands for Web Content Accessibility Guidelines, and it's a set of internationally recognized recommendations that help ensure websites and apps are accessible to everyone.",
      otherOrganizations: [
        "/assets/sensory-space.png",
        "/assets/sensory-space.png",
        "/assets/sensory-space.png",
      ],
    },
    recommendations: [
      {
        id: "1",
        questionId: "21",
        text: "Install fully accessible bathrooms/toilets",
        priority: "high",
        effort: "4-6 weeks",
      },
    ],
    category: "Job, Role & Time Structure",
  },
  {
    id: "22",
    text: "During recruitment, do you offer interview accommodations and tailored onboarding?",
    answer: "",
    comment: "",
    description: "",
    questionGuide: {
      image: "/assets/22.png",
      description:
        "Also known as reasonable adjustments during interviews, these are changes made to the interview process that help candidates with disabilities or specific needs showcase their potential to the same level of equity as people without. The types of accommodations can be varied e.g. providing a BSL interpreter to deaf interviewees, pre-sharing interview questions to dysgrafia or ADHD, allowing 1 on 1 interviews for those with social anxiety, giving additional time to those with speech impairments or dyslexia",
      otherOrganizations: [
        "/assets/sensory-space.png",
        "/assets/sensory-space.png",
        "/assets/sensory-space.png",
      ],
    },
    recommendations: [
      {
        id: "1",
        questionId: "22",
        text: "Install fully accessible bathrooms/toilets",
        priority: "high",
        effort: "4-6 weeks",
      },
    ],
    category: "Job, Role & Time Structure",
  },
  {
    id: "23",
    text: "Do you as a business offer employees hybrid working options?",
    answer: "",
    comment: "",
    description: "",
    questionGuide: {
      image: "/assets/22.png",
      description:
        "This is any combination of work setup that allows for employees to work from spaces other than the office e.g. work from anywhere, work from home. This can be across any combination e.g. 1, 2 or 3 days of wfh vs 2 ,3 days in office. ",
      otherOrganizations: [
        "/assets/sensory-space.png",
        "/assets/sensory-space.png",
        "/assets/sensory-space.png",
      ],
    },
    recommendations: [
      {
        id: "1",
        questionId: "23",
        text: "Install fully accessible bathrooms/toilets",
        priority: "high",
        effort: "4-6 weeks",
      },
    ],
    category: "Job, Role & Time Structure",
  },
];

export const assessments: Assessment[] = [
  {
    id: 1,
    title: "Review 01",
    type: "Starter",
    date: "2024-11-20",
    tasksCompleted: 5,
    totalTasks: 10,
    status: "complete",
  },
  {
    id: 2,
    title: "Review 02",
    type: "Basic",
    date: "2024-10-05",
    tasksCompleted: 0,
    totalTasks: 10,
    status: "incomplete",
  },
  {
    id: 3,
    title: "Review 03",
    type: "Comprehensive",
    date: "2024-09-15",
    tasksCompleted: 3,
    totalTasks: 8,
    status: "incomplete",
  },
];

export const latestAssessmentData = [
  {
    title: "Building & Infrastructure",
    value: 10,
    total: 20,
  },
  {
    title: "Equipment & Ergonomics",
    value: 20,
    total: 20,
  },
  {
    title: "Work culture & experience",
    value: 10,
    total: 20,
  },
  {
    title: "Job, Role, Time Structure",
    value: 0,
    total: 20,
  },
];

export const latestTaskData = [
  {
    title: "To Do",
    value: 10,
    total: 20,
  },
  {
    title: "In Progress",
    value: 10,
    total: 20,
  },
  {
    title: "Completed",
    value: 20,
    total: 20,
  },
  {
    title: "Archived",
    value: 10,
    total: 20,
  },
];

export const chartData = [
  { browser: "chrome", visitors: 10, fill: "var(--color-chrome)" },
  { browser: "safari", visitors: 15, fill: "var(--color-safari)" },
  { browser: "firefox", visitors: 3, fill: "var(--color-firefox)" },
  { browser: "edge", visitors: 3, fill: "var(--color-edge)" },
];
export const chartConfig = {
  visitors: {
    label: "Visitors",
  },
  edge: {
    label: "To Do",
    color: "hsl(var(--chart-4))",
  },
  chrome: {
    label: "In Progress",
    color: "hsl(var(--chart-1))",
  },
  safari: {
    label: "Completed",
    color: "hsl(var(--chart-2))",
  },
  firefox: {
    label: "Archived",
    color: "hsl(var(--chart-3))",
  },
} satisfies ChartConfig;

export const comments = [
  {
    id: "1",
    username: "Name Surname",
    timestamp: "21 Sept 2024",
    content:
      "I believe we actually do have a WFH policy, not sure why this question is marked as NO. Kindly refer to the HR policy on section 6.",
    category: "Job, Role, Time Structure",
    questionNumber: 2,
  },
  {
    id: "2",
    username: "Name Surname",
    timestamp: "22 Sept 2024",
    content:
      "The board has decided that this provision will be cancelled from next month, so I think you should answer it as NO.",
    category: "Job, Role, Time Structure",
    questionNumber: 3,
  },
];

export const data: TaskData[] = [
  {
    id: "1",
    title: "Task 1",
    startDate: "2024-01-01",
    status: "To Do",
    category: "Building & Infrastructure",
    dueDate: "2024-01-01",
    assignedTo: "John Doe",
  },
  {
    id: "2",
    title: "Task 2",
    startDate: "2024-01-01",
    status: "In Progress",
    category: "Equipment & Ergonomics",
    dueDate: "2024-01-01",
    assignedTo: "John Doe",
  },
  {
    id: "3",
    title: "Task 3",
    startDate: "2024-01-01",
    status: "Archived",
    category: "Work culture & experience",
    dueDate: "2024-01-01",
    assignedTo: "John Doe",
  },
  {
    id: "4",
    title: "Task 4",
    startDate: "2024-01-01",
    status: "To Do",
    category: "Job, Role, Time Structure",
    dueDate: "2024-01-01",
    assignedTo: "John Doe",
  },
  {
    id: "5",
    title: "Task 5",
    startDate: "2024-01-01",
    status: "Completed",
    category: "Accommodation",
    dueDate: "2024-01-01",
    assignedTo: "Han Solo",
  },
];

export const iconMap: { [key: string]: React.ElementType } = {
  "Building & Infrastructure": Building,
  "Equipment & Ergonomics": Headphones,
  "Work culture & experience": Heart,
  "Job, Role & Time Structure": Hammer,
};
