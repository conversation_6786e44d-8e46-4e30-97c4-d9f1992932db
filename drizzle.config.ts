import "dotenv/config";
import { defineConfig } from "drizzle-kit";

// Define custom environment type
type Environment = "development" | "production" | "local" | undefined;

const env = (process.env.NODE_ENV as Environment) || "local";
const envFile =
  env === "production"
    ? ".env.production"
    : env === "development"
      ? ".env.development"
      : ".env.local";

// Add this to test the environment
console.log("Current environment:", env);
console.log("Using env file:", envFile);
console.log("Database URL:", process.env.DATABASE_URL);

// Load the appropriate .env file
require("dotenv").config({ path: envFile });

export default defineConfig({
  schema: "./src/server/drizzle/schema.ts",
  out: "./src/server/drizzle/migrations",
  dialect: "postgresql",
  strict: true,
  verbose: true,
  dbCredentials: {
    url: process.env.DATABASE_URL as string,
  },
});
